{"bad_request": {"message": "Request kh<PERSON>ng h<PERSON><PERSON> l<PERSON>.", "code": 400}, "unauthorized": {"message": "JWT không hợp hoặc đã hết hạn, vui lòng đăng nhập lại.", "code": 401}, "not_allowed": {"message": "bạn không thể thực hi<PERSON>, vui lòng đăng nhập lại trướ<PERSON> khi thực thi api.", "code": 405}, "not_found": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mục nào", "code": 404}, "internal_server_error": {"message": "có lỗi phát sinh trong server.", "code": 500}, "validate_error": {"message": "<PERSON><PERSON><PERSON> thực không thành công.", "code": 412}, "lang_not_support_error": {"message": "<PERSON><PERSON> thống chỉ hỗ trợ với ngôn ngữ (vi,en).", "code": 100}, "must_not_empty": {"message": "%s phải không rỗng.", "code": 101}, "not_exist": {"message": "%s [%s] không tồn tại trong hệ thống.", "code": 102}, "already_exist": {"message": "%s [%s] đã tồn tại trong hệ thống.", "code": 103}, "message_success": {"message": "request thành công.", "code": 200}, "website_url_not_found": {"message": "Không tìm thấy website url.", "code": 200}, "register_tracking_landing_page_error": {"message": "Lỗi đăng ký tracking landing page.", "code": 200}, "script_not_found": {"message": "Không tìm thấy script.", "code": 200}, "invalid_type": {"message": "Type kh<PERSON>ng hợp lệ.", "code": 200}, "config_not_found": {"message": "<PERSON><PERSON><PERSON> hình không tồn tại.", "code": 413}, "config_ids_not_found": {"message": "<PERSON><PERSON><PERSON> hình không tồn tại.", "code": 200}, "website_url_exist": {"message": "Website url đã tồn tại.", "code": 200}, "not_permission_update_website_url_error": {"message": "Website url không đ<PERSON><PERSON><PERSON> c<PERSON> nh<PERSON>.", "code": 413}, "update_tracking_status_error": {"message": "cập nhật trạng thái tracking.", "code": 413}, "connect_database": {"message": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đư<PERSON> tới host và port.", "title": "<PERSON><PERSON><PERSON> n<PERSON> đ<PERSON> database"}, "authorization": {"message": "Username hoặc Password không chính xác.", "title": "Authorization"}, "database_not_exist": {"message": "Database không tồn tại.", "title": "Database"}, "schema_not_exist": {"message": "<PERSON><PERSON><PERSON> không tồn tại.", "title": "<PERSON><PERSON><PERSON>"}, "not_permission_read_database": {"message": "<PERSON><PERSON><PERSON><PERSON> có quyền đọc dữ liệu.", "title": "Permission"}, "field_verify_not_mapping_one": {"message": "Bạn cần mapping 01 trong các field đ<PERSON><PERSON><PERSON> xác thực.", "code": 413}, "update_status_connect_failed": {"message": "<PERSON><PERSON><PERSON> nhật trạng thái kết nối thất bại.", "code": 413}, "not_on_sync_data": {"message": "<PERSON><PERSON><PERSON><PERSON> thể bật sync data với cấu hình connector này.", "code": 413}, "delete_connector_fail_by_connector_in_running": {"message": "Xoá connector không thành công vì có connector đang ở trạng thái chạy.", "code": 413}, "information_connect_config_fail": {"message": "<PERSON>ết nối không thành công. <PERSON><PERSON> lòng kiểm tra lại thông tin kết nối.", "code": 413}, "configuration_limit": {"message": "Bạn không đư<PERSON><PERSON> phép tạo mới cấu hình tích hợp sms do vượt quá hạn mức cấu hình (10 cấu hình/provider).", "code": 413}, "configuration_email_limit": {"message": "<PERSON><PERSON><PERSON><PERSON> thể thêm mới do số lượng cấu hình gửi E-mail tích hợp với nhà cung cấp này đã đạt mức tối đa 10 cấu hình.", "code": 413}, "sms_config_exists": {"message": "<PERSON><PERSON><PERSON> hình tích hợp SMS đã tồn tại trên hệ thống.", "code": 409}, "email_config_exists": {"message": "Domain tích hợp qua provider %s đã tồn tại trên hệ thống. Vui lòng nhập domain khác.", "code": 409}, "provider_not_found": {"message": "Provider <PERSON><PERSON><PERSON><PERSON> tồn tại.", "code": 404}, "need_stop_connect_change_mode": {"message": "Bạn cần ngắt kết nối trước khi chuyển cơ chế đồng bộ sang snapshot trong cấu hình cài đặt.", "code": 413}, "app_use_in_connector": {"message": "Ứng dụng đang được sử dụng trong connector", "code": 413}, "status_code_check_connect_webhooks": {"title": "Mã: {status_code}"}, "app_name_exists": {"message": "Tên <PERSON>ng dụng {app_name} đã tồn tại trên hệ thống.", "code": 413}, "app_not_exists": {"message": "application_deleted", "code": 413}, "connector_name_exist_data_in": {"message": "Tên connector đã tồn tại trên hệ thống.", "code": 413}, "connector_name_exist_data_out": {"message": "Tê<PERSON> đích đến đã tồn tại trên hệ thống.", "code": 413}, "app_not_exists_set_app_new": {"message": "Application đượ<PERSON> khai báo đã bị xoá. <PERSON><PERSON> lòng cấu hình <PERSON> k<PERSON>, để thực hiện gửi thử nghiệm.", "code": 413}, "delete_integration_account_fail": {"message": "Gỡ tích hợp tài khoản không thành công!", "code": 413}, "spreadsheet_not_exist": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang tính.", "code": 413}, "table_not_found_primary_key": {"message": "Bảng dữ liệu cần tồn tại field là kho<PERSON> ch<PERSON>.", "code": 413}, "not_allowed_add_action": {"message": "Thêm mới giá trị chỉ hỗ trợ mapping tới các trường thông tin dạng mảng (array)", "code": 413}, "streaming_must_have_primary_key": {"message": "<PERSON><PERSON>u hình không thành công do bảng dữ liệu nguồn thiếu thông tin khóa chính để bắt đầu tiến trình streaming", "code": 413}, "domain_not_found": {"message": "Thông tin domain không được để trống", "code": 413}, "domain_duplicate": {"message": "Danh sách domain đang trùng nhau. <PERSON><PERSON> lòng kiểm tra lại", "code": 413}, "aws_config_exists": {"message": "Tài khoản AWS tích hợp qua provider %s đã tồn tại trên hệ thống. <PERSON><PERSON> lòng nhập tài khoản kh<PERSON>.", "code": 409}, "upsert_error": {"message": "Upsert dữ liệu không thành công.", "code": 409}, "max_domain_notification": {"message": "Chỉ đư<PERSON><PERSON> phép khai báo tối đa 1 domain cho mục đích sử dụng thông báo.", "code": 413}, "multiple_domains_required_to_remove": {"message": "Số lượng domain cấu hình trong tài khoản nhiều hơn 1 mới được phép gỡ domain", "code": 413}, "cannot_retrieve_data_sample": {"message": "<PERSON><PERSON><PERSON><PERSON> thể truy xuất mẫu dữ liệu: ", "code": 413}, "domain_exist": {"message": "Domain đã tồn tại trên hệ thống. V<PERSON> lòng nhập domain khác.", "code": 409}}