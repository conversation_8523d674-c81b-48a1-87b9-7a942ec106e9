{"bad_request": {"message": "Bad request.", "code": 400}, "unauthorized": {"message": "JWT is invalid or is expired. Please login again.", "code": 401}, "not_allowed": {"message": "you can't execute, please login before execution this api.", "code": 405}, "not_found": {"message": "Item is not found.", "code": 404}, "internal_server_error": {"message": "There is an internal server error.", "code": 500}, "validate_error": {"message": "Validation failed.", "code": 412}, "lang_not_support_error": {"message": "system only support for language (vi,en).", "code": 100}, "must_not_empty": {"message": "%s must not empty.", "code": 101}, "not_exist": {"message": "%s [%s] not exist in system.", "code": 102}, "already_exist": {"message": "%s [%s] already exist in system.", "code": 103}, "message_success": {"message": "request successful.", "code": 200}, "website_url_not_found": {"message": "website_url not found.", "code": 200}, "register_tracking_landing_page_error": {"message": "register tracking landing page error.", "code": 200}, "script_not_found": {"message": "script not found.", "code": 200}, "invalid_type": {"message": "invalid type.", "code": 200}, "config_not_found": {"message": "config not found.", "code": 200}, "config_ids_not_found": {"message": "config ids not found.", "code": 200}, "website_url_exist": {"message": "website url exist.", "code": 200}, "not_permission_update_website_url_error": {"message": "Website url not updated.", "code": 413}, "update_tracking_status_error": {"message": "update tracking status error.", "code": 413}, "connect_database": {"message": "Unable to connect to host and port.", "title": "Connect to database"}, "authorization": {"message": "Incorrect Username or Password.", "title": "Authorization"}, "database_not_exist": {"message": "The database does not exist.", "title": "Database"}, "not_permission_read_database": {"message": "No permission to read data.", "title": "Permission"}, "field_verify_not_mapping_one": {"message": "You need to map at least 01 field for verification.", "code": 413}, "update_status_connect_failed": {"message": "Update status connect failed.", "code": 413}, "not_on_sync_data": {"message": "Unable to enable data sync with this connector configuration.", "code": 413}, "delete_connector_fail_by_connector_in_running": {"message": "Deleting the connector failed because there was a connector in running state.", "code": 413}, "information_connect_config_fail": {"message": "The connection failed. Please double-check the connection information.", "code": 413}, "configuration_limit": {"message": "You are not allowed to create new sms integration configurations due to exceeding the configuration limit (10 configurations/provider).", "code": 413}, "configuration_email_limit": {"message": "Cannot add new ones because the number of E-mail sending profiles integrated with this provider has reached the maximum of 10 profiles.", "code": 413}, "sms_config_exists": {"message": "SMS integration configuration already exists on the system.", "code": 409}, "email_config_exists": {"message": "The domain integrated through provider %s already exists on the system. Please enter another domain.", "code": 409}, "need_stop_connect_change_mode": {"message": "You need to disconnect before switching the sync mechanism to snapshot in the settings configuration.", "code": 413}, "app_use_in_connector": {"message": "App being used in the connector", "code": 413}, "status_code_check_connect_webhooks": {"title": "Code: {status_code}"}, "app_name_exists": {"message": "App name {} already exists on the system.", "code": 413}, "app_not_exists": {"message": "application_deleted", "code": 413}, "connector_name_exist_data_in": {"message": "Connector name already exists on the system.", "code": 413}, "connector_name_exist_data_out": {"message": "Destination name already exists on the system.", "code": 413}, "app_not_exists_set_app_new": {"message": "The declared application has been deleted. Please configure another application, to perform test sending.", "code": 413}, "delete_integration_account_fail": {"message": "Delete integration account failed. Please configure another integration account.", "code": 413}, "spreadsheet_not_exist": {"message": "Spreadsheet not exists!", "code": 413}, "table_not_found_primary_key": {"message": "Table not exists primary key.", "code": 413}, "domain_not_found": {"message": "Domain not found.", "code": 413}, "domain_duplicate": {"message": "List domain is duplicated. Please check again.", "code": 413}, "aws_config_exists": {"message": "Account AWS integrated through provider %s already exists on the system. Please enter another account.", "code": 409}, "upsert_error": {"message": "Upsert data failed", "code": 413}, "max_domain_notification": {"message": "Only 1 domain can be declared for notification purposes.", "code": 413}, "not_allowed_add_action": {"message": "The 'Add new value' mode can only map to array fields.", "code": 413}, "streaming_must_have_primary_key": {"message": "Source table needs a primary key to initiate streaming.", "code": 413}, "multiple_domains_required_to_remove": {"message": "Removing a domain is only allowed when more than one domain is configured in the account.", "code": 413}, "cannot_retrieve_data_sample": {"message": "Cannot retrieve data sample: ", "code": 413}, "domain_exist": {"message": "Domain already exists on the system. Please enter another domain.", "code": 409}}