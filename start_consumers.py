import os
import sys

from configs.kafka_config import CONSUMER_GROUP, KAFKA_TOPIC

if __name__ == "__main__":
    name_queue = sys.argv[1]
    topic_name = os.environ.get("kafka_topics")
    group_id = os.environ.get("kafka_consumer_group_name")

    if name_queue == "handle-data-history":
        from src.consumers.handle_data_history_consumer import HandleDataHistoryConsumer

        consumer = HandleDataHistoryConsumer(topic_name=topic_name, group_id=group_id)

    if name_queue == "handle-sync-orchestration":
        from src.consumers.sync_data_connector_to_orchestration_consumer import (
            SyncDataConnectorToOrchestrationConsumer,
        )

        consumer = SyncDataConnectorToOrchestrationConsumer(topic_name=topic_name, group_id=group_id)

    elif name_queue == KAFKA_TOPIC.MARKETPLACE_PROCESS_OUT_DATA:
        from src.consumers.process_data_out_consumer import ProcessDataOutConsumer
        consumer = ProcessDataOutConsumer(topic_name=topic_name, group_id=group_id)


    elif name_queue == KAFKA_TOPIC.UPSERT_EMAIL_NM:
        from src.consumers.upsert_provider_config_nm_consumer import UpsertProviderConfigNotifiManagement
        consumer = UpsertProviderConfigNotifiManagement(topic_name=topic_name, group_id=group_id)