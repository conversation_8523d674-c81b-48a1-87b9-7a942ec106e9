#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 12/12/2024
"""

from src.common import CommonKeys, ProviderConfigKeys
from src.internal_module.notify_management import NotifyManagementHelper
from src.models.provider_model import ProviderModel

if __name__ == "__main__":
    provider_type = 212
    provider_model: ProviderModel = ProviderModel()
    notify_management_helper: NotifyManagementHelper = NotifyManagementHelper()
    detail = provider_model.find_one({"provider_type": provider_type})
    if detail:
        connect_config_info = detail.get("connect_config_info")
        if connect_config_info:
            for item in connect_config_info:
                field = item.get("field")
                if field == "provider_api":
                    item["value"] = "https://momailer.mobio.io/api/v1.0/send-email"
                if field == "auth_pass":
                    item["value"] = "YnZiYW5rOlluWmlZVzVyT2pGUUlTY3lkemx1V1Z4YlQzeFVZVVU9"
            print(
                provider_model.update_one_query(
                    {"provider_type": provider_type}, {"connect_config_info": connect_config_info}
                )
            )

        domains = ["uat-news.bvbank.net.vn", "uat-care.bvbank.net.vn", "uat-promotion.bvbank.net.vn"]
        for domain in domains:

            payload_send = {
                ProviderConfigKeys.AUTH_NAME: "",
                ProviderConfigKeys.AUTH_PASS: "",
                ProviderConfigKeys.PROVIDER_API: "",
                ProviderConfigKeys.AUTH_ATTACHMENT: domain,
                ProviderConfigKeys.PROVIDER_TYPE: provider_type,
                ProviderConfigKeys.STATUS: 1,
                ProviderConfigKeys.OTHERS: {},
            }

            for config in connect_config_info:
                payload_send[config.get(ProviderConfigKeys.ConfigInfoKeys.FIELD)] = config.get(
                    ProviderConfigKeys.ConfigInfoKeys.VALUE
                )
            print("payload_send :: {}".format(payload_send))
            notify_management_helper.upsert_provider_config(detail.get(CommonKeys.MERCHANT_ID), payload_send)
