
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel


def add_config_snapshot_connector():
    filter_option = {
    }
    token_query = None
    while True:
        connectors, token_query = ConfigConnectorsModel().find_paginate_load_more(filter_option, per_page=500, after_token=token_query)
        snapshot_config = {
            "snapshot_type": "full",
            "checkpoint": {
                "field_name": "",
                "field_type": "",
                "option": {
                    "option_type": "",
                    "checkpoint_value": ""
                }
            },
            "partition": 10000,
            "is_use_custom_checkpoint": False,
            "is_edit_checkpoint": False
        }
        for connector in connectors:
            config_sync_calendar = connector.get("config_sync_calendar")
            if config_sync_calendar and "snapshot_config" not in config_sync_calendar:
                mode = config_sync_calendar.get("mode")
                if mode == "snapshot":
                    ConfigConnectorsModel().update_by_set({"_id": connector["_id"]}, {"config_sync_calendar.snapshot_config": snapshot_config })
        if not connectors:
            break
        
if __name__ == "__main__":
    add_config_snapshot_connector()
    
