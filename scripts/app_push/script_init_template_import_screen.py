#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 27/06/2025
"""

import datetime

from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from configs import MarketPlaceApplicationConfig, RedisConfig
from src.common import WORKING_DIR
from src.models.file_upload_model import FileUploadModel, TypeFileUpload

MobioMediaSDK().config(
    redis_uri=RedisConfig.REDIS_URI,
    admin_host=MarketPlaceApplicationConfig.ADMIN_HOST,
    cache_prefix=RedisConfig.CACHE_PREFIX,
)

if __name__ == "__main__":
    file_path = WORKING_DIR + "/resources/templates/import_screen/Maufile_danhsachmanhinh.xlsx"
    file_info = MobioMediaSDK().upload_with_kafka(merchant_id="mobio", file_path=file_path, expire=None)
    filter_option = {
        "type": TypeFileUpload.TEMPLATE_IMPORT_SCREEN,
        "merchant_id": "mobio",
    }
    data_upsert = {
        "type": TypeFileUpload.TEMPLATE_IMPORT_SCREEN,
        "merchant_id": "mobio",
        "url": file_info.get("url"),
        "filename": "Template Import Screen",
        "format": file_info.get("format"),
        "capacity": file_info.get("capacity"),
        "created_time": datetime.datetime.now(datetime.timezone.utc),
        "created_by": "mobio",
    }
    FileUploadModel().upsert(filter_option, data_upsert)
    print("Done")
