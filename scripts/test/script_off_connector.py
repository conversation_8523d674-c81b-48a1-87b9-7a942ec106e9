#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 19/11/2024
"""

from mobio.libs.logging import MobioLogging
from src.internal_module.orchestration import OrchestrationApiHelper
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.log_sync_data_to_module_other_model import LogSyncDataToModuleOtherModel

if __name__ == "__main__":
    connector_configs = ConfigConnectorsModel().find({"status_connect": "on"}, {"merchant_id": 1, "_id": 1})
    for connector_config in connector_configs:
        connector_id = connector_config["_id"]
        merchant_id = connector_config["merchant_id"]
        orchestration_id = LogSyncDataToModuleOtherModel().get_orchestration_id_by_connector_id(
            merchant_id=merchant_id, connector_id=connector_id
        )
        MobioLogging().debug("script_off_connector :: orchestration_id :: {}".format(orchestration_id))
        off_pipeline = OrchestrationApiHelper().stop_pipeline(merchant_id, orchestration_id)
        MobioLogging().debug("script_off_connector :: off_pipeline :: {}".format(off_pipeline))
