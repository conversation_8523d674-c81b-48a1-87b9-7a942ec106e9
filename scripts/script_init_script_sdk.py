#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 05/07/2024
"""

from datetime import datetime

from src.models.sdk_script_model import SDKScriptModel

if __name__ == "__main__":
    text_script = """
    <script>!function(b,c,e,f,g){var h=b.mo=b.mo||{},a=c.createElement('script');a.type='text/javascript',a.async=!0,a.defer=!0,a.onload=()=>{h.init||(h=b.mo=b.mo||{}),h.init({code:f,source:g})},a.src=e;var d=c.getElementsByTagName('script')[0];d.parentNode.insertBefore(a,d)}(window,document,'https://resources.mobio.vn/content/js/test/v2/mo.sdk.min.js','#code','#merchant_id');</script>
    """

    if not SDKScriptModel().find_one({"merchant_id": "DEFAULT"}):
        SDKScriptModel().insert_document(
            {"script": text_script, "merchant_id": "DEFAULT", "created_time": datetime.utcnow()}
        )
    else:
        SDKScriptModel().update_by_set(
            {"merchant_id": "DEFAULT"},
            {"script": text_script, "merchant_id": "DEFAULT", "created_time": datetime.utcnow()},
            upsert=True,
        )
    print("DONE")
