# Đ<PERSON>ng bộ pipeline_name từ bảng log_sync_data_to_module_other về bảng config_connectors

from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.log_sync_data_to_module_other_model import LogSyncDataToModuleOtherModel


def add_pipeline_name_connector():
    filter_option = {
        "module_sync": "orchestration"
    }
    token_query = None
    while True:
        log_sync_orchestration, token_query = LogSyncDataToModuleOtherModel().find_paginate_load_more(filter_option, per_page=500, after_token=token_query)
        for log in log_sync_orchestration:
            orchestration_connector_name = (
                        log.get("module_data", {}).get("connector_name") if log else None
                    )      
            connector_id = log.get("connector_id")
            if orchestration_connector_name:
                ConfigConnectorsModel().update_by_set({"_id": connector_id}, {"pipeline_name": orchestration_connector_name}) 
        if not log_sync_orchestration:
            break
    print("========Done========")
 
if __name__ == "__main__":
    add_pipeline_name_connector()