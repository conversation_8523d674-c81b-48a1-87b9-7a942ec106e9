#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 21/01/2025
"""
import sys

from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.orchestration.pipeline_session_model import PipelineSessionModel


def get_status_pipeline_connector(connector_id):
    if not connector_id:
        print("Connector ID is required")
        exit(1)
    filter_data = {}
    if connector_id:
        filter_data["connector_id"] = int(connector_id)
    list_pipeline_by_connector = (
        PipelineSessionModel()
        .find(
            filter_data,
            {
                "connector_id": 1,
                "session_id": 1,
                "consume_status": 1,
                "process_status": 1,
            },
        )
        .sort({"_id": -1})
        .limit(1)
    )

    for pipeline_item in list_pipeline_by_connector:
        return pipeline_item.get("process_status")
    return None


if __name__ == "__main__":
    connector_id = sys.argv[1] if len(sys.argv) > 1 else None
    status_pipeline = get_status_pipeline_connector(connector_id)
    print("Status pipeline of connector {}: {}".format(connector_id, status_pipeline))
    ConfigConnectorsModel().update_one_query({"_id": int(connector_id)}, {"status_pipeline": status_pipeline})
