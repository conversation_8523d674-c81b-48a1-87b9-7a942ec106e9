#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 02/06/2025
"""
from bson import ObjectId
from pymongo import UpdateOne

from src.internal_module.admin import AdminInternal
from src.models.log_action import LogActionModel


def migrate_data_old_log_action():
    next_token = None
    per_page = 20
    filter_option = {
        "username": {"$exists": False}
    }
    while True:
        lst_query_update = []
        lst_test = []
        log_actions, next_token = LogActionModel().find_paginate_load_more(
            filter_option, per_page, next_token
        )
        for log_action in log_actions:
            data_admin = AdminInternal().get_detail_user_information_by_account_id(
                log_action.get("merchant_id"),
                log_action.get("created_by")
            )
            if not data_admin:
                continue
            log_action_id = log_action.get("_id")
            data_update = {
                "username": data_admin.get("username"),
                "fullname": data_admin.get("fullname"),
                "email": data_admin.get("email"),
            }
            lst_query_update.append(
                UpdateOne(
                    {"_id": log_action_id},
                    {"$set": data_update}
                )
            )
            lst_test.append(log_action_id)
        if lst_query_update:
            LogActionModel().collector().bulk_write(lst_query_update)
        if not next_token:
            break

if __name__ == "__main__":
    migrate_data_old_log_action()
