#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/09/2024
"""

from mobio.libs.run_script import MobioRunScript

"""
- cấu trúc gồm  
  - every_deploy: các script luôn luôn chạy mỗi khi lên phiên bản mới, khối script này sẽ chạy đầu tiên, các script trong khối được chạy tuần tự. 
  - version: các script chạy theo version, số version là kiểu int tăng dần, script sẽ chạy version từ thấp đến cao, trong mỗi version có nhiều script sẽ chạy tuần tự. các script tồn tại trong file init_script.result sẽ không chạy lại nữa thư viện sẽ tìm version chưa chạy để chạy tiếp.
"""

VERSION_CONFIG = {
    "every_deploy": {
        "script": [
            "python3.11 -u ensure_indexes_mongo.py",
            "python3.11 -u ensure_kafka_topic.py",
            "PYTHONPATH=./ python3.11 scripts/script_init_script_sdk.py",
            "PYTHONPATH=./ python3.11 scripts/script_add_config_firebase_default.py",
            "PYTHONPATH=./ python3.11 scripts/script_init_file_download_sdk.py",
            "PYTHONPATH=./ python3.11 scripts/script_init_data_data_flow.py",
            "PYTHONPATH=./ python3.11 scripts/script_update_event_data_out.py",
            "PYTHONPATH=./ python3.11 scripts/script_init_object_handle.py",
            "PYTHONPATH=./ python3.11 scripts/script_init_data_example_data_in_server_api.py",
            "PYTHONPATH=./ python3.11 scripts/script_init_setting_add_on_connector.py",
            "PYTHONPATH=./ python3.11 scripts/add_connector/script_add_connector_microsoft_excel.py",
        ]
    },
    "version": {
        # 1: [  # 4.24
        # ],
        1: [
            "PYTHONPATH=./ python3.11 scripts/script_init_setting_add_on_connector.py",
        ],
        2: [
            "PYTHONPATH=./ python3.11 scripts/script_init_setting_add_on_connector.py",
        ],
        3: [
            "PYTHONPATH=./ python3.11 scripts/migrate/reports/script_migrate_df_report_to_daily_report.py",
            "PYTHONPATH=./ python3.11 scripts/script_convert_status_sync.py",
        ],
        4: [
            "PYTHONPATH=./ python3.11 scripts/v4dot44/script_add_status_connect_to_connector.py",
            "PYTHONPATH=./ python3.11 scripts/script_add_region_aws_ses.py",
            "PYTHONPATH=./ python3.11 scripts/script_init_provider_info.py",
        ],
        5: 
        [
            "PYTHONPATH=./ python3.11 scripts/v4dot45/script_add_config_transform_helper_dlvn.py DLVN",
            "PYTHONPATH=./ python3.11 scripts/migrate/log_action/script_migrate_data_old_log_action.py",
        ],
        6: [
            "PYTHONPATH=./ python3.11 scripts/app_push/script_init_template_import_screen.py",
        ],
    },
}

if __name__ == "__main__":
    MobioRunScript().run_script_by_version(VERSION_CONFIG)
