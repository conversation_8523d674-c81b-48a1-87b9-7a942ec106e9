#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os

from mobio.sdks.base.configs import ApplicationConfig


class MarketPlaceApplicationConfig(ApplicationConfig):
    NAME = "MarketPlace"

    ApplicationConfig.WORKING_DIR = str(os.environ.get("MARKET_PLACE_HOME"))
    ApplicationConfig.RESOURCE_DIR = os.path.join(ApplicationConfig.WORKING_DIR, "resources")
    ApplicationConfig.CONFIG_DIR = os.path.join(ApplicationConfig.RESOURCE_DIR, "configs")
    ApplicationConfig.LANG_DIR = os.path.join(ApplicationConfig.RESOURCE_DIR, "lang")

    ApplicationConfig.CONFIG_FILE_PATH = os.path.join(ApplicationConfig.CONFIG_DIR, "market_place.conf")
    ApplicationConfig.LOG_CONFIG_FILE_PATH = os.path.join(ApplicationConfig.CONFIG_DIR, "logging.conf")
    ApplicationConfig.LOG_FILE_PATH = os.path.join(ApplicationConfig.APPLICATION_LOGS_DIR)

    MARKET_PLACE_FOLDER_NAME = os.environ.get("MARKET_PLACE_FOLDER_NAME")

    ADMIN_HOST = os.environ.get("ADMIN_HOST", "")
    YEK_REWOP = os.environ.get("YEK_REWOP")
    CEM_HOST = os.environ.get("CEM_HOST", "")


class RedisConfig:
    REDIS_URI = os.environ.get("REDIS_URI", "redis://redis-server:6379/0")
    REDIS_CLUSTER_URI = os.environ.get("REDIS_CLUSTER_URI")
    REDIS_TYPE = os.environ.get("REDIS_TYPE")
    CACHE_PREFIX = "market_place_cache_"


class MongoConfig:
    MONGO_URI = os.environ.get("MARKET_PLACE_MONGO_URI")
    DATA_ORCHESTRATION_MONGODB_URI = os.environ.get("DATA_ORCHESTRATION_MONGODB_URI")


class OlapConfig:
    OLAP_URI = os.environ.get("OLAP_VIEW_USER_STARROCKS_URI")


class LoggingConfig:
    K8S = bool(int(os.environ.get("K8S", "0")))
    ALLOW_LOG_K8S = "1"


class KafkaReplication:
    DEFAULT_BROKER_ID_ASSIGN = "DEFAULT_BROKER_ID_ASSIGN"
