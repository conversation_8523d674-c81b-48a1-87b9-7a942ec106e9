apiVersion: apps/v1
kind: Deployment
metadata:
  name: survey-sch-send-email-report-month-deployment
  labels:
    app: survey-sch-send-email-report-month
spec:
  replicas: 1
  selector:
    matchLabels:
      app: survey-sch-send-email-report-month
  template:
    metadata:
      labels:
        app: survey-sch-send-email-report-month
    spec:
      serviceAccountName: mobio
      containers:
        - name: survey
          image: {image}
          command: ["/bin/sh", "-c"]
          args:
            [
              "cd $SURVEY_HOME; sh prepare_env.sh && python3.11 -u start_schedulers.py aggregate_month",
            ]
          resources:
            requests:
              memory: 30Mi
              cpu: 40m
            limits:
              memory: 1Gi
              cpu: 500m
          envFrom:
            - configMapRef:
                name: mobio-config
            - secretRef:
                name: mobio-secret
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            - name: mobio-public-shared-data
              mountPath: /media/data/public_resources/
      initContainers:
        - name: init-survey
          image: {image}
          command:
            ["/bin/sh", "-c", "cd $SURVEY_HOME; sh prepare_env.sh && sh check_image.sh"]
          envFrom:
            - configMapRef:
                name: mobio-config
            - secretRef:
                name: mobio-secret
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            - name: mobio-public-shared-data
              mountPath: /media/data/public_resources/
      imagePullSecrets:
        - name: registrypullsecret
      volumes:
        - name: mobio-shared-data
          persistentVolumeClaim:
            claimName: mobio-resources-pvc
        - name: mobio-public-shared-data
          persistentVolumeClaim:
            claimName: mobio-public-resources-pvc
