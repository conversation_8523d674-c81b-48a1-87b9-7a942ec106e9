apiVersion: apps/v1
kind: Deployment
metadata:
  name: marketplace-csm-data-history
  labels:
    app: marketplace-csm-data-history
spec:
  replicas: 1
  selector:
    matchLabels:
      app: marketplace-csm-data-history
  template:
    metadata:
      labels:
        app: marketplace-csm-data-history
    spec:
      serviceAccountName: mobio
      containers:
        - name: market-place
          image: {image}
          imagePullPolicy: IfNotPresent
          command: [ "/bin/sh", "-c" ]
          args: ["touch /media/data/resources/kafka-liveness-pod/$HOSTNAME; cd $MARKET_PLACE_HOME; sh prepare_env.sh && python3.11 -u start_consumers.py handle-data-history"]
          resources:
            requests:
              memory: 30Mi
              cpu: 40m
            limits:
              memory: 1Gi
              cpu: 500m
          envFrom:
            - configMapRef:
                name: mobio-config
            - secretRef:
                name: mobio-secret
          env:
            - name: kafka_topics
              value: "market-place-change-data"
            - name: kafka_consumer_group_name
              value: "handle-data-history"
            - name: kafka_topic_retry
              value: ""
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            - name: mobio-public-shared-data
              mountPath: /media/data/public_resources/
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - "cat /media/data/resources/kafka-liveness-pod/$HOSTNAME"
            timeoutSeconds: 30
            periodSeconds: 30
            initialDelaySeconds: 300
            failureThreshold: 1
      initContainers:
        - name: init-market-place
          image: {image}
          command: ['/bin/sh', '-c', "cd $MARKET_PLACE_HOME; sh prepare_env.sh && sh check_image.sh"]
          envFrom:
            - configMapRef:
                name: mobio-config
            - secretRef:
                name: mobio-secret
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            - name: mobio-public-shared-data
              mountPath: /media/data/public_resources/
      imagePullSecrets:
        - name: registrypullsecret
      volumes:
        - name: mobio-shared-data
          persistentVolumeClaim:
            claimName: mobio-resources-pvc
        - name: mobio-public-shared-data
          persistentVolumeClaim:
            claimName: mobio-public-resources-pvc