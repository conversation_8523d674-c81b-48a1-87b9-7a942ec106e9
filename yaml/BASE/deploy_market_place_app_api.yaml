apiVersion: apps/v1
kind: Deployment
metadata:
 name: marketplace-api
 labels:
   app: marketplace-api
spec:
 replicas: 1
 selector:
   matchLabels:
     app: marketplace-api
 template:
   metadata:
     labels:
       app: marketplace-api
   spec:
     serviceAccountName: mobio
     nodeSelector:
        workertype: api
     tolerations:
      - key: workertype
        operator: Equal
        value: api
     containers:
       - name: market-place
         image: {image}
         imagePullPolicy: IfNotPresent
         command: ["/bin/sh", "-c"]
         args: ["cd $MARKET_PLACE_HOME; sh prepare_env.sh && uwsgi --http :80 --wsgi-file app_market_place_api.py --callable app --master --processes 4 -b 65536 --lazy --enable-threads"]
         envFrom:
           - configMapRef:
               name: mobio-config
           - secretRef:
               name: mobio-secret
         ports:
           - containerPort: 80
         resources:
           requests:
             memory: 300Mi
             cpu: 40m
           limits:
             memory: 1Gi
             cpu: 800m
         volumeMounts:
           - name: mobio-shared-data
             mountPath: /media/data/resources/
           - name: mobio-public-shared-data
             mountPath: /media/data/public_resources/
         startupProbe:
           tcpSocket:
             port: 80
           initialDelaySeconds: 30
           periodSeconds: 10
         readinessProbe:
           httpGet:
             port: 80
             path: /api/v1.0/ping
           initialDelaySeconds: 60
           periodSeconds: 30
         livenessProbe:
           httpGet:
             port: 80
             path: /api/v1.0/ping
           initialDelaySeconds: 120
           periodSeconds: 5
           timeoutSeconds: 4
           successThreshold: 1
           failureThreshold: 3
     initContainers:
       - name: init-market-place
         image: {image}
         command: ['/bin/sh', '-c', "cd $MARKET_PLACE_HOME; sh prepare_env.sh && sh check_image.sh"]
         envFrom:
           - configMapRef:
               name: mobio-config
           - secretRef:
               name: mobio-secret
         volumeMounts:
           - name: mobio-shared-data
             mountPath: /media/data/resources/
           - name: mobio-public-shared-data
             mountPath: /media/data/public_resources/
     imagePullSecrets:
       - name: registrypullsecret
     volumes:
       - name: mobio-shared-data
         persistentVolumeClaim:
           claimName: mobio-resources-pvc
       - name: mobio-public-shared-data
         persistentVolumeClaim:
           claimName: mobio-public-resources-pvc
---
apiVersion: v1
kind: Service
metadata:
 name: market-place-app-api-service
 labels:
   app: market-place-app-api
spec:
 ports:
   - port: 80
     protocol: TCP
 selector:
   app: marketplace-api
