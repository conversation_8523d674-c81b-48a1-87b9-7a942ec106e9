<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Debugger</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.5/css/jquery.dataTables.min.css">
    <style>
        body {
            margin: 20px;
        }

        .code-container {
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            overflow-x: auto;
            max-height: 600px;
        }

        /* CSS cho bảng chi tiết để không có thanh cuộn */
        #detailsTableContainer {
            overflow: visible;
            /* Loại bỏ thanh cuộn */
        }

        /* Tùy chỉnh chiều rộng cột nếu cần */
        #detailsTable th,
        #detailsTable td {
            white-space: normal;
            /* Cho phép nội dung xuống dòng */
            word-wrap: break-word;
            /* Đảm bảo nội dung không tràn ra ngoài */
            padding: 8px;
            /* Giảm padding để tiết kiệm không gian */
            font-size: 0.9rem;
            /* Giảm font-size nếu cần */
        }

        /* CSS cho phần hiển thị số lượng */
        #summary {
            margin-bottom: 15px;
            text-align: center;
            /* Căn giữa nội dung */
        }

        #summary .badge {
            font-size: 1rem;
            margin-right: 10px;
        }

        /* Đảm bảo bảng chi tiết sử dụng toàn bộ chiều rộng container và căn giữa */
        #detailsTable {
            width: 100%;
            /* Đảm bảo bảng chiếm toàn bộ chiều rộng container */
            margin: 0 auto;
            /* Căn giữa bảng */
        }

        /* Định dạng nội dung JSON */
        .json-content {
            white-space: pre-wrap;
            /* Cho phép xuống dòng */
            word-break: break-word;
            /* Đảm bảo nội dung không tràn ra ngoài */
            max-height: 200px;
            /* Giới hạn chiều cao để tránh table quá cao */
            overflow-y: auto;
            /* Thêm thanh cuộn dọc nếu cần */
        }

        /* Tăng chiều rộng của cột Event Value */
        #detailsTable th.event-value,
        #detailsTable td.event-value {
            width: 30%;
        }
    </style>
</head>

<body>
    <div class="container">
        <h2 class="mb-4 text-center">Live Debugger</h2>

        <!-- Form lọc dữ liệu -->
        <form method="POST" class="mb-4">
            <div class="row">
                <div class="col-md-4">
                    <label for="connector_id" class="form-label">Connector ID</label>
                    <input type="text" class="form-control" id="connector_id" name="connector_id"
                        placeholder="Nhập Connector ID" value="{{ connector_id }}">
                </div>
                <div class="col-md-4">
                    <label for="session_id" class="form-label">Session ID</label>
                    <input type="text" class="form-control" id="session_id" name="session_id"
                        placeholder="Nhập Session ID" value="{{ session_id }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Lọc dữ liệu</button>
                </div>
            </div>
        </form>

        <!-- Bảng hiển thị dữ liệu -->
        <table id="requestTable" class="table table-bordered w-100">
            <thead>
                <tr>
                    <th>Connector ID</th>
                    <th>Session ID</th>
                    <th>Thời gian bắt đầu xử lý</th>
                    <th>Thời gian kết thúc</th>
                    <th>Trạng thái consume dữ liệu</th>
                    <th>Trạng thái xử lý dữ liệu</th>
                    <th>Thời gian tạo pipeline</th>
                    <th>Chi tiết</th> <!-- Thêm cột này -->
                </tr>
            </thead>
            <tbody>
                {% for request in requests %}
                <tr>
                    <td>{{ request.connector_id }}</td>
                    <td>{{ request.session_id }}</td>
                    <td>{{ request.start_time }}</td>
                    <td>{{ request.end_time }}</td>
                    <td>{{ request.consume_status }}</td>
                    <td>{{ request.process_status }}</td>
                    <td>{{ request.created_time }}</td>
                    <td><a href="#" class="view-details" data-id="{{ request.session_id }}">Xem chi tiết</a></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Bảng chi tiết mới được căn giữa và không có thanh cuộn -->
        <div id="details" class="mt-4" style="display: none;">
            <h3 class="text-center">Chi tiết Session</h3>

            <!-- Phần hiển thị số lượng -->
            <div id="summary" class="mb-3">
                <span class="badge bg-success" id="count-consume">Consume: 0</span>
                <span class="badge bg-danger" id="count-error">Error: 0</span>
                <span class="badge bg-info text-dark" id="count-processed">Processed: 0</span>
            </div>

            <!-- Loader -->
            <div id="loader" class="text-center mb-3" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>

            <div id="detailsTableContainer">
                <table class="table table-striped w-100" id="detailsTable">
                    <thead>
                        <tr>
                            <th>Message ID</th>
                            <th>Thời gian bắt đầu</th>
                            <th class="event-value">Event Value</th>
                            <th>Trạng thái consume</th>
                            <th>Kết quả consume</th>
                            <th>Trạng thái xử lý</th>
                            <th>Kết quả xử lý</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Dữ liệu sẽ được thêm vào đây bằng JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bao gồm các thư viện JS -->
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.5/js/jquery.dataTables.min.js"></script>
    <script>
        $(document).ready(function () {
            // Khởi tạo DataTables cho bảng chính mà không có phân trang (paging)
            $('#requestTable').DataTable({
                "paging": false, // Bỏ phân trang
                "scrollX": false, // Loại bỏ cuộn ngang cho bảng chính nếu không cần
                "language": {
                    "search": "Tìm kiếm:",
                    "lengthMenu": "Hiển thị _MENU_ mục",
                    "zeroRecords": "Không tìm thấy dữ liệu",
                    "info": "Hiển thị từ _START_ đến _END_ trong tổng số _TOTAL_ mục",
                    "infoEmpty": "Hiển thị từ 0 đến 0 trong tổng số 0 mục",
                    "infoFiltered": "(lọc từ tổng số _MAX_ mục)"
                },
                "responsive": true // Bật responsive cho bảng chính
            });

            // Biến để theo dõi trạng thái DataTables của detailsTable
            let detailsTableInitialized = false;

            $('.view-details').on('click', function (e) {
                e.preventDefault();
                const sessionId = $(this).data('id');

                // Hiển thị Loader và ẩn bảng chi tiết
                $('#loader').show();
                $('#detailsTableContainer').hide();
                $('#details').show();

                $.getJSON(`/guest/market-place/api/v1.0/dataflow/connectors/live-debug/${sessionId}`, function (data) {
                    // Ẩn Loader sau khi nhận được dữ liệu
                    $('#loader').hide();
                    $('#detailsTableContainer').show();

                    if (data && Array.isArray(data)) {
                        // Gom nhóm dữ liệu theo message_id
                        const groupedData = data.reduce((acc, item) => {
                            const messageId = item.message_id;
                            if (!acc[messageId]) {
                                acc[messageId] = {
                                    created_time: item.created_time,
                                    event_value: item.event_value,
                                    state_consume: null,
                                    result_consume: null,
                                    other_states: [],
                                    other_results: []
                                };
                            }
                            if (item.state.toLowerCase() === 'consume') {
                                acc[messageId].state_consume = item.state;
                                if (item.result) {
                                    const sanitizedResult = item.result.replace(/\bNone\b/g, 'null');
                                    acc[messageId].result_consume = sanitizedResult;
                                }
                            } else {
                                // Check if this state already exists for this message
                                if (!acc[messageId].other_states.includes(item.state)) {
                                    acc[messageId].other_states.push(item.state);
                                    if (item.result) {
                                        const sanitizedResult = item.result.replace(/\bNone\b/g, 'null');
                                        acc[messageId].other_results.push(sanitizedResult);
                                    } else {
                                        acc[messageId].other_results.push(null);
                                    }
                                }
                            }

                            return acc;
                        }, {});

                        const $detailsTableBody = $('#detailsTable tbody');
                        $detailsTableBody.empty(); // Xóa dữ liệu cũ

                        // Biến để đếm số lượng consume, error, processed
                        let countConsume = 0;
                        let countError = 0;
                        let countProcessed = 0;

                        // Lặp qua các nhóm message_id
                        for (const [messageId, details] of Object.entries(groupedData)) {
                            // Phân tích event_value nếu là chuỗi JSON
                            let eventValue = 'N/A';
                            if (details.event_value) {
                                try {
                                    const parsedEventValue = typeof details.event_value === 'string' ? JSON.parse(details.event_value) : details.event_value;
                                    eventValue = `<div class="json-content">${JSON.stringify(parsedEventValue, null, 2)}</div>`;
                                } catch (e) {
                                    console.error('Lỗi phân tích event_value:', e);
                                    eventValue = details.event_value;
                                }
                            }

                            // Định dạng State Consume và Result Consume
                            let stateConsumeBadge = 'N/A';
                            let resultConsumeContent = 'N/A';
                            if (details.state_consume) {
                                countConsume++;
                                // Định dạng màu sắc cho State Consume
                                switch (details.state_consume.toLowerCase()) {
                                    case 'consume':
                                        stateConsumeBadge = '<span class="badge bg-success">Consume</span>';
                                        break;
                                    default:
                                        stateConsumeBadge = `<span class="badge bg-secondary">${details.state_consume}</span>`;
                                }

                                // Phân tích result_consume nếu là chuỗi JSON
                                if (details.result_consume) {
                                    try {
                                        const parsedResultConsume = typeof details.result_consume === 'string' ? JSON.parse(details.result_consume) : details.result_consume;
                                        resultConsumeContent = `<div class="json-content">${JSON.stringify(parsedResultConsume, null, 2)}</div>`;
                                    } catch (e) {
                                        console.error('Lỗi phân tích result_consume:', e);
                                        resultConsumeContent = details.result_consume;
                                    }
                                }
                            }

                            // Định dạng các State và Result khác
                            let otherStatesFormatted = '';
                            let otherResultsFormatted = '';
                            details.other_states.forEach(function (state, index) {
                                const result = details.other_results[index];
                                // Đếm số lượng consume, error, processed dựa trên state
                                if (state.toLowerCase() === 'error') {
                                    countError++;
                                } else if (state.toLowerCase() === 'processed') {
                                    countProcessed++;
                                }

                                // Định dạng màu sắc cho State
                                let stateBadge = '';
                                switch (state.toLowerCase()) {
                                    case 'error':
                                        stateBadge = '<span class="badge bg-danger">Error</span>';
                                        break;
                                    case 'processed':
                                        stateBadge = '<span class="badge bg-info text-dark">Processed</span>';
                                        break;
                                    default:
                                        stateBadge = `<span class="badge bg-secondary">${state}</span>`;
                                }

                                // Phân tích result nếu là chuỗi JSON
                                let resultContent = 'N/A';
                                if (result) {
                                    try {
                                        const parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
                                        resultContent = `<div class="json-content">${JSON.stringify(parsedResult, null, 2)}</div>`;
                                    } catch (e) {
                                        console.error('Lỗi phân tích result:', e);
                                        resultContent = result;
                                    }
                                }

                                otherStatesFormatted += `
                                    <div class="mb-2">
                                        ${stateBadge}
                                    </div>
                                `;

                                otherResultsFormatted += `
                                    <div class="mb-2">
                                        ${resultContent}
                                    </div>
                                `;
                            });

                            const row = `
                                <tr>
                                    <td>${messageId || 'N/A'}</td>
                                    <td>${details.created_time || 'N/A'}</td>
                                    <td>${eventValue}</td>
                                    <td>${stateConsumeBadge}</td>
                                    <td>${resultConsumeContent}</td>
                                    <td>${otherStatesFormatted}</td>
                                    <td>${otherResultsFormatted}</td>
                                </tr>
                            `;
                            $detailsTableBody.append(row);
                        }

                        // Cập nhật số lượng consume, error, processed
                        $('#count-consume').text(`Consume: ${countConsume}`);
                        $('#count-error').text(`Error: ${countError}`);
                        $('#count-processed').text(`Processed: ${countProcessed}`);

                        // Nếu DataTables chưa được khởi tạo, khởi tạo nó
                        if (!detailsTableInitialized) {
                            $('#detailsTable').DataTable({
                                "paging": false, // Bỏ phân trang
                                "searching": true, // Bật tìm kiếm
                                "ordering": false, // Bỏ sắp xếp vì có nội dung phức tạp
                                "responsive": true, // Bật responsive
                                "scrollX": false, // Loại bỏ cuộn ngang
                                "language": {
                                    "search": "Tìm kiếm:",
                                    "lengthMenu": "Hiển thị _MENU_ mục",
                                    "zeroRecords": "Không tìm thấy dữ liệu",
                                    "info": "Hiển thị từ _START_ đến _END_ trong tổng số _TOTAL_ mục",
                                    "infoEmpty": "Hiển thị từ 0 đến 0 trong tổng số 0 mục",
                                    "infoFiltered": "(lọc từ tổng số _MAX_ mục)"
                                },
                                "dom": '<"top"f>rt<"bottom"ip><"clear">'
                            });
                            detailsTableInitialized = true;
                        } else {
                            // Nếu đã khởi tạo DataTables, cập nhật lại dữ liệu
                            const detailsTable = $('#detailsTable').DataTable();
                            detailsTable.clear().draw();
                            for (const [messageId, details] of Object.entries(groupedData)) {
                                // Phân tích event_value nếu là chuỗi JSON
                                let eventValue = 'N/A';
                                if (details.event_value) {
                                    try {
                                        const parsedEventValue = typeof details.event_value === 'string' ? JSON.parse(details.event_value) : details.event_value;
                                        eventValue = `<div class="json-content">${JSON.stringify(parsedEventValue, null, 2)}</div>`;
                                    } catch (e) {
                                        console.error('Lỗi phân tích event_value:', e);
                                        eventValue = details.event_value;
                                    }
                                }

                                // Định dạng State Consume và Result Consume
                                let stateConsumeBadge = 'N/A';
                                let resultConsumeContent = 'N/A';
                                if (details.state_consume) {
                                    // Định dạng màu sắc cho State Consume
                                    switch (details.state_consume.toLowerCase()) {
                                        case 'consume':
                                            stateConsumeBadge = '<span class="badge bg-success">Consume</span>';
                                            break;
                                        default:
                                            stateConsumeBadge = `<span class="badge bg-secondary">${details.state_consume}</span>`;
                                    }

                                    // Phân tích result_consume nếu là chuỗi JSON
                                    if (details.result_consume) {
                                        try {
                                            const parsedResultConsume = typeof details.result_consume === 'string' ? JSON.parse(details.result_consume) : details.result_consume;
                                            resultConsumeContent = `<div class="json-content">${JSON.stringify(parsedResultConsume, null, 2)}</div>`;
                                        } catch (e) {
                                            console.error('Lỗi phân tích result_consume:', e);
                                            resultConsumeContent = details.result_consume;
                                        }
                                    }
                                }

                                // Định dạng các State và Result khác
                                let otherStatesFormatted = '';
                                let otherResultsFormatted = '';
                                details.other_states.forEach(function (state, index) {
                                    const result = details.other_results[index];
                                    // Định dạng màu sắc cho State
                                    let stateBadge = '';
                                    switch (state.toLowerCase()) {
                                        case 'error':
                                            stateBadge = '<span class="badge bg-danger">Error</span>';
                                            break;
                                        case 'processed':
                                            stateBadge = '<span class="badge bg-info text-dark">Processed</span>';
                                            break;
                                        default:
                                            stateBadge = `<span class="badge bg-secondary">${state}</span>`;
                                    }

                                    // Phân tích result nếu là chuỗi JSON
                                    let resultContent = 'N/A';
                                    if (result) {
                                        try {
                                            const parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
                                            resultContent = `<div class="json-content">${JSON.stringify(parsedResult, null, 2)}</div>`;
                                        } catch (e) {
                                            console.error('Lỗi phân tích result:', e);
                                            resultContent = result;
                                        }
                                    }

                                    otherStatesFormatted += `
                                        <div class="mb-2">
                                            ${stateBadge}
                                        </div>
                                    `;

                                    otherResultsFormatted += `
                                        <div class="mb-2">
                                            ${resultContent}
                                        </div>
                                    `;
                                });

                                detailsTable.row.add([
                                    messageId || 'N/A',
                                    details.created_time || 'N/A',
                                    eventValue,
                                    stateConsumeBadge,
                                    resultConsumeContent,
                                    otherStatesFormatted,
                                    otherResultsFormatted
                                ]).draw(false);
                            }

                            // Điều chỉnh lại cột sau khi cập nhật dữ liệu
                            detailsTable.columns.adjust().responsive.recalc();
                        }

                        // Điều chỉnh lại cột sau khi cập nhật dữ liệu
                        $('#detailsTable').DataTable().columns.adjust().responsive.recalc();
                    } else {
                        alert('Không tìm thấy chi tiết!');
                    }
                }).fail(function () {
                    $('#loader').hide();
                    $('#detailsTableContainer').show();
                    alert('Lỗi khi gọi API!');
                });
            });
        });
    </script>
</body>

</html>