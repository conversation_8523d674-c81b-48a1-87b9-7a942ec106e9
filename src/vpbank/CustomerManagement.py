#!/usr/bin/python
# -*- coding: utf8 -*-

*********************************** Batches ***********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/batches/import/users Batch import users
@apiDescription Dịch vụ tạo/update user.
@apiGroup BatchOperations
@apiVersion 1.0.0
@apiName BatchImportUsers
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}    business_case_id    Id của chương trình campaign.
@apiParam   (Body:)   {User[]}    users   Danh sách các user cần import vào hệ thống.

@apiParam  (User)      {String}        [display_name]                Tên khách hàng
@apiParam     (User)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (User)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (User)      {String}        [email]                       Email của khách hàng
@apiParam  (User)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (User)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (User)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (User)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (User)       {String}        [address]                      Địa chỉ
@apiParam  (User)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (User)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (User)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (User)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (User)       {Number}        [district_code]                Mã quận huyện
@apiParam  (User)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (User)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (User)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (User)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam  (User)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (User)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (User)      {Number}        [religiousness]                Tôn giáo
@apiParam  (User)      {Number}        [nation]                       Dân tộc.
@apiParam  (User)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (User)      {String}        [location]                     Vị trí.
@apiParam  (User)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (User)    {Object}    [third_party_info]    Thông tin bổ sung của third party. Thông tin này sẽ được gửi lại cho third party sau khi phân tích user.

@apiParamExample   {json}  Body example
{
  "business_case_id":"3ec01a58-e8dc-4e99-925c-0ee094b5e92e",
  "users": [
    {
      "display_name": "andrew",
      "phone_number": "+***********",
      "email": "<EMAIL>",
      "social_user": {
          "id_social": "3456789",
          "social_type": 1,
      },
      "full_name": "Andrew Nguyễn",
      "phone_number2": "",
      "phone_number3": "",
      "gender": 2,
      "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
      "national_phone_code": "84",
      "province_code": 1,
      "district_code": 5,
      "ward_code": 167,
      "marital_status": 1,
      "birthday": "********",
      "income_low_threshold": 0,
      "income_high_threshold": 0,
      "income_unit": 1,
      "income_type": 1,
      "religiousness": 1,
      "nation": 1,
      "job": 39,
      "location": "",
      "people_id": "",
      "frequently_demands": [
          1,
          2,
          3
      ],
      "third_party_info":{
        "id": "f79a7c11-31ae-4637-9a09-a0fce3184dd4"
      }
    }
  ]
}
"""

******************************** Feedback Lead ********************************
* version: 1.0.4                                                              *
* version: 1.0.3                                                              *
* version: 1.0.2                                                              *
* version: 1.0.1                                                              *
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM-VPBank update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.4
@apiName FeedbackLead
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống VPBank-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (UserInfo)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (UserInfo)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (UserInfo)         {Array}     answers         Danh sách câu trả lời của khách hàng.
@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.
@apiParam   (UserInfo)    {Object}    [third_party_info]    Thông tin bổ sung của third party. Thông tin này sẽ được gửi lại cho third party sau khi phân tích user.

@apiParam     (Feedback)    {Array}  [status]   Cập nhật tình trạng lead.
@apiParam     (Feedback)    {Number}  [status..value]   Trạng thái lead.<br/>
<li><code>1: Chưa liên hệ</code></li>
<li><code>2: KH không nghe máy</code></li>
<li><code>3: KH hẹn gọi lại sau</code></li>
<li><code>4: KH tắt máy hoặc sai số</code></li>
<li><code>5: KH từ chối</code></li>
<li><code>6: KH không đủ điều kiện</code></li>
<li><code>7: KH đã sử dụng sản phẩm</code></li>
<li><code>8: KH đồng ý</code></li>
<li><code>9: Khác</code></li>
<li><code>10: KH nộp hồ sơ</code></li>
<li><code>11: KH mở thẻ thành công</code></li>
<li><code>12: KH đủ điều kiện nhưng ở xa không hỗ trợ</code></li>
@apiParam     (Feedback)    {Number}  [status..updated_time]   Thời điểm cập nhật trạng thái. Đơn vị tính timestamp. Ví dụ: <code>"updated_time": 1528881775138</code><br/>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
              "display_name": "AnNV",
              "phone_number": "+***********",
              "email": "<EMAIL>",
              "status": 1,
              "avatar": "",
              "birthday": "29/11/1989",
              "gender": 2,
              "address": "Cầu Giấy, Hà Nội",
              "phone_number2": "",
              "national_phone_code": "84",
              "province_code": 1,
              "district_code": 1,
              "ward_code": 1,
              "marital_status": 1,
              "monthly_income": 0,
              "income_unit": 1,
              "income_type": 1,
              "religiousness": 1,
              "nation": 1,
              "job": 39,
              "location": "",
              "people_id": "**********",
              "frequently_demands": [1, 2, 3],
              "answers": [
                {
                    "question_id": 1,
                    "int_result": 1,
                    "string_result": "Có"
                },
                {
                    "question_id": 2,
                    "int_result": 0,
                    "string_result": "Không"
                }
              ],
              "third_party_info":{
                "id": "f79a7c11-31ae-4637-9a09-a0fce3184dd4"
              }
            },
            "feedback":{
                "status":[
                  {
                    "value": 1,
                    "updated_time": *************
                  },
                  {
                    "value": 8,
                    "updated_time": *************
                  }
                ]
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống VPBank-Mobio.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""
****************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM-VPBank update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.3
@apiName FeedbackLead
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống VPBank-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (UserInfo)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (UserInfo)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (UserInfo)         {Array}     answers         Danh sách câu trả lời của khách hàng.
@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.

@apiParam     (Feedback)    {Array}  [status]   Cập nhật tình trạng lead.
@apiParam     (Feedback)    {Number}  [status..value]   Trạng thái lead.<br/>
<li><code>1: Chưa liên hệ</code></li>
<li><code>2: KH không nghe máy</code></li>
<li><code>3: KH hẹn gọi lại sau</code></li>
<li><code>4: KH tắt máy hoặc sai số</code></li>
<li><code>5: KH từ chối</code></li>
<li><code>6: KH không đủ điều kiện</code></li>
<li><code>7: KH đã sử dụng sản phẩm</code></li>
<li><code>8: KH đồng ý</code></li>
<li><code>9: Khác</code></li>
<li><code>10: KH nộp hồ sơ</code></li>
<li><code>11: KH mở thẻ thành công</code></li>
<li><code>12: KH đủ điều kiện nhưng ở xa không hỗ trợ</code></li>
@apiParam     (Feedback)    {Number}  [status..updated_time]   Thời điểm cập nhật trạng thái. Đơn vị tính timestamp. Ví dụ: <code>"updated_time": 1528881775138</code><br/>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
              "display_name": "AnNV",
              "phone_number": "+***********",
              "email": "<EMAIL>",
              "status": 1,
              "avatar": "",
              "birthday": "29/11/1989",
              "gender": 2,
              "address": "Cầu Giấy, Hà Nội",
              "phone_number2": "",
              "national_phone_code": "84",
              "province_code": 1,
              "district_code": 1,
              "ward_code": 1,
              "marital_status": 1,
              "monthly_income": 0,
              "income_unit": 1,
              "income_type": 1,
              "religiousness": 1,
              "nation": 1,
              "job": 39,
              "location": "",
              "people_id": "**********",
              "frequently_demands": [1, 2, 3],
              "answers": [
                {
                    "question_id": 1,
                    "int_result": 1,
                    "string_result": "Có"
                },
                {
                    "question_id": 2,
                    "int_result": 0,
                    "string_result": "Không"
                }
              ]
            },
            "feedback":{
                "status":[
                  {
                    "value": 1,
                    "updated_time": *************
                  },
                  {
                    "value": 8,
                    "updated_time": *************
                  }
                ]
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống VPBank-Mobio.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""
****************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM-VPBank update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.2
@apiName FeedbackLead
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống VPBank-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (UserInfo)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (UserInfo)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (UserInfo)         {Array}     answers         Danh sách câu trả lời của khách hàng.
@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.

@apiParam     (Feedback)    {Number}  [trusted]   Kết quả đánh giá trusted profile.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Profile không trusted;</li>
<li><code>1</code>: Profile có trusted.</li>
@apiParam     (Feedback)    {Number}  [saled]   Kết quả sale.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Sale thất bại;</li>
<li><code>1</code>: Sale thành công.</li>
@apiParam     (Feedback)    {Number}  [demand]   Kết quả đánh giá nhu cầu của khách hàng.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: KH không có nhu cầu;</li>
<li><code>1</code>: KH có nhu cầu.</li>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
              "display_name": "AnNV",
              "phone_number": "+***********",
              "email": "<EMAIL>",
              "status": 1,
              "avatar": "",
              "birthday": "29/11/1989",
              "gender": 2,
              "address": "Cầu Giấy, Hà Nội",
              "phone_number2": "",
              "national_phone_code": "84",
              "province_code": 1,
              "district_code": 1,
              "ward_code": 1,
              "marital_status": 1,
              "monthly_income": 0,
              "income_unit": 1,
              "income_type": 1,
              "religiousness": 1,
              "nation": 1,
              "job": 39,
              "location": "",
              "people_id": "**********",
              "frequently_demands": [1, 2, 3],
              "answers": [
                {
                    "question_id": 1,
                    "int_result": 1,
                    "string_result": "Có"
                },
                {
                    "question_id": 2,
                    "int_result": 0,
                    "string_result": "Không"
                }
              ]
            },
            "feedback":{
                "trusted": 1,
                "saled": 1,
                "demand": 1
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống VPBank-Mobio.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""
****************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM-VPBank update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.1
@apiName FeedbackLead
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống VPBank-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam  (UserInfo)      {Number}        [monthly_income]         Thu nhập hàng tháng
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam     (Feedback)    {Number}  [trusted]   Kết quả đánh giá trusted profile.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Profile không trusted;</li>
<li><code>1</code>: Profile có trusted.</li>
@apiParam     (Feedback)    {Number}  [saled]   Kết quả sale.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Sale thất bại;</li>
<li><code>1</code>: Sale thành công.</li>
@apiParam     (Feedback)    {Number}  [demand]   Kết quả đánh giá nhu cầu của khách hàng.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: KH không có nhu cầu;</li>
<li><code>1</code>: KH có nhu cầu.</li>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
                "display_name": "AnNV",
                "phone_number": "+***********",
                "email": "<EMAIL>",
                "status": 1,
                "avatar": "",
                "birthday": "29/11/1989",
                "gender": 2,
                "address": "Cầu Giấy, Hà Nội",
                "phone_number2": "",
                "national_phone_code": "84",
                "province_code": 1,
                "district_code": 1,
                "ward_code": 1,
                "marital_status": 1,
                "monthly_income": 0,
                "income_unit": 1,
                "income_type": 1,
                "religiousness": 1,
                "nation": 1,
                "job": 39,
                "location": "",
                "people_id": "**********",
                "frequently_demands": [1, 2, 3]
            },
            "feedback":{
                "trusted": 1,
                "saled": 1,
                "demand": 1
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống VPBank-Mobio.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""
****************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM-VPBank update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.0
@apiName FeedbackLead
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống VPBank-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam  (UserInfo)      {Number}        [monthly_income]         Thu nhập hàng tháng
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [hobby]                        Sở thích.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam     (Feedback)    {Number}  [trusted]   Kết quả đánh giá trusted profile.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Profile không trusted;</li>
<li><code>1</code>: Profile có trusted.</li>
@apiParam     (Feedback)    {Number}  [saled]   Kết quả sale.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Sale thất bại;</li>
<li><code>1</code>: Sale thành công.</li>
@apiParam     (Feedback)    {Number}  [demand]   Kết quả đánh giá nhu cầu của khách hàng.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: KH không có nhu cầu;</li>
<li><code>1</code>: KH có nhu cầu.</li>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
                "display_name": "AnNV",
                "phone_number": "+***********",
                "email": "<EMAIL>",
                "status": 1,
                "avatar": "",
                "birthday": "29/11/1989",
                "gender": 2,
                "address": "Cầu Giấy, Hà Nội",
                "phone_number2": "",
                "national_phone_code": "84",
                "province_code": 1,
                "district_code": 1,
                "ward_code": 1,
                "marital_status": 1,
                "monthly_income": 0,
                "income_unit": 1,
                "income_type": 1,
                "religiousness": 1,
                "nation": 1,
                "job": 39,
                "hobby": "",
                "location": "",
                "people_id": "**********",
                "frequently_demands": [1, 2, 3]
            },
            "feedback":{
                "trusted": 1,
                "saled": 1,
                "demand": 1
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống VPBank-Mobio.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""


****************************************** API Update User **************************************************
* version: 1.0.4                                                                                            *
* version: 1.0.3                                                                                            *
* version: 1.0.2                                                                                            *
* version: 1.0.1                                                                                            *
* version: 1.0.0                                                                                            *
*************************************************************************************************************
"""
@api {patch} https://api.bank.mobio.vn/profiling/v1.0/users/<user_id> Update user
@apiDescription API cập nhật 1 user.
@apiGroup Customer
@apiVersion 1.0.4
@apiName UpdateCustomer
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.
@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (Body:)     {Array}     [answers]                               Mảng danh sách câu trả lời của khách hàng

@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.
@apiParam   (Body:)    {Object}    [third_party_info]    Thông tin bổ sung của third party. Thông tin này sẽ được gửi lại cho third party sau khi phân tích user.

@apiParamExample Body answers object Example:
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="answers"
{
  "question_id": 1,
  "int_result": 1,
  "string_result": "Có"
}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="answers"
{
  "question_id": 2,
  "int_result": 0,
  "string_result": "Không"
}

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 3 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiSuccess       {Object}        social_user                  Mảng đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": [{
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }],
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
******************
"""
@api {patch} https://api.bank.mobio.vn/profiling/v1.0/users/<user_id> Update user
@apiDescription API cập nhật 1 user.
@apiGroup Customer
@apiVersion 1.0.3
@apiName UpdateCustomer
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.
@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (Body:)     {Array}     [answers]                               Mảng danh sách câu trả lời của khách hàng

@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.

@apiParamExample Body answers object Example:
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="answers"
{
  "question_id": 1,
  "int_result": 1,
  "string_result": "Có"
}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="answers"
{
  "question_id": 2,
  "int_result": 0,
  "string_result": "Không"
}

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 3 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiSuccess       {Object}        social_user                  Mảng đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": [{
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }],
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
******************
"""
@api {patch} https://api.bank.mobio.vn/profiling/v1.0/users/<user_id> Update user
@apiDescription API cập nhật 1 user.
@apiGroup Customer
@apiVersion 1.0.2
@apiName UpdateCustomer
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.
@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 3 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiSuccess       {Object}        social_user                  Mảng đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": [{
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }],
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
******************
"""
@api {patch} https://api.bank.mobio.vn/profiling/v1.0/users/<user_id> Update user
@apiDescription API cập nhật 1 user.
@apiGroup Customer
@apiVersion 1.0.1
@apiName UpdateCustomer
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.
@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 3 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiSuccess       {Object}        social_user                  Mảng đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": [{
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }],
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
******************
"""
@api {patch} https://api.bank.mobio.vn/profiling/v1.0/users/<user_id> Update user
@apiDescription API cập nhật 1 user. <br/>Lưu ý: Body được gửi lên theo cấu trúc <code>form-data</code>
@apiGroup Customer
@apiVersion 1.0.0
@apiName UpdateCustomer
@apiIgnore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.
@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 3 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Object}        social_user                  Mảng đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": [{
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }],
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": "",
  "people_id": ""
}
"""

************************************************************************************************************
*********************************** API Get Danh sách khách hàng *******************************************
************************************************************************************************************
"""
@api {get} https://api.bank.mobio.vn/profiling/v1.0/users Lấy danh sách thông tin user
@apiDescription API Lấy Danh sách thông tin user, gồm social info và các thông tin cơ bản. API hỗ trợ tìm kiếm, sắp xếp, filter, paging danh sách user.
@apiGroup Customer
@apiVersion 1.0.0
@apiName GetListUserInfo
@apiIgnore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)     {Number}    [mkt_step]                                 Filter theo Trạng thái tài khoản.
<li><code>1: Khách hàng tiềm năng của hệ thống</code>. Bao gồm khách hàng có <code>step=1,2</code> trong CSDL</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>. Bao gồm khách hàng có <code>step=3</code> trong CSDL</li>
<li><code>3: Chờ sale</code>. Bao gồm khách hàng có <code>step=4</code> trong CSDL</li>
@apiParam   (Query:)     {String}    [search]                               Chuỗi tìm kiếm. Tìm kiếm theo tên hoặc email hoặc số điện thoại user.


@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        display_name                Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess  (data)      {String}        email                       Email của khách hàng
@apiSuccess  (data)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess  (data)       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess  (data)       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess  (data)       {String}        phone_number3                Số điện thoại thứ 3 của khách hàng
@apiSuccess  (data)       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {String}        hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
      "mkt_step": 1,
      "display_name": "andrew",
      "phone_number": "+***********",
      "email": "<EMAIL>",
      "status": 1,
      "avatar": "",
      "created_time": "2017-12-12T15:12:28Z",
      "updated_time": "2017-12-12T15:12:28Z",
      "social_user": [{
              "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
              "social_type": 1,
              "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
            }],
      "birthday": "29/11/1989",
      "phone_number2": "",
      "phone_number3": "",
      "gender": 2,
      "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
      "full_name": "andrew",
      "national_phone_code": "84",
      "province_code": 1,
      "district_code": 1,
      "ward_code": 1,
      "marital_status": 1,
      "income_low_threshold": 0,
      "income_high_threshold": 0,
      "income_unit": 1,
      "income_type": 1,
      "religiousness": 1,
      "nation": 1,
      "job": 39,
      "hobby": "",
      "location": "",
      "people_id": "",
      "frequently_demands": [1, 2, 3]
    },
    ...
  ],
  "paging":{
    ...
  }
}
"""

************************************************************************************************************
******************************************* API User fill form *********************************************
************************************************************************************************************
"""
@api {put} https://api.bank.mobio.vn/profiling/v1.0/users/actions/fill-form User điền thông tin cá nhân
@apiDescription API Lưu thông tin user khi user điền form. API sẽ tìm kiếm user theo số điện thoại hoặc email, nếu tồn tại user sẽ update thông tin, ngược lại sẽ tạo mới.
@apiGroup Customer
@apiVersion 1.0.2
@apiName UserFillForm
@apiIgnore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>

@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 3 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
"""
@api {put} https://api.bank.mobio.vn/profiling/v1.0/users/actions/fill-form User điền thông tin cá nhân
@apiDescription API Lưu thông tin user khi user điền form. API sẽ tìm kiếm user theo số điện thoại hoặc email, nếu tồn tại user sẽ update thông tin, ngược lại sẽ tạo mới.
@apiGroup Customer
@apiVersion 1.0.1
@apiName UserFillForm
@apiIgnore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>

@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 3 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
"""
@api {put} https://api.bank.mobio.vn/profiling/v1.0/users/actions/fill-form User điền thông tin cá nhân
@apiDescription API Lưu thông tin user khi user điền form. API sẽ tìm kiếm user theo số điện thoại hoặc email, nếu tồn tại user sẽ update thông tin, ngược lại sẽ tạo mới.<br/><code> Lưu ý: Cấu trúc body gửi lên theo form-data</code>
@apiGroup Customer
@apiVersion 1.0.0
@apiName UserFillForm
@apiIgnore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 3 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": "",
  "people_id": ""
}
"""

*********************************************** API Create User ********************************************
* version: 1.0.3                                                                                           *
* version: 1.0.2                                                                                           *
* version: 1.0.1                                                                                           *
* version: 1.0.0                                                                                           *
************************************************************************************************************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/users/2 Tạo user
@apiDescription API tạo mới 1 user.
@apiGroup Customer
@apiVersion 1.0.3
@apiName CreateCustomer
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    [password]                              Mật khẩu
@apiParam   (Body:)     {String}    display_name                            Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {Object}    [social_user]                           Thông tin chi tiết của user theo mạng xã hội.
<table>
    <thead>
        <tr>
          <th style="width: 40%">Field</th>
          <th style="width: 10%">Type</th>
          <th style="width: 50%">Description</th>
        </tr>
    </thead>
    <tr>
        <td class="code">id_social</td>
        <td>String</td>
        <td><p>ID của social</p></td>
    </tr>
    <tr>
        <td class="code">social_type</td>
        <td>Number</td>
        <td><p>Loại social</p><p class="type-size">Allowed values:<li><code>1:Facebook</code></li><li><code>2:GooglePlus</code></li> <li><code>3:Instagram</code></li> <li><code>4:Zalo</code></li></p></td>
    </tr>
    <tr>
        <td class="code">access_token <span class="label label-optional">optional</span></td>
        <td>String</td>
        <td><p>Access token để truy cập social (nếu có).</p></td>
    </tr>
</table>
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYYmmDD</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (Body:)     {Array}     [answers]                               Mảng danh sách câu trả lời của khách hàng

@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.
@apiParam   (Body:)    {Object}    [third_party_info]    Thông tin bổ sung của third party. Thông tin này sẽ được gửi lại cho third party sau khi phân tích user.

@apiParamExample Body answers object Example:
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="answers"
{
  "question_id": 1,
  "int_result": 1,
  "string_result": "Có"
}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="answers"
{
  "question_id": 2,
  "int_result": 0,
  "string_result": "Không"
}

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Social Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
*******************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/users/2 Tạo user
@apiDescription API tạo mới 1 user.
@apiGroup Customer
@apiVersion 1.0.2
@apiName CreateCustomer
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    [password]                              Mật khẩu
@apiParam   (Body:)     {String}    display_name                            Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {Object}    [social_user]                           Thông tin chi tiết của user theo mạng xã hội.
<table>
    <thead>
        <tr>
          <th style="width: 40%">Field</th>
          <th style="width: 10%">Type</th>
          <th style="width: 50%">Description</th>
        </tr>
    </thead>
    <tr>
        <td class="code">id_social</td>
        <td>String</td>
        <td><p>ID của social</p></td>
    </tr>
    <tr>
        <td class="code">social_type</td>
        <td>Number</td>
        <td><p>Loại social</p><p class="type-size">Allowed values:<li><code>1:Facebook</code></li><li><code>2:GooglePlus</code></li> <li><code>3:Instagram</code></li> <li><code>4:Zalo</code></li></p></td>
    </tr>
    <tr>
        <td class="code">access_token <span class="label label-optional">optional</span></td>
        <td>String</td>
        <td><p>Access token để truy cập social (nếu có).</p></td>
    </tr>
</table>
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYYmmDD</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (Body:)     {Array}     [answers]                               Mảng danh sách câu trả lời của khách hàng

@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.

@apiParamExample Body answers object Example:
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="answers"
{
  "question_id": 1,
  "int_result": 1,
  "string_result": "Có"
}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="answers"
{
  "question_id": 2,
  "int_result": 0,
  "string_result": "Không"
}

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Social Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
**********************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/users/2 Tạo user
@apiDescription API tạo mới 1 user.
@apiGroup Customer
@apiVersion 1.0.1
@apiName CreateCustomer
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    [password]                              Mật khẩu
@apiParam   (Body:)     {String}    display_name                            Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {Object}    [social_user]                           Thông tin chi tiết của user theo mạng xã hội.
<table>
    <thead>
        <tr>
          <th style="width: 40%">Field</th>
          <th style="width: 10%">Type</th>
          <th style="width: 50%">Description</th>
        </tr>
    </thead>
    <tr>
        <td class="code">id_social</td>
        <td>String</td>
        <td><p>ID của social</p></td>
    </tr>
    <tr>
        <td class="code">social_type</td>
        <td>Number</td>
        <td><p>Loại social</p><p class="type-size">Allowed values:<li><code>1:Facebook</code></li><li><code>2:GooglePlus</code></li> <li><code>3:Instagram</code></li> <li><code>4:Zalo</code></li></p></td>
    </tr>
    <tr>
        <td class="code">access_token <span class="label label-optional">optional</span></td>
        <td>String</td>
        <td><p>Access token để truy cập social (nếu có).</p></td>
    </tr>
</table>
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYYmmDD</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>


@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Social Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
*********************
"""
@api {post} https://api.bank.mobio.vn/profiling/v1.0/users/2 Tạo user
@apiDescription API tạo mới 1 user.
@apiGroup Customer
@apiVersion 1.0.0
@apiName CreateCustomer
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    [password]                              Mật khẩu
@apiParam   (Body:)     {String}    display_name                            Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {Object}    [social_user]                           Thông tin chi tiết của user theo mạng xã hội.
<table>
    <thead>
        <tr>
          <th style="width: 40%">Field</th>
          <th style="width: 10%">Type</th>
          <th style="width: 50%">Description</th>
        </tr>
    </thead>
    <tr>
        <td class="code">id_social</td>
        <td>String</td>
        <td><p>ID của social</p></td>
    </tr>
    <tr>
        <td class="code">social_type</td>
        <td>Number</td>
        <td><p>Loại social</p><p class="type-size">Allowed values:<li><code>1:Facebook</code></li><li><code>2:GooglePlus</code></li> <li><code>3:Instagram</code></li> <li><code>4:Zalo</code></li></p></td>
    </tr>
    <tr>
        <td class="code">access_token <span class="label label-optional">optional</span></td>
        <td>String</td>
        <td><p>Access token để truy cập social (nếu có).</p></td>
    </tr>
</table>
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYYmmDD</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.
@apiParam   (Body:)     {String}    [people_id]                             Chứng minh thư.
@apiParam   (Body:)     {Array}     [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>


@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {String}        people_id                    Chứng minh thư.
@apiSuccess       {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Social Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "income_type": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": "",
  "people_id": "",
  "frequently_demands": [1, 2, 3]
}
"""
