#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 25/12/2024
"""

import json
import re
import uuid
from datetime import datetime

from bson import ObjectId
from pymongo import MongoClient, ReadPreference, WriteConcern

from configs import MongoConfig
from src.common import DatetimeTypeKeys
from src.common.json_encoder import J<PERSON>NEncoder
from src.common.utils import Base64, utf8_to_ascii

base_client = MongoClient(MongoConfig.DATA_ORCHESTRATION_MONGODB_URI)
db_name = re.search(r"^mongodb://[^@]+@[^/]+/([^?$]+).*$", MongoConfig.DATA_ORCHESTRATION_MONGODB_URI).group(1)


class BaseOrchestrationModel:
    client = base_client
    collection_name = ""

    def _db(self):
        return self.client[db_name]

    def get_db(self):
        db = self.client[db_name]
        collection = db[self.collection_name]
        return collection

    def insert(self, dictionary):
        db = self.client[db_name]
        return db[self.collection_name].insert_one(dictionary)

    def insert_many(self, document):
        db = self.client[db_name]
        return db[self.collection_name].insert_many(document)

    def insert_document(self, dictionary):
        db = self.client[db_name]
        return db[self.collection_name].insert_one(dictionary)

    def update_set_dictionary(self, search_option, dictionary):
        db = self.client[db_name]

        document = db[self.collection_name].find_one(search_option)
        if document:
            return (
                db[self.collection_name]
                .update_one(filter=search_option, update={"$set": dictionary}, upsert=True)
                .matched_count
                >= 1
            )
        return None

    def update_dictionary(self, document_id, dictionary):
        if isinstance(document_id, str):
            document_id = ObjectId(document_id)
        return self.collector().update_one({"_id": document_id}, dictionary).matched_count

    def update_one_query(self, query, data):
        return self.client.get_database(db_name)[self.collection_name].update_one(query, {"$set": data}).matched_count

    def update_many(self, filter_option, update_option):
        db = self.client[db_name]
        return db[self.collection_name].update_many(filter_option, update_option).matched_count

    def update(self, filter_option, update_option, upsert=False, multi=False):
        db = self.client[db_name]
        db[self.collection_name].update(filter_option, update_option, upsert=upsert, multi=multi)

    def update_by_set(self, filter_option, update_option, upsert=False, multi=False):
        db = self.client[db_name]
        return db[self.collection_name].update_many(filter_option, {"$set": update_option}).matched_count

    def delete_one(self, delete_options):
        db = self.client[db_name]
        return db[self.collection_name].delete_one(delete_options)

    def delete_many(self, delete_options):
        db = self.client[db_name]
        return db[self.collection_name].delete_many(delete_options).deleted_count

    def upsert(self, search_option, dictionary):
        db = self.client[db_name]
        document = db[self.collection_name].find_one(search_option)
        if document:
            document.update(dictionary)
            self.collector().replace_one({"_id": document.get("_id")}, dictionary, upsert=True)
            return document.get("_id")
        else:
            return db[self.collection_name].insert_one(dictionary).inserted_id

    def find(self, search_option, obj_field_select: dict = None):
        db = self.client[db_name]
        if obj_field_select:
            return db[self.collection_name].find(search_option, obj_field_select)
        return db[self.collection_name].find(search_option)

    def find_one(self, search_option, fields_select: dict = None):
        if fields_select:
            return self.collector().find_one(search_option, fields_select)
        return self.collector().find_one(search_option)

    def collector(self):
        return self._db()[self.collection_name]

    def count_by_query(self, count_option):
        db = self.client[db_name]
        return db[self.collection_name].count_documents(count_option)

    def count(self, search_option=None):
        if not search_option:
            search_option = {}
        return self._db()[self.collection_name].count_documents(search_option)

    def select_all(self, search_option, projection=None):
        return self.collector().find(search_option, projection)

    def find_paginate(self, search_option, page=0, per_page=None, sort_option=None, projection=None, order=-1):
        collection = self.collector().find(search_option, projection)
        if sort_option:
            collection = collection.sort(sort_option, order)

        if page != -1:
            if per_page:
                collection = collection.limit(per_page)
            if page > 0:
                page -= 1
                offset = int(page) * int(per_page)
                collection = collection.skip(offset)

        return collection

    def _aggregate(self, group, match: object, sort=None, project=None):
        db = self.client[db_name]
        pipeline = []
        if match:
            pipeline.append({"$match": match})
        pipeline.append({"$group": group})
        if sort:
            pipeline.append({"$sort": sort})
        if project:
            pipeline.append({"$project": project})
        return db[self.collection_name].aggregate(pipeline)

    def aggregate(self, pipeline):
        db = self.client[db_name]
        return db[self.collection_name].aggregate(pipeline)

    def distinct(self, fields, query):
        db = self.client[db_name]

        if type(fields) is str:
            return db[self.collection_name].distinct(fields, query)

        return None

    def bulk_write_data(self, data):
        db = self.client[db_name]
        return db[self.collection_name].bulk_write(data)

    @staticmethod
    def normalize_uuid(some_uuid):
        if isinstance(some_uuid, str):
            return uuid.UUID(some_uuid)
        return some_uuid

    @staticmethod
    def normalize_object_id(some_object_id):
        if isinstance(some_object_id, str):
            return ObjectId(some_object_id)
        return some_object_id

    def get_collector(self, read_preference=False, write_concern=None):
        w = None
        r = None
        if write_concern is not None:
            w = WriteConcern(w=write_concern)
        if read_preference:
            r = ReadPreference.SECONDARY_PREFERRED
        return self._db().get_collection(self.collection_name, read_preference=r, write_concern=w)

    @classmethod
    def generate_after_token(cls, item, sort_keys):
        """
        Hàm dùng để generate token cho page tiếp theo. (dùng cho Mongo)
        :param item: item cuối cùng của page trước.
        :param sort_keys: Danh sách các field được sắp xếp (tuple)
        :return: token: giá trị dùng để client request lấy page tiếp theo.
        """
        data_gen_token = []
        for key in sort_keys:
            data_gen_token.append(item.get(key))
        if data_gen_token:
            data_token = JSONEncoder().json_loads(data_gen_token)
            next_token = Base64.encode(json.dumps(data_token, ensure_ascii=False))
            return next_token

        return ""

    @classmethod
    def parse_token(cls, token):
        if not token:
            return None
        condition = json.loads(Base64.decode(token))
        return condition

    def find_paginate_load_more(self, search_option, per_page=10, after_token=None, sort_option=None, projection=None):
        """
        Support sort field type: number, _id
        :param search_option:
        :param per_page:
        :param after_token:
        :param sort_option: (chỉ support sort 1 field)
        :param projection: (Bao gồm field được sort)
        :return:
        """
        if not sort_option:
            sort_option = [("_id", 1)]
        # else:
        #     check_id = next((sort for sort in sort_option if sort[0] == "_id"), None)
        #     order_type = next(sort[1] for sort in sort_option)
        #     if not check_id:
        #         sort_option.append(("_id", order_type))
        sort_keys = [k[0] for k in sort_option]

        if after_token:
            token = self.parse_token(after_token)
            # if len(sort_option) == 1:
            #     if sort_option[0][0] == "_id":
            #         token[0] = ObjectId(token[0])
            #     if sort_option[0][1] == 1:
            #         search_extend = {sort_option[0][0]: {"$gt": token[0]}}
            #     else:
            #         search_extend = {sort_option[0][0]: {"$lt": token[0]}}
            # else:
            filter_id = {}
            for index, sort in enumerate(sort_option):
                if not token[index]:
                    continue
                if sort[0] == "_id":
                    token[index] = ObjectId(token[index])
                    if sort[1] == 1:
                        filter_id = {sort[0]: {"$gt": token[index]}}
                    else:
                        filter_id = {sort[0]: {"$lt": token[index]}}

            condition_combine = {}
            for index, sort in enumerate(sort_option):
                if sort[0] != "_id":
                    value = token[index]
                    if sort[0] in DatetimeTypeKeys.VALUES:
                        value = datetime.strptime(value, DatetimeTypeKeys.FORMAT)
                    if sort[1] == 1:
                        condition_combine.update({sort[0]: {"$gte": value}})
                    else:
                        condition_combine.update({sort[0]: {"$lte": value}})
            condition_combine.update(filter_id)
            # lst_search_extend = [condition_compare, condition_combine]

            # search_extend = {
            #     "$or": lst_search_extend
            # }
            search_extend = condition_combine

            if search_option:
                search_option.update(search_extend)
            else:
                search_option = search_extend

        collection = self.get_collector().find(search_option, projection).limit(per_page)
        if sort_option:
            collection = collection.sort(sort_option)
        data = [x for x in collection]
        next_token = None
        if data and len(data) == per_page:
            last_item = data[-1]
            next_token = self.generate_after_token(last_item, sort_keys)

        return data, next_token

    def _gen_data_build_search_query(self, search_keywords):
        if search_keywords:
            normalize_search = utf8_to_ascii(search_keywords)
            normalize_search = normalize_search.lower()
            import re

            rgx = re.compile(".*{}.*".format(normalize_search), re.IGNORECASE)
            return rgx
        return None
