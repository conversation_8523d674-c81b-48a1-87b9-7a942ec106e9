import math

from bson import ObjectId

from src.common import CommonKeys, ProviderConfigKeys, ProviderType
from src.common.utils import get_time_now, utf8_to_ascii
from src.models import BaseModel


class ProviderConfigModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "provider_config"

    def count_provider_config_in_merchant(self, merchant_id, provider_type):
        return self.count({CommonKeys.MERCHANT_ID: merchant_id, ProviderConfigKeys.PROVIDER_TYPE: provider_type})

    def count_email_provider_config_in_merchant(self, merchant_id):
        return self.count({
            CommonKeys.MERCHANT_ID: merchant_id,
            ProviderConfigKeys.PROVIDER_TYPE: {"$in": [ProviderType.SES, ProviderType.MOBIO_MAILER]},
        })

    def create_provider_config(self, payload, merchant_id, created_by):

        name = payload.get(ProviderConfigKeys.NAME).strip()
        time_now = get_time_now()

        search_option = {
            CommonKeys.MERCHANT_ID: merchant_id,
            ProviderConfigKeys.NAME: name,
            ProviderConfigKeys.TYPE: payload.get(ProviderConfigKeys.TYPE),
            ProviderConfigKeys.PROVIDER_TYPE: payload.get(ProviderConfigKeys.PROVIDER_TYPE),
        }

        payload.update(
            {
                ProviderConfigKeys.NAME: name,
                ProviderConfigKeys.LOWER_CASE_NAME: name.lower(),
                CommonKeys.MERCHANT_ID: merchant_id,
                CommonKeys.CREATED_BY: created_by,
                CommonKeys.CREATED_TIME: time_now,
                CommonKeys.UPDATED_TIME: time_now,
                CommonKeys.UPDATED_BY: created_by,
            }
        )

        return self.upsert(search_option=search_option, dictionary=payload)

    def update_provider_config_by_id(self, provider_config_id, payload, updated_by=None):
        time_now = get_time_now()

        payload.update({CommonKeys.UPDATED_TIME: time_now})
        if updated_by:
            payload.update({CommonKeys.UPDATED_BY: updated_by})

        return self.update_one_query({ProviderConfigKeys.ID: ObjectId(provider_config_id)}, payload)

    def get_provider_configs(self, merchant_id, search, provider_types, order, sort, page, per_page, fields_select):
        query = {
            CommonKeys.MERCHANT_ID: merchant_id,
            ProviderConfigKeys.PROVIDER_TYPE: {"$in": provider_types},
        }
        if search:
            search = utf8_to_ascii(search).lower()
            query_update = {
                "$or": [
                    {ProviderConfigKeys.NAME: {"$regex": search}},
                    {ProviderConfigKeys.LOWER_CASE_NAME: {"$regex": search}},
                ]
            }
            query.update(query_update)

        results = list(
            self.find_paginate(query, page, per_page, sort_option=sort, projection=fields_select, order=order)
        )
        total_count = self.count(query)
        total_page = math.ceil(total_count / per_page)

        return results, {
            "page": page,
            "per_page": per_page,
            "total_page": total_page,
            "total_count": total_count,
        }

    def get_detail_provider_config_by_id(
        self,
        merchant_id,
        sms_config_id,
        fields_select={},
    ):

        return self.find_one(
            {CommonKeys.MERCHANT_ID: merchant_id, ProviderConfigKeys.ID: ObjectId(sms_config_id)},
            fields_select=fields_select,
        )

    def get_provider_config_by_name(self, merchant_id, name, provider_type, config_type):
        return self.find_one(
            {
                CommonKeys.MERCHANT_ID: merchant_id,
                ProviderConfigKeys.LOWER_CASE_NAME: name.strip().lower(),
                ProviderConfigKeys.PROVIDER_TYPE: provider_type,
                ProviderConfigKeys.TYPE: config_type,
            }
        )

    def get_provider_config_by_name_in_all_merchant(self, name, provider_type):
        return self.find_one(
            {
                ProviderConfigKeys.LOWER_CASE_NAME: name.strip().lower(),
                ProviderConfigKeys.PROVIDER_TYPE: provider_type,
            }
        )

    def get_provider_config_by_name_in_all_merchant_sms(self, name, lst_provider_type_sms):
        return self.find_one(
            {
                ProviderConfigKeys.LOWER_CASE_NAME: name.strip().lower(),
                ProviderConfigKeys.PROVIDER_TYPE: {"$in": lst_provider_type_sms},
            }
        )

    def delete_provider_config(self, merchant_id, sms_config_id):
        query = {CommonKeys.MERCHANT_ID: merchant_id, ProviderConfigKeys.ID: ObjectId(sms_config_id)}

        return self.delete_one(query).deleted_count

    def get_email_provider_config_in_merchant(self, merchant_id):
        return self.find({
            CommonKeys.MERCHANT_ID: merchant_id,
            ProviderConfigKeys.PROVIDER_TYPE: {"$in": [ProviderType.SES, ProviderType.MOBIO_MAILER]},
        })