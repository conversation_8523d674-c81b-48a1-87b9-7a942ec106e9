#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 10/06/2025
"""

from src.models import BaseModel


class SetupInformationPushNotificationModel(BaseModel):
    """
    - <PERSON><PERSON><PERSON> là Collection lưu thông tin cấu hình push notification của merchant
    - Sẽ lưu 2 loại cấu hình ở trong collection này:
        - TYPE: webpush
        - TYPE: apppush
    - Cấu trúc Collection:
        - _id: ObjectId
        - merchant_id: ObjectId
        - type: String
        - config: Object
        - created_time: Date
        - updated_time: Date
    """
    
    
    def __init__(self):
        super().__init__()
        self.collection_name = "setup_push_notification"

    def get_by_merchant(self, merchant_id):
        return self.find_one({self.field.MERCHANT_ID: merchant_id})
