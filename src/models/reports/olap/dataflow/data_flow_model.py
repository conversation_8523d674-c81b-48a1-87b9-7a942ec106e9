#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 30/05/2024
"""


from uuid import UUID

from mobio.libs.logging import MobioLogging
from sqlalchemy import text
from sqlalchemy.exc import OperationalError, ProgrammingError

from configs import OlapConfig
from src.common.utils import get_list_object_query_starrock
from src.helpers.olap.data_flow.session import DataFlowSession
from src.models.reports.olap.base_dialect import BaseDialect


class DataFlowDialect(BaseDialect):
    def __init__(self, olap_uri=OlapConfig.OLAP_URI, sniff=False):
        super().__init__(olap_uri=olap_uri, sniff=sniff)
        self.olap_uri = olap_uri
        self.session_class = DataFlowSession(olap_uri=self.olap_uri, sniff=sniff)

    def normalize_uuid(self, data) -> UUID:
        return UUID(data)

    def process_query(self, text_query, param_query=None):
        with self.session_class.SessionLocal() as session:
            try:
                statement = text(text_query)
                self.m_log.info("process_query :: statement :: {}".format(statement))
                result = session.execute(statement, param_query)
            except ProgrammingError as pe:
                self.m_log.warning(f"fail when fetch data :: {pe}")
                result = None
            except OperationalError as oe:
                self.m_log.warning(f"fail when operation: {oe}")
                result = None
        return result

    def __convert_session_detail_to_dict(self, session_detail):
        error_count = session_detail.error_count
        consume_count = session_detail.consume_count
        processing_count = session_detail.processing_count
        processed_count = session_detail.processed_count
        session_id = session_detail.session_id

        detail = {
            "session_id": session_id,
            "total_processing": processing_count,
            "total_process_done": processed_count,
            "total_process_fail": error_count,
            "total_wait_process": consume_count - (processing_count + processed_count + error_count),
        }
        return detail

    def __convert_results_sessions(self, sessions):
        results = []
        for session in sessions:
            results.append(self.__convert_session_detail_to_dict(session))
        return results

    def snapshot_list_session_of_connector(
        self,
        merchant_id,
        mapping_state,
        connector_id,
        start_time,
        end_time,
        per_page,
        before_token,
        sort_by,
        order_by,
        lst_consume_status,
    ):
        skip_number = 0
        page_index = 0
        parse_token = self.parse_token(before_token)
        if parse_token:
            page_index = parse_token.get("page_index", 0)
            skip_number = int(page_index) * int(per_page)
        stmt = f"""
        SELECT 
            session_id,
            processing_count,
            processed_count,
            processed_add_count,
            processed_updated_count,
            error_count,
            consume_count,
            start_time,
            end_time,
            connector_name,
            consume_status,
            process_status,
            mode
        FROM (
            SELECT 
                'total_streaming_prev_snapshot' AS session_id,
                SUM(processing_count) AS processing_count,
                SUM(processed_count) AS processed_count,
                SUM(processed_add_count) AS processed_add_count,
                SUM(processed_updated_count) AS processed_updated_count,
                SUM(error_count) AS error_count,
                SUM(consume_count) AS consume_count,
                MIN(start_time) AS start_time,
                MAX(end_time) AS end_time,
                MAX(connector_name) AS connector_name,
                MAX(consume_status) AS consume_status,
                MAX(process_status) AS process_status,
                'streaming' AS mode
            FROM (
                SELECT 
                    sr.session_id,
                    SUM(CASE WHEN sr.state = 'processing' THEN sr.count ELSE 0 END) AS processing_count,
                    SUM(CASE WHEN sr.state = 'processed' THEN sr.count ELSE 0 END) AS processed_count,
                    SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' THEN sr.count ELSE 0 END) AS processed_add_count,
                    SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' THEN sr.count ELSE 0 END) AS processed_updated_count,
                    SUM(CASE WHEN sr.state = 'error' THEN sr.count ELSE 0 END) AS error_count,
                    SUM(CASE WHEN sr.state = 'consume' THEN sr.count ELSE 0 END) AS consume_count,
                    ps.start_time,
                    ps.end_time,
                    ps.connector_name,
                    ps.consume_status,
                    ps.process_status,
                    ps.mode
                FROM data_flow.dataflow_report sr
                INNER JOIN data_flow.pipeline_session ps ON sr.session_id = ps.session_id
                WHERE sr.connector_id = :connector_id
                AND sr.object is null
                AND ps.mode = 'streaming'  -- Chỉ lấy các bản ghi có mode là streaming
                AND STR_TO_DATE(CONCAT(sr.date, ' ', sr.hour, ':', sr.minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
                AND ps.consume_status IN :lst_consume_status
                GROUP BY sr.session_id, ps.start_time, ps.end_time, ps.connector_name,
                        ps.consume_status, ps.process_status, ps.mode
            ) AS streaming_data
            HAVING COUNT(*) > 0
            UNION ALL
            
            SELECT 
                sr.session_id,
                SUM(CASE WHEN sr.state = 'processing' THEN sr.count ELSE 0 END) AS processing_count,
                SUM(CASE WHEN sr.state = 'processed' THEN sr.count ELSE 0 END) AS processed_count,
                SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' THEN sr.count ELSE 0 END) AS processed_add_count,
                SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' THEN sr.count ELSE 0 END) AS processed_updated_count,
                SUM(CASE WHEN sr.state = 'error' THEN sr.count ELSE 0 END) AS error_count,
                SUM(CASE WHEN sr.state = 'consume' THEN sr.count ELSE 0 END) AS consume_count,
                ps.start_time,
                ps.end_time,
                ps.connector_name,
                ps.consume_status,
                ps.process_status,
                ps.mode
            FROM data_flow.dataflow_report sr
            INNER JOIN data_flow.pipeline_session ps ON sr.session_id = ps.session_id
            WHERE sr.connector_id = :connector_id
            AND sr.object is null
            AND ps.mode = 'batch'  -- Chỉ lấy các bản ghi có mode là batch
            AND STR_TO_DATE(CONCAT(sr.date, ' ', sr.hour, ':', sr.minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
            AND ps.consume_status IN :lst_consume_status
            GROUP BY sr.session_id, ps.start_time, ps.end_time, ps.connector_name,
                    ps.consume_status, ps.process_status, ps.mode
        ) AS combined_data
        ORDER BY start_time DESC
        LIMIT :skip_number, :per_page
        """

        stmt1 = f"""
        WITH deduplicated_data AS (
            SELECT *
            FROM (
                SELECT 
                    session_id, 
                    state, 
                    result, 
                    created_time, 
                    connector_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY session_id, message_id, state, connector_id, merchant_id, reason
                        ORDER BY created_time ASC
                    ) AS row_num
                FROM data_flow.dataflow_log
                WHERE connector_id = :connector_id
            ) AS tmp
            WHERE row_num = 1  and  connector_id = :connector_id -- Giữ duy nhất 1 bản ghi trong mỗi nhóm
        )
        SELECT 
            session_id,
            processing_count,
            processed_count,
            processed_add_count,
            processed_updated_count,
            error_count,
            consume_count,
            start_time,
            end_time,
            connector_name,
            consume_status,
            process_status,
            mode
        FROM (
            SELECT 
                'total_streaming_prev_snapshot' AS session_id,
                SUM(processing_count) AS processing_count,
                SUM(processed_count) AS processed_count,
                SUM(processed_add_count) AS processed_add_count,
                SUM(processed_updated_count) AS processed_updated_count,
                SUM(error_count) AS error_count,
                SUM(consume_count) AS consume_count,
                MIN(start_time) AS start_time,
                MAX(end_time) AS end_time,
                MAX(connector_name) AS connector_name,
                MAX(consume_status) AS consume_status,
                MAX(process_status) AS process_status,
                'streaming' AS mode
            FROM (
                SELECT 
                    sr.session_id,
                    SUM(CASE WHEN sr.state = 'processing' THEN 1 ELSE 0 END) AS processing_count,
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND ((IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') != 'find')
                            OR IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed')
                        THEN 1 ELSE 0 
                    END) AS processed_count,
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                        AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add'
                        THEN 1 ELSE 0 
                    END) AS processed_add_count,
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                        AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update'
                        THEN 1 ELSE 0 
                    END) AS processed_updated_count,
                    SUM(CASE 
                        WHEN sr.state = 'error' 
                            AND (
                                get_json_string(sr.result, '$.profile') IS NULL 
                                OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                                OR (
                                    IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' 
                                    AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find'
                                )
                            ) 
                        THEN 1 ELSE 0 
                    END) AS error_count,
                    SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS consume_count,
                    ps.start_time,
                    ps.end_time,
                    ps.connector_name,
                    ps.consume_status,
                    ps.process_status,
                    ps.mode
                FROM deduplicated_data sr
                INNER JOIN data_flow.pipeline_session ps ON sr.session_id = ps.session_id
                WHERE sr.connector_id = :connector_id
                AND ps.mode = 'streaming'  -- Chỉ lấy các bản ghi có mode là streaming
                AND sr.created_time BETWEEN :start_time AND :end_time
                AND ps.consume_status IN :lst_consume_status
                GROUP BY sr.session_id, ps.start_time, ps.end_time, ps.connector_name,
                        ps.consume_status, ps.process_status, ps.mode
            ) AS streaming_data
            HAVING COUNT(*) > 0
            UNION ALL
            
            SELECT 
                sr.session_id,
                SUM(CASE WHEN sr.state = 'processing' THEN 1 ELSE 0 END) AS processing_count,
                SUM( CASE 
                    WHEN sr.state != 'consume' AND ((IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') != 'find')
                        OR IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                        OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                        OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                        OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                        OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed')
                    THEN 1 ELSE 0 
                END) AS processed_count,
                SUM( CASE 
                    WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                       AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add'
                    THEN 1 ELSE 0 
                END) AS processed_add_count,
                SUM( CASE 
                    WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                       AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update'
                    THEN 1 ELSE 0 
                END) AS processed_updated_count,
                SUM(CASE 
                    WHEN sr.state = 'error' 
                        AND (
                            get_json_string(sr.result, '$.profile') IS NULL 
                            OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                            OR (
                                IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' 
                                AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find'
                            )
                        ) 
                    THEN 1 ELSE 0 
                END) AS error_count,
                SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS consume_count,
                ps.start_time,
                ps.end_time,
                ps.connector_name,
                ps.consume_status,
                ps.process_status,
                ps.mode
            FROM deduplicated_data sr
            INNER JOIN data_flow.pipeline_session ps ON sr.session_id = ps.session_id
            WHERE sr.connector_id = :connector_id
            AND ps.mode = 'batch'  -- Chỉ lấy các bản ghi có mode là batch
            AND sr.created_time BETWEEN :start_time AND :end_time
            AND ps.consume_status IN :lst_consume_status
            GROUP BY sr.session_id, ps.start_time, ps.end_time, ps.connector_name,
                    ps.consume_status, ps.process_status, ps.mode
        ) AS combined_data
        ORDER BY {sort_by} {order_by}
        LIMIT :skip_number, :per_page
        """

        param_query = {
            "connector_id": connector_id,
            "start_time": start_time,
            "end_time": end_time,
            "skip_number": skip_number,
            "per_page": per_page,
            "lst_consume_status": tuple(lst_consume_status),
        }
        results = self.process_query(stmt1, param_query)
        if results:
            return results, self.generate_after_token_by_page_index(page_index + 1)
        return [], ""

    def snapshot_detail_session_by_id(self, merchant_id, session_id, connector_id):
        stmt = f"""
        SELECT 
            sr.session_id,
            SUM(CASE WHEN sr.state = 'processing' THEN sr.count ELSE 0 END) AS processing_count,
            SUM(CASE WHEN sr.state = 'processed' THEN sr.count ELSE 0 END) AS processed_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' THEN sr.count ELSE 0 END) AS processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' THEN sr.count ELSE 0 END) AS processed_updated_count,
            SUM(CASE WHEN sr.state = 'error' THEN sr.count ELSE 0 END) AS error_count,
            SUM(CASE WHEN sr.state = 'consume' THEN sr.count ELSE 0 END) AS consume_count,
            ps.start_time,
            ps.end_time,
            ps.connector_name,
            ps.consume_status,
            ps.process_status 
        FROM data_flow.dataflow_report sr
        INNER JOIN data_flow.pipeline_session ps ON sr.session_id = ps.session_id
        WHERE sr.connector_id = :connector_id AND sr.session_id = :session_id AND sr.object is null
        GROUP BY sr.session_id, ps.start_time, ps.end_time, ps.connector_name,
            ps.consume_status, ps.process_status
        """

        stmt1 = f"""
        WITH deduplicated_data AS (
            SELECT *
            FROM (
                SELECT 
                    session_id, 
                    state, 
                    result, 
                    created_time, 
                    connector_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY session_id, message_id, state, connector_id, merchant_id, reason
                        ORDER BY created_time ASC
                    ) AS row_num
                FROM data_flow.dataflow_log
                WHERE connector_id = :connector_id
            ) AS tmp
            WHERE row_num = 1 -- Giữ duy nhất 1 bản ghi trong mỗi nhóm
        )
        SELECT 
            sr.session_id,
            SUM(CASE WHEN sr.state = 'processing' THEN 1 ELSE 0 END) AS processing_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND ((IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') != 'find')
                    OR IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed')
                THEN 1 ELSE 0 
            END) AS processed_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add'
                THEN 1 ELSE 0 
            END) AS processed_add_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update'
                THEN 1 ELSE 0 
            END) AS processed_updated_count,
            SUM(CASE 
                WHEN sr.state = 'error' 
                    AND (
                        get_json_string(sr.result, '$.profile') IS NULL 
                        OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                        OR (
                            IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' 
                            AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find'
                        )
                    ) 
                THEN 1 ELSE 0 
            END) AS error_count,
            SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS consume_count,
            ps.start_time,
            ps.end_time,
            ps.connector_name,
            ps.consume_status,
            ps.process_status,
            SUM(CASE WHEN sr.state = 'processed' AND json_length(sr.result) > 1  THEN 1 ELSE 0 END) AS event_processed_count,
            SUM(CASE 
                WHEN sr.state = 'error' 
                    AND (
                        IFNULL(JSON_QUERY(sr.result, '$.profile'), '') = ''  
                        OR (
                            JSON_QUERY(sr.result, '$.profile') IS NOT NULL 
                            AND (
                                JSON_LENGTH(sr.result) > 1 
                                OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                            )
                        )
                    )  
                THEN 1 
                ELSE 0 
            END) AS event_error_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND 
                    ((IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'add')
                    OR (IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'add'))
                THEN 1 ELSE 0 
            END) AS object_new_count,

            SUM(CASE 
                WHEN sr.state != 'consume' AND 
                    ((IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'update')
                    OR (IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'update'))
                THEN 1 ELSE 0 
            END) AS object_update_count
            
        FROM deduplicated_data sr
        INNER JOIN data_flow.pipeline_session ps ON sr.session_id = ps.session_id
        WHERE sr.connector_id = :connector_id AND sr.session_id = :session_id 
        GROUP BY sr.session_id, ps.start_time, ps.end_time, ps.connector_name,
            ps.consume_status, ps.process_status
        """
        param_query = {
            "connector_id": connector_id,
            "session_id": session_id,
        }
        results = self.process_query(stmt1, param_query)
        if results:
            return results.first()
        return None

    def detail_total_streaming_prev_snapshot(self, merchant_id, connector_id):
        stmt = f"""
        SELECT
            'total_streaming_prev_snapshot' AS session_id,
            SUM(processing_count) AS processing_count,
            SUM(processed_count) AS processed_count,
            SUM(processed_add_count) AS processed_add_count,
            SUM(processed_updated_count) AS processed_updated_count,
            SUM(error_count) AS error_count,
            SUM(consume_count) AS consume_count,
            MIN(start_time) AS start_time,
            MAX(end_time) AS end_time,
            MAX(connector_name) AS connector_name,
            MAX(consume_status) AS consume_status,
            MAX(process_status) AS process_status
        FROM
            (
                SELECT
                    SUM(CASE WHEN sr.state = 'processing' THEN sr.count ELSE 0 END) AS processing_count,
                    SUM(CASE WHEN sr.state = 'processed' THEN sr.count ELSE 0 END) AS processed_count,
                    SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' THEN sr.count ELSE 0 END) AS processed_add_count,
                    SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' THEN sr.count ELSE 0 END) AS processed_updated_count,
                    SUM(CASE WHEN sr.state = 'error' THEN sr.count ELSE 0 END) AS error_count,
                    SUM(CASE WHEN sr.state = 'consume' THEN sr.count ELSE 0 END) AS consume_count,
                    ps.start_time,
                    ps.end_time,
                    ps.connector_name,
                    ps.consume_status,
                    ps.process_status
                FROM
                    data_flow.dataflow_report sr
                INNER JOIN data_flow.pipeline_session ps ON
                    sr.session_id = ps.session_id
                WHERE
                    sr.connector_id = :connector_id
                    AND ps.mode = 'streaming'
                    AND sr.object IS NULL
                GROUP BY 
                    sr.session_id, 
                    ps.start_time,
                    ps.end_time,
                    ps.connector_name,
                    ps.consume_status, 
                    ps.process_status
            ) AS streaming_data;
        """

        stmt1 = f"""
        WITH deduplicated_data AS (
            SELECT *
            FROM (
                SELECT 
                    session_id, 
                    state, 
                    result, 
                    created_time, 
                    connector_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY session_id, message_id, state, connector_id, merchant_id, reason
                        ORDER BY created_time ASC
                    ) AS row_num
                FROM data_flow.dataflow_log
                WHERE connector_id = :connector_id
            ) AS tmp
            WHERE row_num = 1 -- Giữ duy nhất 1 bản ghi trong mỗi nhóm
        )
            SELECT
            'total_streaming_prev_snapshot' AS session_id,
            SUM(processing_count) AS processing_count,
            SUM(processed_count) AS processed_count,
            SUM(processed_add_count) AS processed_add_count,
            SUM(processed_updated_count) AS processed_updated_count,
            SUM(error_count) AS error_count,
            SUM(consume_count) AS consume_count,
            MIN(start_time) AS start_time,
            MAX(end_time) AS end_time,
            MAX(connector_name) AS connector_name,
            MAX(consume_status) AS consume_status,
            MAX(process_status) AS process_status,
            SUM(event_processed_count) AS event_processed_count,
            SUM(event_error_count) AS event_error_count,
            SUM(object_new_count) AS object_new_count,
            SUM(object_update_count) AS object_update_count
            FROM
                (
                    SELECT
                        SUM(CASE WHEN sr.state = 'processing' THEN 1 ELSE 0 END) AS processing_count,
                        SUM(CASE 
                            WHEN sr.state != 'consume' AND ((IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') != 'find')
                                OR IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                                OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                                OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                                OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                                OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed')
                            THEN 1 ELSE 0 
                        END) AS processed_count,
                        SUM(CASE 
                            WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                                AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add'
                            THEN 1 ELSE 0 
                        END) AS processed_add_count,
                        SUM(CASE 
                            WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                                AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update'
                            THEN 1 ELSE 0 
                        END) AS processed_updated_count,
                        SUM(CASE 
                            WHEN sr.state = 'error' 
                                AND (
                                    get_json_string(sr.result, '$.profile') IS NULL 
                                    OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                                    OR (
                                        IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' 
                                        AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find'
                                    )
                                ) 
                            THEN 1 ELSE 0 
                        END) AS error_count,
                        SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS consume_count,
                        ps.start_time,
                        ps.end_time,
                        ps.connector_name,
                        ps.consume_status,
                        ps.process_status,
                        SUM(CASE WHEN sr.state = 'processed' AND json_length(sr.result) > 1  THEN 1 ELSE 0 END) AS event_processed_count,
                        SUM(CASE 
                            WHEN sr.state = 'error' 
                                AND (JSON_QUERY(sr.result, '$.profile') IS NULL  
                                    OR (JSON_QUERY(sr.result, '$.profile') IS NOT NULL 
                                        AND JSON_LENGTH(sr.result) > 1))  
                            THEN 1 
                            ELSE 0 
                        END) AS event_error_count,
                        SUM(CASE 
                            WHEN sr.state != 'consume' AND 
                                ((IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                                AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'add')
                                OR (IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                                AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'add'))
                            THEN 1 ELSE 0 
                        END) AS object_new_count,

                        SUM(CASE 
                            WHEN sr.state != 'consume' AND 
                                ((IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                                AND IFNULL(get_json_string(sr.result, '$.dynamic_event.action'), '') = 'update')
                                OR (IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                                AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'update'))
                            THEN 1 ELSE 0 
                        END) AS object_update_count
            
                    FROM
                        deduplicated_data sr
                    INNER JOIN data_flow.pipeline_session ps ON
                        sr.session_id = ps.session_id
                    WHERE
                        sr.connector_id = :connector_id
                        AND ps.mode = 'streaming'
                    GROUP BY 
                        sr.session_id, 
                        ps.start_time,
                        ps.end_time,
                        ps.connector_name,
                        ps.consume_status, 
                        ps.process_status
                ) AS streaming_data;
        """
        param_query = {
            "connector_id": connector_id,
        }
        results = self.process_query(stmt1, param_query)
        if results:
            return results.first()
        return None

    def streaming_history_list_session_of_connector(
        self,
        merchant_id,
        mapping_state,
        connector_id,
        start_time,
        end_time,
        per_page,
        before_token,
        sort_by,
        order_by,
    ):

        skip_number = 0
        page_index = 0
        parse_token = self.parse_token(before_token)
        if parse_token:
            page_index = parse_token.get("page_index", 0)
            skip_number = int(page_index) * int(per_page)

        stmt = f"""    
        SELECT
            sr.date,
            SUM(CASE WHEN sr.state = 'processing' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processing_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processed_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'profile' THEN sr.count ELSE 0 END) AS profile_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'profile' THEN sr.count ELSE 0 END) AS profile_processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'company' THEN sr.count ELSE 0 END) AS company_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'company' THEN sr.count ELSE 0 END) AS company_processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'ticket' THEN sr.count ELSE 0 END) AS ticket_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'ticket' THEN sr.count ELSE 0 END) AS ticket_processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'deal' THEN sr.count ELSE 0 END) AS sale_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'deal' THEN sr.count ELSE 0 END) AS sale_processed_update_count,
            SUM(CASE WHEN sr.state = 'error' AND sr.object is NULL THEN sr.count ELSE 0 END) AS error_count,
            SUM(CASE WHEN sr.state = 'consume' AND sr.object is NULL THEN sr.count ELSE 0 END) AS consume_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.object = 'dynamic_event' THEN sr.count ELSE 0 END) AS total_event_done,
            SUM(CASE WHEN sr.state = 'error' AND sr.object = 'dynamic_event' THEN sr.count ELSE 0 END) AS total_event_fail,
            SUM(CASE WHEN sr.state = 'processed' AND sr.object = 'product_holding' THEN sr.count ELSE 0 END) AS total_product_holding_done,
            SUM(CASE WHEN sr.state = 'error' AND sr.object = 'product_holding' THEN sr.count ELSE 0 END) AS total_product_holding_fail,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'product_holding' THEN sr.count ELSE 0 END) AS total_product_holding_add,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'product_holding' THEN sr.count ELSE 0 END) AS total_product_holding_update
        FROM
            data_flow.dataflow_report sr
        WHERE
            sr.connector_id = :connector_id
            AND STR_TO_DATE(CONCAT(sr.date, ' ', sr.hour, ':', sr.minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
        GROUP BY 
            sr.date
        ORDER BY 
            {sort_by} {order_by}
        LIMIT 
            :skip_number, :per_page;        
        """

        stmt1 = f"""
        WITH deduplicated_data AS (
                SELECT *
                FROM (
                    SELECT 
                        session_id, 
                        state, 
                        result, 
                        created_time, 
                        connector_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY session_id, message_id, state, connector_id, merchant_id, reason
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM data_flow.dataflow_log
                    WHERE connector_id = :connector_id
                ) AS tmp
                WHERE row_num = 1 -- Giữ duy nhất 1 bản ghi trong mỗi nhóm
            )
        SELECT
            FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(sr.created_time) / 86400) * 86400) AS date,
            SUM(CASE WHEN sr.state = 'processing' THEN 1 ELSE 0 END) AS processing_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND ((IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') != 'find')
                    OR IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed')
                THEN 1 ELSE 0 
            END) AS processed_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add'
                THEN 1 ELSE 0 
            END) AS profile_processed_add_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update'
                THEN 1 ELSE 0 
            END) AS profile_processed_update_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.company.action'), '') = 'add'
                THEN 1 ELSE 0 
            END) AS company_processed_add_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.company.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.company.action'), '') = 'update'
                THEN 1 ELSE 0 
            END) AS company_processed_update_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.ticket.action'), '') = 'add'
                THEN 1 ELSE 0 
            END) AS ticket_processed_add_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.ticket.action'), '') = 'update'
                THEN 1 ELSE 0 
            END) AS ticket_processed_update_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.sale.action'), '') = 'add'
                THEN 1 ELSE 0 
            END) AS sale_processed_add_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.sale.action'), '') = 'update'
                THEN 1 ELSE 0 
            END) AS sale_processed_update_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.deal.action'), '') = 'add'
                THEN 1 ELSE 0 
            END) AS deal_processed_add_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.deal.action'), '') = 'update'
                THEN 1 ELSE 0 
            END) AS deal_processed_update_count,
            SUM(CASE 
                WHEN sr.state = 'error' 
                    AND (
                        get_json_string(sr.result, '$.profile') IS NULL 
                        OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                        OR (
                            IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' 
                            AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find'
                        )
                    ) 
                THEN 1 ELSE 0 
            END) AS error_count,
            SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS consume_count,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                THEN 1 ELSE 0 
            END) AS total_event_done,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'error'
                THEN 1 ELSE 0 
            END) AS total_event_fail,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                THEN 1 ELSE 0 
            END) AS total_product_holding_done,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'error'
                THEN 1 ELSE 0 
            END) AS total_product_holding_fail,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') = 'add'
                THEN 1 ELSE 0 
            END) AS total_product_holding_add,
            SUM(CASE 
                WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                    AND IFNULL(get_json_string(sr.result, '$.product_holding.action'), '') IN ('upsert', 'update')
                THEN 1 ELSE 0 
            END) AS total_product_holding_update
        FROM deduplicated_data sr    
        WHERE
            sr.created_time BETWEEN :start_time AND :end_time
        GROUP BY 
            date
        ORDER BY 
            {sort_by} {order_by}
        LIMIT 
            :skip_number, :per_page;    
        """
        results = self.process_query(
            stmt1,
            {
                "connector_id": connector_id,
                "start_time": start_time,
                "end_time": end_time,
                "sort_by": sort_by,
                "skip_number": skip_number,
                "per_page": per_page,
            },
        )
        if results:
            return results, self.generate_after_token_by_page_index(page_index + 1)
        return [], ""

    def __build_query_sum_state_snapshot(self, mapping_state):
        build_sum_by_state = ""
        for state in mapping_state.keys():
            state_query = state if isinstance(state, int) else '"{}"'.format(state)
            build_sum_by_state += f"SUM(CASE WHEN state = {state_query} THEN count ELSE 0 END) AS {state}_count"
            if state != list(mapping_state.keys())[-1]:
                build_sum_by_state += ", "
        return build_sum_by_state

    def __build_query_where_snapshot(self, connector_id, start_time=None, end_time=None):
        result_query_where = f"connector_id={connector_id}"
        if start_time and end_time:
            result_query_where += f"""
            AND STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') BETWEEN "{start_time}" AND "{end_time}"
            """
        return result_query_where

    def snapshot_total_session_of_connector_by_range_time(
        self, merchant_id, mapping_state, connector_id, start_time, end_time, lst_consume_status
    ):
        stmt = f"""
        SELECT
            COUNT(DISTINCT CASE WHEN mode = 'streaming' THEN 'streaming_group' ELSE session_id END) AS total_session,
            consume_status,
            MAX(mode) AS mode,
            MIN(start_time) AS start_time,
            MAX(end_time) AS end_time
        FROM (
            SELECT
                sr.session_id as session_id,
                ps.consume_status as consume_status,
                ps.mode as mode,
                ps.start_time as start_time,
                ps.end_time as end_time
            FROM
                data_flow.dataflow_report sr
            INNER JOIN data_flow.pipeline_session ps ON sr.session_id = ps.session_id
            WHERE
                sr.connector_id = :connector_id
                AND sr.object IS NULL
                AND ps.start_time BETWEEN :start_time AND :end_time
                AND ps.consume_status IN :lst_consume_status
        ) AS subquery
        GROUP BY
            CASE 
                WHEN mode = 'streaming' THEN 'streaming_group'
                ELSE session_id
            END,
            consume_status
        ORDER BY
            MIN(start_time) DESC;
        """

        param_query = {
            "connector_id": connector_id,
            "start_time": start_time,
            "end_time": end_time,
            "lst_consume_status": lst_consume_status,
        }

        return self.process_query(stmt, param_query)

    def streaming_realtime_result(
        self, merchant_id, connector_id, mapping_state, start_time, end_time, time_step, time_unit
    ):
        stmt = f"""
        SELECT 
            SUM(CASE WHEN state = 'consume' THEN count ELSE 0 END) AS consume_count,
            SUM(CASE WHEN state = 'processing' THEN count ELSE 0 END) AS processing_count,
            SUM(CASE WHEN state = 'processed' THEN count ELSE 0 END) AS processed_count,
            SUM(CASE WHEN state = 'error' THEN count ELSE 0 END) AS error_count,
            CASE
                WHEN MINUTE(timestamp) BETWEEN 0 AND 14 THEN DATE_FORMAT(timestamp, '%Y-%m-%d %H:15')
                WHEN MINUTE(timestamp) BETWEEN 15 AND 29 THEN DATE_FORMAT(timestamp, '%Y-%m-%d %H:30')
                WHEN MINUTE(timestamp) BETWEEN 30 AND 44 THEN DATE_FORMAT(timestamp, '%Y-%m-%d %H:45')
                WHEN MINUTE(timestamp) BETWEEN 45 AND 59 THEN DATE_FORMAT(DATE_ADD(timestamp, INTERVAL 1 HOUR), '%Y-%m-%d %H:00')
            END AS time_query
        FROM (
            SELECT
                state,
                count,
                STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') AS timestamp,
                FLOOR(UNIX_TIMESTAMP(STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i')) / 900) AS group_id
            FROM data_flow.dataflow_report
            WHERE connector_id = :connector_id
                AND STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
                AND object is null
        ) AS subquery
        GROUP BY 
            group_id, time_query
        ORDER BY 
            group_id;
        """
        MobioLogging().info(
            "Streaming realtime result param {}".format(
                {"connector_id": connector_id, "start_time": start_time, "end_time": end_time}
            )
        )
        return self.process_query(stmt, {"connector_id": connector_id, "start_time": start_time, "end_time": end_time})

    def streaming_realtime_result_by_status(self, merchant_id, connector_id, mapping_state, start_time, end_time):
        stmt = f"""
        SELECT 
            SUM(CASE WHEN state = 'processing' THEN count ELSE 0 END) AS processing_count,
            SUM(CASE WHEN state = 'processed' THEN count ELSE 0 END) AS processed_count,
            SUM(CASE WHEN state = 'error' THEN count ELSE 0 END) AS error_count,
            MIN(CASE WHEN state = 'consume' THEN STR_TO_DATE(CONCAT(`date`, ' ', `hour`, ':', `minute`), '%Y-%m-%d %H:%i') END) AS first_time,
            MAX(CASE WHEN state = 'consume' THEN STR_TO_DATE(CONCAT(`date`, ' ', `hour`, ':', `minute`), '%Y-%m-%d %H:%i') END) AS last_time
        FROM data_flow.dataflow_report
        WHERE connector_id = :connector_id
            AND STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
            AND object is null
        """
        result = self.process_query(
            stmt, param_query={"connector_id": connector_id, "start_time": start_time, "end_time": end_time}
        )
        if result:
            result = result.first()
            return result._asdict() if result else None
        return None

    def streaming_realtime_result_by_object(self, merchant_id, connector_id, mapping_state, start_time, end_time):
        stmt = f"""
        SELECT 
            object, 
            SUM(CASE WHEN action = 'add' THEN count ELSE 0 END) AS add_count,
            SUM(CASE WHEN action = 'update' THEN count ELSE 0 END) AS updated_count
        FROM data_flow.dataflow_report
        WHERE connector_id = :connector_id
            AND STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
            AND state="processed"
            AND object is not null
        GROUP BY
            object
        """
        result = self.process_query(
            stmt,
            param_query={"connector_id": connector_id, "start_time": start_time, "end_time": end_time},
        )
        if result:
            return result
        return None

    def streaming_history_result_by_object(self, merchant_id, connector_id, mapping_state, start_time, end_time):
        stmt = f"""
        SELECT 
            object,
            date,
            SUM(CASE WHEN state = 'processed' and action = 'add' THEN count ELSE 0 END) AS process_add,
            SUM(CASE WHEN state = 'processed' and action = 'update' THEN count ELSE 0 END) AS process_updated
        FROM data_flow.dataflow_report
        WHERE connector_id = :connector_id
            AND STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
            AND object is not null
        GROUP BY
            date, object
        ORDER BY date desc
        """
        result = self.process_query(
            stmt, {"connector_id": connector_id, "start_time": start_time, "end_time": end_time}
        )
        if result:
            return result
        return None

    def streaming_history_total_pipeline(self, merchant_id, connector_id, mapping_state, start_time, end_time):
        stmt = f"""
     	SELECT 
     		date,
            SUM(CASE WHEN state = 'error' THEN count ELSE 0 END) AS total_process_fail,
            SUM(CASE WHEN state = 'processed' THEN count ELSE 0 END) AS total_process_done
        FROM data_flow.dataflow_report
        WHERE connector_id = :connector_id
            AND STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
            AND object is null
        GROUP BY 
        	date
        ORDER BY date desc
        """

        stmt1 = f"""
        WITH deduplicated_data AS (
                SELECT *
                FROM (
                    SELECT 
                        session_id, 
                        state, 
                        result, 
                        created_time, 
                        connector_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY session_id, message_id, state, connector_id, merchant_id, reason
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM data_flow.dataflow_log
                    WHERE connector_id = :connector_id
                ) AS tmp
                WHERE row_num = 1 -- Giữ duy nhất 1 bản ghi trong mỗi nhóm
            )
        SELECT
            FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(sr.created_time) / 86400) * 86400) AS date,
            SUM(CASE 
                WHEN sr.state != 'consume' AND ((IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') != 'find')
                    OR IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                    OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed')
                THEN 1 ELSE 0 
            END) AS total_process_done,
            
            SUM(CASE 
                WHEN sr.state = 'error' 
                    AND (
                        get_json_string(sr.result, '$.profile') IS NULL 
                        OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                        OR (
                            IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' 
                            AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find'
                        )
                    ) 
                THEN 1 ELSE 0 
            END) AS total_process_fail
            
        FROM deduplicated_data sr    
        WHERE
            sr.created_time BETWEEN :start_time AND :end_time
        GROUP BY 
            date;
        """

        result = self.process_query(
            stmt1, {"connector_id": connector_id, "start_time": start_time, "end_time": end_time}
        )
        if result:
            return result
        return None

    def select_last_time_process_by_connector_ids(self, connector_ids):
        stmt = """
            SELECT 
                connector_id,
                MAX(STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i')) AS max_datetime
            FROM data_flow.dataflow_report
            WHERE connector_id IN :connector_ids
                AND object is null
            GROUP BY 
                connector_id
            ORDER BY max_datetime DESC;
        """
        result = self.process_query(stmt, {"connector_ids": connector_ids})
        if result:
            return result
        return None

    def select_last_time_process_by_connector_ids_data_out(self, connector_ids):
        stmt = """
            SELECT connector_id, MAX(next_time) as max_datetime
            FROM data_out.send_event
            WHERE connector_id IN :connector_ids AND status = 1
            GROUP BY 
                connector_id
            ORDER BY max_datetime DESC;
        """
        result = self.process_query(stmt, {"connector_ids": connector_ids})
        if result:
            return result
        return None

    def report_connector_in_range_time(self, start_time, end_time):
        stmt = f"""    
        SELECT
            sr.connector_id,
            SUM(CASE WHEN sr.state = 'processing' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processing_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processed_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'profile' THEN sr.count ELSE 0 END) AS profile_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'profile' THEN sr.count ELSE 0 END) AS profile_processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'company' THEN sr.count ELSE 0 END) AS company_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'company' THEN sr.count ELSE 0 END) AS company_processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'ticket' THEN sr.count ELSE 0 END) AS ticket_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'ticket' THEN sr.count ELSE 0 END) AS ticket_processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'deal' THEN sr.count ELSE 0 END) AS sale_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'deal' THEN sr.count ELSE 0 END) AS sale_processed_update_count,
            SUM(CASE WHEN sr.state = 'error' AND sr.object is NULL THEN sr.count ELSE 0 END) AS error_count,
            SUM(CASE WHEN sr.state = 'consume' AND sr.object is NULL THEN sr.count ELSE 0 END) AS consume_count,
            MIN(STR_TO_DATE(CONCAT(sr.date, ' ', sr.hour, ':', sr.minute), '%Y-%m-%d %H:%i')) AS start_time
        FROM
            data_flow.dataflow_report sr
        WHERE
            STR_TO_DATE(CONCAT(sr.date, ' ', sr.hour, ':', sr.minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
        GROUP BY 
            sr.connector_id    
        """
        results = self.process_query(
            stmt,
            {
                "start_time": start_time,
                "end_time": end_time,
            },
        )
        if results:
            return results
        return []

    def report_connector_in_ids(self, connector_ids):
        stmt = f"""    
        SELECT
            sr.connector_id,
            SUM(CASE WHEN sr.state = 'processing' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processing_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processed_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'profile' THEN sr.count ELSE 0 END) AS profile_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object is NULL THEN sr.count ELSE 0 END) AS processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'profile' THEN sr.count ELSE 0 END) AS profile_processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'company' THEN sr.count ELSE 0 END) AS company_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'company' THEN sr.count ELSE 0 END) AS company_processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'ticket' THEN sr.count ELSE 0 END) AS ticket_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'ticket' THEN sr.count ELSE 0 END) AS ticket_processed_update_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'add' AND sr.object = 'deal' THEN sr.count ELSE 0 END) AS sale_processed_add_count,
            SUM(CASE WHEN sr.state = 'processed' AND sr.action = 'update' AND sr.object = 'deal' THEN sr.count ELSE 0 END) AS sale_processed_update_count,
            SUM(CASE WHEN sr.state = 'error' AND sr.object is NULL THEN sr.count ELSE 0 END) AS error_count,
            SUM(CASE WHEN sr.state = 'consume' AND sr.object is NULL THEN sr.count ELSE 0 END) AS consume_count,
            MIN(STR_TO_DATE(CONCAT(sr.date, ' ', sr.hour, ':', sr.minute), '%Y-%m-%d %H:%i')) AS start_time
        FROM
            data_flow.dataflow_report sr
        WHERE
            sr.connector_id IN :connector_ids
        GROUP BY 
            sr.connector_id    
        """
        results = self.process_query(
            stmt,
            {
                "connector_ids": connector_ids,
            },
        )
        if results:
            return results
        return []

    def snapshot_result_running_session_of_connector(
        self,
        connector_id,
        start_time,
        end_time,
        lst_consume_status,
    ):
        stmt = f"""
        WITH deduplicated_data AS (
            SELECT *
            FROM (
                SELECT 
                    session_id, 
                    state, 
                    result, 
                    created_time, 
                    connector_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY session_id, message_id, state,  connector_id, merchant_id, reason, action
                        ORDER BY created_time DESC
                    ) AS row_num
                FROM data_flow.dataflow_log
                WHERE connector_id = :connector_id
            ) AS tmp
            WHERE row_num = 1 -- Giữ duy nhất 1 bản ghi trong mỗi nhóm
        )
        SELECT 
            dl.session_id, 
            FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(dl.created_time) / 900) * 900) AS time_group, 
            COUNT(*) AS total_consume
            FROM deduplicated_data dl
            INNER JOIN data_flow.pipeline_session ps
            ON dl.session_id = ps.session_id
            WHERE 
                dl.connector_id = :connector_id 
                AND dl.state = 'consume' 
                AND ps.mode = 'batch'
                AND ps.consume_status IN :lst_consume_status
                AND dl.created_time BETWEEN :start_time AND :end_time
            GROUP BY dl.session_id, time_group
            ORDER BY dl.session_id, time_group;    
        """

        stmt1 = """
            SELECT 
                session_id,
                SUM(CASE WHEN state = 'consume' THEN count ELSE 0 END) AS total_consume,
                CASE
                    WHEN MINUTE(timestamp) BETWEEN 0 AND 14 THEN DATE_FORMAT(timestamp, '%Y-%m-%d %H:15:00')
                    WHEN MINUTE(timestamp) BETWEEN 15 AND 29 THEN DATE_FORMAT(timestamp, '%Y-%m-%d %H:30:00')
                    WHEN MINUTE(timestamp) BETWEEN 30 AND 44 THEN DATE_FORMAT(timestamp, '%Y-%m-%d %H:45:00')
                    WHEN MINUTE(timestamp) BETWEEN 45 AND 59 THEN DATE_FORMAT(DATE_ADD(timestamp, INTERVAL 1 HOUR), '%Y-%m-%d %H:00')
                END AS time_query
            FROM (
                SELECT
                    dl.session_id,
                    dl.state,
                    dl.count,
                    STR_TO_DATE(CONCAT(dl.date, ' ', dl.hour, ':', dl.minute), '%Y-%m-%d %H:%i') AS timestamp,
                    FLOOR(UNIX_TIMESTAMP(STR_TO_DATE(CONCAT(dl.date, ' ', dl.hour, ':', dl.minute), '%Y-%m-%d %H:%i')) / 900) AS group_id
                FROM data_flow.dataflow_report dl
                INNER JOIN data_flow.pipeline_session ps
                ON dl.session_id = ps.session_id         
                WHERE dl.connector_id = :connector_id
                    AND STR_TO_DATE(CONCAT(dl.date, ' ', dl.hour, ':', dl.minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
                    AND dl.object is null
                    AND dl.state = 'consume' 
            ) AS subquery
            GROUP BY 
                group_id, time_query, session_id
            ORDER BY 
                group_id;
        """

        param_query = {
            "connector_id": connector_id,
            "start_time": start_time,
            "end_time": end_time,
            "lst_consume_status": tuple(lst_consume_status),
        }
        return self.process_query(stmt1, param_query)

    def snapshot_total_sessions_by_status_every_day(self, connector_id, start_time, end_time, lst_consume_status):
        stmt = """
            SELECT
                FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(ps.end_time) / 86400) * 86400) AS date_group,
                COUNT(DISTINCT CASE 
                    WHEN ps.mode = 'batch' THEN dl.session_id
                    WHEN ps.mode = 'streaming' THEN CONCAT(ps.mode, '_', FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(ps.end_time) / 86400) * 86400))
                END) AS total_done_sessions
            FROM data_flow.dataflow_report dl
            INNER JOIN data_flow.pipeline_session ps
            ON dl.session_id = ps.session_id
            WHERE 
                dl.connector_id = :connector_id
                AND ps.consume_status IN :lst_consume_status
                AND ps.end_time BETWEEN :start_time AND :end_time
            GROUP BY date_group
            ORDER BY date_group;
        """
        param_query = {
            "connector_id": connector_id,
            "start_time": start_time,
            "end_time": end_time,
            "lst_consume_status": tuple(lst_consume_status),
        }

        return self.process_query(stmt, param_query)

    def streaming_realtime_result_by_object_event(
        self, merchant_id, connector_id, mapping_state, start_time, end_time, time_step, time_unit
    ):
        stmt = f"""
            SELECT 
                SUM(CASE WHEN state = 'processed' AND object = 'profile' AND action = 'add' THEN count ELSE 0 END) AS profile_add_count,
                SUM(CASE WHEN state = 'processed' AND object = 'dynamic_event' THEN count ELSE 0 END) AS dynamic_event_total_rows_success,
                SUM(CASE WHEN state = 'error' AND object = 'dynamic_event' THEN count ELSE 0 END) AS dynamic_event_total_rows_error,
                SUM(CASE WHEN state = 'processed' AND object = 'product_holding' THEN count ELSE 0 END) AS product_holding_total_rows_success,
                SUM(CASE WHEN state = 'error' AND object = 'product_holding' THEN count ELSE 0 END) AS product_holding_total_rows_error,
                CASE
                    WHEN MINUTE(timestamp) BETWEEN 0 AND 14 THEN DATE_FORMAT(timestamp, '%Y-%m-%d %H:15')
                    WHEN MINUTE(timestamp) BETWEEN 15 AND 29 THEN DATE_FORMAT(timestamp, '%Y-%m-%d %H:30')
                    WHEN MINUTE(timestamp) BETWEEN 30 AND 44 THEN DATE_FORMAT(timestamp, '%Y-%m-%d %H:45')
                    WHEN MINUTE(timestamp) BETWEEN 45 AND 59 THEN DATE_FORMAT(DATE_ADD(timestamp, INTERVAL 1 HOUR), '%Y-%m-%d %H:00')
                END AS time_query
            FROM (
                SELECT
                    state,
                    count,
                    object,
                    action,
                    STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') AS timestamp,
                    FLOOR(UNIX_TIMESTAMP(STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i')) / 900) AS group_id
                FROM data_flow.dataflow_report
                WHERE connector_id = :connector_id
                    AND STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
                    AND object is not null
            ) AS subquery
            GROUP BY 
                group_id, time_query
            ORDER BY 
                group_id;
            """
        MobioLogging().info(
            "Streaming realtime result by object event param {}".format(
                {"connector_id": connector_id, "start_time": start_time, "end_time": end_time}
            )
        )
        return self.process_query(stmt, {"connector_id": connector_id, "start_time": start_time, "end_time": end_time})

    def streaming_history_result_by_object_event(
        self, merchant_id, connector_id, mapping_state, start_time, end_time, time_step, time_unit
    ):
        stmt = f"""
            SELECT 
                SUM(CASE WHEN state = 'processed' AND object = 'profile' AND action = 'add' THEN count ELSE 0 END) AS profile_add_count,
                SUM(CASE WHEN state = 'processed' AND object != 'profile' THEN count ELSE 0 END) AS event_processed_count,
                SUM(CASE WHEN state = 'error' AND object != 'profile' THEN count ELSE 0 END) AS event_error_count,
                SUM(CASE WHEN state = 'processed' AND object = 'profile' AND action = 'update' THEN count ELSE 0 END) AS profile_update_count,
                date
            FROM (
                SELECT
                    state,
                    count,
                    object,
                    action,
                    date
                FROM data_flow.dataflow_report
                WHERE connector_id = :connector_id
                    AND STR_TO_DATE(CONCAT(date, ' ', hour, ':', minute), '%Y-%m-%d %H:%i') BETWEEN :start_time AND :end_time
                    AND object is not null
            ) AS subquery
            GROUP BY 
                date
            ORDER BY 
                date;
            """

        stmt1 = f"""
            WITH deduplicated_data AS (
                SELECT *
                FROM (
                    SELECT 
                        session_id, 
                        state, 
                        result, 
                        created_time, 
                        connector_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY session_id, message_id, state, connector_id, merchant_id, reason
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM data_flow.dataflow_log
                    WHERE connector_id = :connector_id
                ) AS tmp
                WHERE row_num = 1 -- Giữ duy nhất 1 bản ghi trong mỗi nhóm
            )
                SELECT 
                SUM(CASE 
                        WHEN sr.state != 'consume' AND ((IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') != 'find')
                            OR IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed')
                        THEN 1 ELSE 0 
                    END) AS processed_count,
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                            AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add'
                        THEN 1 ELSE 0 
                    END) AS processed_add_count,
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                            AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update'
                        THEN 1 ELSE 0 
                    END) AS processed_updated_count,
                    SUM(CASE 
                        WHEN sr.state = 'error' 
                            AND (
                                get_json_string(sr.result, '$.profile') IS NULL 
                                OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                                OR (
                                    IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' 
                                    AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find'
                                )
                            ) 
                        THEN 1 ELSE 0 
                    END) AS error_count,
                    SUM(CASE 
                        WHEN state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                            AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add'
                        THEN 1 ELSE 0 
                    END) AS profile_add_count,
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed'
                        THEN 1 ELSE 0 
                    END) AS event_processed_count,                    
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'error'
                            OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'error'
                            OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'error'
                            OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'error'
                            OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'error'
                        THEN 1 ELSE 0 
                    END) AS event_error_count,
                    FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(created_time) / 86400) * 86400) AS date
                
                FROM deduplicated_data sr
                WHERE connector_id = :connector_id
                    AND created_time BETWEEN :start_time AND :end_time
                    AND result is not null
        
                GROUP BY 
                    date
                ORDER BY 
                    date;
            """

        MobioLogging().info(
            "Streaming history result  by object event param {}".format(
                {"connector_id": connector_id, "start_time": start_time, "end_time": end_time}
            )
        )
        return self.process_query(stmt1, {"connector_id": connector_id, "start_time": start_time, "end_time": end_time})

    def streaming_history_result_from_source(self, merchant_id, connector_id, mapping_state, start_time, end_time):
        stmt = f"""
        WITH deduplicated_data AS (
            SELECT *
            FROM (
                SELECT 
                    session_id, 
                    state, 
                    result, 
                    created_time, 
                    connector_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY session_id, message_id, state, connector_id, merchant_id, reason
                        ORDER BY created_time ASC
                    ) AS row_num
                FROM data_flow.dataflow_log
                WHERE connector_id = :connector_id
            ) AS tmp
            WHERE row_num = 1 -- Giữ duy nhất 1 bản ghi trong mỗi nhóm
        )
     	SELECT 
            SUM(CASE WHEN state = 'consume' THEN 1 ELSE 0 END) AS total_profile_count,
            SUM(CASE 
                WHEN state = 'error' 
                    AND (
                        get_json_string(result, '$.profile') IS NULL 
                        OR IFNULL(get_json_string(result, '$.profile.state'), '') = 'error'
                        OR (
                            IFNULL(get_json_string(result, '$.profile.state'), '') = 'processed' 
                            AND IFNULL(get_json_string(result, '$.profile.action'), '') = 'find'
                        )
                    ) 
                THEN 1 ELSE 0 
            END) AS total_process_fail,
            SUM(CASE 
                WHEN state != 'consume' AND ((IFNULL(get_json_string(result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(result, '$.profile.action'), '') != 'find')
                    OR IFNULL(get_json_string(result, '$.dynamic_event.state'), '') = 'processed'
                    OR IFNULL(get_json_string(result, '$.product_holding.state'), '') = 'processed'
                    OR IFNULL(get_json_string(result, '$.ticket.state'), '') = 'processed'
                    OR IFNULL(get_json_string(result, '$.sale.state'), '') = 'processed'
                    OR IFNULL(get_json_string(result, '$.deal.state'), '') = 'processed')
                THEN 1 ELSE 0 
            END) AS total_process_done,
            SUM(CASE 
                WHEN state != 'consume' AND IFNULL(get_json_string(result, '$.profile.state'), '') = 'processed'
                AND IFNULL(get_json_string(result, '$.profile.action'), '') = 'add'
                THEN 1 ELSE 0 
            END) AS total_add_profile
        FROM deduplicated_data
        WHERE connector_id = :connector_id
            AND created_time BETWEEN :start_time AND :end_time
        """
        result = self.process_query(
            stmt, {"connector_id": connector_id, "start_time": start_time, "end_time": end_time}
        )
        if result:
            return result
        return None
    
    
    def get_data_log_by_session_id(self, session_id):
        stmt = f"""    
            SELECT dfl.session_id, dfl.message_id, dfl.created_time , dfl.event_value , dfl.state ,dfl.reason ,dfl.`action` ,dfl.`result` 
            FROM
                data_flow.dataflow_log dfl
            WHERE
                dfl.session_id = :session_id
            ORDER BY 
                dfl.created_time ASC 
        """
        results = self.process_query(
            stmt,
            {
                "session_id": session_id,
            },
        )
        if results:
            return results
        return []
    
    def snapshot_total_consume_by_session(self, merchant_id, connector_id, session_id, start_time, end_time):
        stmt = f"""
            SELECT count(*) AS total_consume
            FROM data_flow.dataflow_log
            WHERE session_id = :session_id AND state = 'consume' AND created_time BETWEEN :start_time AND :end_time
        """
        result = self.process_query(stmt, {"session_id": session_id, "start_time": start_time, "end_time": end_time})
        if result:
            return result
        return None

    def agg_report_df_log_to_daily_report(self, start_time, end_time, page, per_page):
        stmt = f"""
            WITH deduplicated_data AS (
                SELECT *
                FROM (
                SELECT 
                    session_id, 
                    state, 
                    result, 
                    created_time, 
                    connector_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY session_id, message_id, state, connector_id, merchant_id, reason
                        ORDER BY created_time ASC
                    ) AS row_num
                FROM data_flow.dataflow_log
            ) AS tmp
            WHERE row_num = 1
        )
        SELECT 
            session_id,
            processing_count,
            processed_count,
            processed_add_count,
            processed_updated_count,
            error_count,
            consume_count,
            start_time,
            end_time,
            connector_name,
            consume_status,
            process_status,
            mode
        FROM (
            SELECT 
                'total_streaming_prev_snapshot' AS session_id,
                SUM(processing_count) AS processing_count,
                SUM(processed_count) AS processed_count,
                SUM(processed_add_count) AS processed_add_count,
                SUM(processed_updated_count) AS processed_updated_count,
                SUM(error_count) AS error_count,
                SUM(consume_count) AS consume_count,
                MIN(start_time) AS start_time,
                MAX(end_time) AS end_time,
                MAX(connector_name) AS connector_name,
                MAX(consume_status) AS consume_status,
                MAX(process_status) AS process_status,
                'streaming' AS mode
            FROM (
                SELECT 
                    sr.session_id,
                    SUM(CASE WHEN sr.state = 'processing' THEN 1 ELSE 0 END) AS processing_count,
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND ((IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') != 'find')
                            OR IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                            OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed')
                        THEN 1 ELSE 0 
                    END) AS processed_count,
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                        AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add'
                        THEN 1 ELSE 0 
                    END) AS processed_add_count,
                    SUM(CASE 
                        WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                        AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update'
                        THEN 1 ELSE 0 
                    END) AS processed_updated_count,
                    SUM(CASE 
                        WHEN sr.state = 'error' 
                            AND (
                                get_json_string(sr.result, '$.profile') IS NULL 
                                OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                                OR (
                                    IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' 
                                    AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find'
                                )
                            ) 
                        THEN 1 ELSE 0 
                    END) AS error_count,
                    SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS consume_count,
                    ps.start_time,
                    ps.end_time,
                    ps.connector_name,
                    ps.consume_status,
                    ps.process_status,
                    ps.mode
                FROM deduplicated_data sr
                INNER JOIN data_flow.pipeline_session ps ON sr.session_id = ps.session_id
                AND ps.mode = 'streaming'  -- Chỉ lấy các bản ghi có mode là streaming
                AND sr.created_time BETWEEN :start_time AND :end_time
                GROUP BY sr.session_id, ps.start_time, ps.end_time, ps.connector_name,
                        ps.consume_status, ps.process_status, ps.mode
            ) AS streaming_data
            HAVING COUNT(*) > 0
            UNION ALL
            
            SELECT 
                sr.session_id,
                SUM(CASE WHEN sr.state = 'processing' THEN 1 ELSE 0 END) AS processing_count,
                SUM( CASE 
                    WHEN sr.state != 'consume' AND ((IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') != 'find')
                        OR IFNULL(get_json_string(sr.result, '$.dynamic_event.state'), '') = 'processed'
                        OR IFNULL(get_json_string(sr.result, '$.product_holding.state'), '') = 'processed'
                        OR IFNULL(get_json_string(sr.result, '$.ticket.state'), '') = 'processed'
                        OR IFNULL(get_json_string(sr.result, '$.sale.state'), '') = 'processed'
                        OR IFNULL(get_json_string(sr.result, '$.deal.state'), '') = 'processed')
                    THEN 1 ELSE 0 
                END) AS processed_count,
                SUM( CASE 
                    WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                       AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'add'
                    THEN 1 ELSE 0 
                END) AS processed_add_count,
                SUM( CASE 
                    WHEN sr.state != 'consume' AND IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed'
                       AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'update'
                    THEN 1 ELSE 0 
                END) AS processed_updated_count,
                SUM(CASE 
                    WHEN sr.state = 'error' 
                        AND (
                            get_json_string(sr.result, '$.profile') IS NULL 
                            OR IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'error'
                            OR (
                                IFNULL(get_json_string(sr.result, '$.profile.state'), '') = 'processed' 
                                AND IFNULL(get_json_string(sr.result, '$.profile.action'), '') = 'find'
                            )
                        ) 
                    THEN 1 ELSE 0 
                END) AS error_count,
                SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS consume_count,
                ps.start_time,
                ps.end_time,
                ps.connector_name,
                ps.consume_status,
                ps.process_status,
                ps.mode
            FROM deduplicated_data sr
            INNER JOIN data_flow.pipeline_session ps ON sr.session_id = ps.session_id
            AND ps.mode = 'batch'  -- Chỉ lấy các bản ghi có mode là batch
            AND sr.created_time BETWEEN :start_time AND :end_time
            AND ps.consume_status IN :lst_consume_status
            GROUP BY sr.session_id, ps.start_time, ps.end_time, ps.connector_name,
                    ps.consume_status, ps.process_status, ps.mode
        ) AS combined_data
        ORDER BY start_time DESC
        """

        param_query = {
            "start_time": start_time,
            "end_time": end_time,
        }
        results = self.process_query(stmt, param_query)
        if results:
            return results
        return []

    def agg_report_df_report_to_daily_report(self, start_time, end_time, page, per_page):
        """
        Chuyển đổi dữ liệu từ bảng dataflow_report thành dữ liệu cho bảng daily_reports
        """
        lst_object = get_list_object_query_starrock()

        str_column_select = "connector_id,session_id,SUM(total_rows) as total_rows, SUM(total_rows_success) as total_rows_success,SUM(total_rows_add) as total_rows_add,SUM(total_rows_updated) as total_rows_updated,SUM(total_rows_error) as total_rows_error,SUM(total_rows_find) as total_rows_find,MIN(start_time) as start_time,MAX(end_time) as end_time,MAX(connector_name) as connector_name,MAX(consume_status) as consume_status,MAX(process_status) as process_status,MAX(mode) as mode,"

        x1 = "MAX(connector_id) AS connector_id, 'total_streaming_prev_snapshot' AS session_id,SUM(total_rows) AS total_rows,SUM(total_rows_success) AS total_rows_success,SUM(total_rows_add) AS total_rows_add,SUM(total_rows_updated) AS total_rows_updated,SUM(total_rows_error) AS total_rows_error,SUM(total_rows_find) AS total_rows_find,MIN(start_time) AS start_time,MAX(end_time) AS end_time,MAX(connector_name) AS connector_name,MAX(consume_status) AS consume_status,MAX(process_status) AS process_status,MAX(mode) AS mode,"

        x2 = "sr.connector_id, sr.session_id,SUM(CASE WHEN sr.state = 'consume' AND sr.object is Null THEN sr.count ELSE 0 END) AS total_rows,SUM(CASE WHEN sr.state = 'processed' AND sr.object is Null THEN sr.count ELSE 0 END) AS total_rows_success,SUM(CASE WHEN sr.state = 'processed' AND sr.object is Null AND sr.action = 'add' THEN sr.count ELSE 0 END) AS total_rows_add,SUM(CASE WHEN sr.state = 'processed' AND sr.object is Null AND sr.action = 'update' THEN sr.count ELSE 0 END) AS total_rows_updated,SUM(CASE WHEN sr.state = 'error' AND sr.object is Null THEN sr.count ELSE 0 END) AS total_rows_error,SUM(CASE WHEN sr.state = 'processed' AND sr.object is Null AND sr.action = 'find' THEN sr.count ELSE 0 END) AS total_rows_find,ps.start_time as start_time,ps.end_time as end_time,ps.connector_name as connector_name,ps.consume_status as consume_status,ps.process_status as process_status,ps.mode as mode,"

        index = 0
        for object_item in lst_object:
            str_column_select += f"SUM({object_item}_total_rows) as {object_item}_total_rows, SUM({object_item}_total_rows_success) as {object_item}_total_rows_success, SUM({object_item}_total_rows_add) as {object_item}_total_rows_add, SUM({object_item}_total_rows_updated) as {object_item}_total_rows_updated, SUM({object_item}_total_rows_error) as {object_item}_total_rows_error, SUM({object_item}_total_rows_find) as {object_item}_total_rows_find"
            x1 += f"SUM({object_item}_total_rows) AS {object_item}_total_rows, SUM({object_item}_total_rows_success) AS {object_item}_total_rows_success, SUM({object_item}_total_rows_add) AS {object_item}_total_rows_add, SUM({object_item}_total_rows_updated) AS {object_item}_total_rows_updated, SUM({object_item}_total_rows_error) AS {object_item}_total_rows_error, SUM({object_item}_total_rows_find) AS {object_item}_total_rows_find"
            x2 += f"SUM(CASE WHEN sr.state = 'consume' AND sr.object = '{object_item}' THEN sr.count ELSE 0 END) AS {object_item}_total_rows,SUM(CASE WHEN sr.state = 'processed' AND sr.object = '{object_item}' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_success,SUM(CASE WHEN sr.state = 'processed' AND sr.object = '{object_item}' AND sr.action = 'add' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_add,SUM(CASE WHEN sr.state = 'processed' AND sr.object = '{object_item}' AND sr.action = 'update' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_updated,SUM(CASE WHEN sr.state = 'error' AND sr.object = '{object_item}' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_error,SUM(CASE WHEN sr.state = 'processed' AND sr.object = '{object_item}' AND sr.action = 'find' THEN sr.count ELSE 0 END) AS {object_item}_total_rows_find"
            if index != len(lst_object) - 1:
                str_column_select += ", "
                x1 += ", "
                x2 += ", "
            index += 1

        stmt = f"""
        SELECT
            {str_column_select}
        FROM
            (
                SELECT
                    {x2}
                FROM
                    data_flow.dataflow_report sr
                INNER JOIN data_flow.pipeline_session ps ON
                    sr.session_id = ps.session_id
                GROUP BY
                    sr.connector_id,
                    sr.session_id,
                    ps.start_time,
                    ps.end_time,
                    ps.connector_name,
                    ps.consume_status,
                    ps.process_status,
                    ps.mode
            ) AS combined_data
        WHERE
            (start_time >= :start_time OR start_time IS NULL)
            AND (end_time <= :end_time OR end_time IS NULL)
        GROUP BY
            connector_id, session_id
        ORDER BY
            connector_id, session_id ASC
        LIMIT :page, :per_page
        """
        param_query = {
            "page": page,
            "per_page": per_page,
            "start_time": start_time,
            "end_time": end_time,
        }
        results = self.process_query(stmt, param_query)
        if results:
            return results
        return []
    
    def count_total_status_session_by_connector(self, list_pipeline_name):
        stmt = """
        SELECT 
            pipeline_name AS pipeline_name,
            SUM(CASE WHEN process_status = 'running' THEN 1 ELSE 0 END) AS processing_count,
            SUM(CASE WHEN process_status = 'finished' THEN 1 ELSE 0 END) AS processed_count,
            SUM(CASE WHEN process_status = 'failed' THEN 1 ELSE 0 END) AS failed_count,
            SUM(CASE WHEN process_status = 'prepare' THEN 1 ELSE 0 END) AS prepare_count,
            SUM(CASE WHEN process_status = 'stop' THEN 1 ELSE 0 END) AS stop_count
            
        FROM  data_flow.pipeline_session ps 
        WHERE pipeline_name IN :list_pipeline_name
        GROUP BY pipeline_name
        """
        param_query = {
            "list_pipeline_name": list_pipeline_name
        }
        results = self.process_query(stmt, param_query)
        if results:
            return results
        return []
    
    
if __name__ == "__main__":
    print(DataFlowDialect().count_total_status_session_by_connector(["connector_2597_1750049972"]))
    
    def get_sync_log_snapshot(self, connector_id, session_id, per_page, before_token):
        skip_number = 0
        page_index = 0
        parse_token = self.parse_token(before_token)
        if parse_token:
            page_index = parse_token.get("page_index", 0)
            skip_number = int(page_index) * int(per_page)
        list_session = []
        if session_id == "total_streaming_prev_snapshot":
            smt = f"""
                session_df_log AS (
                    SELECT
                        distinct(session_id)
                    FROM data_flow.dataflow_log
                    WHERE connector_id = :connector_id
                )
                SELECT
                    distinct(session_id)
                FROM session_df_log
                LEFT JOIN data_flow.pipeline_session ps ON
                    session_df_log.session_id = ps.session_id AND ps.mode = 'streaming'
            """
            results = DataFlowDialect().process_query(
                smt, {"connector_id": connector_id}
            )
            if results:
                list_session = [result[0] for result in results]
        else:
            list_session = [session_id]
            
        smt1 = f"""
            WITH deduplicated_data AS (
                SELECT 
                    connector_id,
                    session_id,
                    message_id,
                    state,
                    event_value,
                    result,
                    action,
                    created_time,
                    reason
                FROM (
                    SELECT 
                        connector_id,
                        session_id,
                        message_id,
                        state,
                        event_value,
                        result,
                        action,
                        created_time,
                        reason,
                        ROW_NUMBER() OVER (
                            PARTITION BY session_id, message_id, state
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM data_flow.dataflow_log
                    WHERE session_id IN :session_ids
                ) AS tmp
                WHERE row_num = 1
            ),

            start_time_data AS (
                SELECT 
                    message_id,
                    session_id,
                    created_time AS start_time
                FROM (
                    SELECT *,
                        ROW_NUMBER() OVER (
                            PARTITION BY message_id
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM deduplicated_data
                    WHERE state = 'consume'
                ) AS sub
                WHERE row_num = 1
            ),

            end_state_ranked AS (
                SELECT 
                    message_id,
                    state,
                    created_time,
                    reason,
                    ROW_NUMBER() OVER (
                        PARTITION BY message_id
                        ORDER BY 
                            CASE 
                                WHEN state = 'processed' THEN 1
                                WHEN state = 'error' THEN 2
                                ELSE 3
                            END
                    ) AS rank,
                    result
                FROM deduplicated_data
                WHERE state IN ('processed', 'error')
            ),

            end_time_data AS (
                SELECT 
                    message_id,
                    created_time AS end_time,
                    state AS end_state,
                    reason,
                    result
                FROM end_state_ranked
                WHERE rank = 1
            )

            SELECT 
                COALESCE(s.message_id, e.message_id) AS message_id,
                s.session_id AS session_id,
                e.result AS result,
                s.start_time AS start_time,
                e.end_time AS end_time,
                e.end_state AS end_state,
                e.reason AS reason
            FROM start_time_data s
            LEFT JOIN end_time_data e ON s.message_id = e.message_id
            ORDER BY s.start_time DESC
            LIMIT :skip_number, :per_page
            """
        results = DataFlowDialect().process_query(
            smt1, {"session_ids": list_session,  "skip_number": skip_number, "per_page": per_page}
        )
        if results:
            return results, self.generate_after_token_by_page_index(page_index + 1)
        return [], ""
    
    def count_message_snapshot_by_status(self, connector_id, session_id):
        list_session = []
        if session_id == "total_streaming_prev_snapshot":
            smt = f"""
                session_df_log AS (
                    SELECT
                        distinct(session_id)
                    FROM data_flow.dataflow_log
                    WHERE connector_id = :connector_id
                )
                SELECT
                    distinct(session_id)
                FROM session_df_log
                LEFT JOIN data_flow.pipeline_session ps ON
                    session_df_log.session_id = ps.session_id AND ps.mode = 'streaming'
            """
            results = DataFlowDialect().process_query(
                smt, {"connector_id": connector_id}
            )
            if results:
                list_session = [result[0] for result in results]
        else:
            list_session = [session_id]
        smt1 = f"""
            WITH deduplicated_data AS (
                SELECT 
                    connector_id,
                    session_id,
                    message_id,
                    state,
                    event_value,
                    result,
                    action,
                    created_time
                FROM (
                    SELECT 
                        connector_id,
                        session_id,
                        message_id,
                        state,
                        event_value,
                        result,
                        action,
                        created_time,
                        ROW_NUMBER() OVER (
                            PARTITION BY session_id, message_id, state
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM data_flow.dataflow_log
                    WHERE session_id IN :session_ids
                ) AS tmp
                WHERE row_num = 1
            )
            SELECT
                sr.connector_id,
                sr.session_id,
                SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS total_rows,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL THEN 1 ELSE 0 END) AS total_rows_success,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'add' THEN 1 ELSE 0 END) AS total_rows_add,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'update' THEN 1 ELSE 0 END) AS total_rows_updated,
                SUM(CASE WHEN sr.state = 'error' THEN 1 ELSE 0 END) AS total_rows_error,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'find' THEN 1 ELSE 0 END) AS total_rows_find
            FROM
                deduplicated_data sr
            GROUP BY sr.connector_id, sr.session_id
            """
        results = DataFlowDialect().process_query(
            smt1, {"session_ids": list_session}
        )
        if results:
            results = list(results)
            return results[0]._asdict()
        return []
    
    def count_message_streaming_by_date(self, connector_id):
        smt1 = f"""
            WITH deduplicated_data AS (
                SELECT 
                    connector_id,
                    session_id,
                    message_id,
                    state,
                    event_value,
                    result,
                    action,
                    created_time
                FROM (
                    SELECT 
                        connector_id,
                        session_id,
                        message_id,
                        state,
                        event_value,
                        result,
                        action,
                        created_time,
                        ROW_NUMBER() OVER (
                            PARTITION BY session_id, message_id, state
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM data_flow.dataflow_log
                    WHERE connector_id = :connector_id
                ) AS tmp
                WHERE row_num = 1
            )
            SELECT
                sr.connector_id,
                SUM(CASE WHEN sr.state = 'consume' THEN 1 ELSE 0 END) AS total_rows,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL THEN 1 ELSE 0 END) AS total_rows_success,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'add' THEN 1 ELSE 0 END) AS total_rows_add,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'update' THEN 1 ELSE 0 END) AS total_rows_updated,
                SUM(CASE WHEN sr.state = 'error' THEN 1 ELSE 0 END) AS total_rows_error,
                SUM(CASE WHEN sr.state = 'processed' AND sr.event_value IS NULL AND sr.action = 'find' THEN 1 ELSE 0 END) AS total_rows_find
            FROM
                deduplicated_data sr
            GROUP BY sr.connector_id;
            """
        results = DataFlowDialect().process_query(
            smt1, {"connector_id": connector_id}
        )
        if results:
            results = list(results)
            return results[0]._asdict()
        return []
    
    def get_sync_log_streaming(self, connector_id, per_page, before_token):
        skip_number = 0
        page_index = 0
        parse_token = self.parse_token(before_token)
        if parse_token:
            page_index = parse_token.get("page_index", 0)
            skip_number = int(page_index) * int(per_page)
        smt1 = f"""
            WITH deduplicated_data AS (
                SELECT 
                    connector_id,
                    session_id,
                    message_id,
                    state,
                    event_value,
                    result,
                    action,
                    reason,
                    created_time
                FROM (
                    SELECT 
                        connector_id,
                        session_id,
                        message_id,
                        state,
                        event_value,
                        result,
                        action,
                        reason,
                        created_time,
                        ROW_NUMBER() OVER (
                            PARTITION BY session_id, message_id, state
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM data_flow.dataflow_log
                    WHERE connector_id = :connector_id
                ) AS tmp
                WHERE row_num = 1
            ),

            start_time_data AS (
                SELECT 
                    message_id,
                    session_id,
                    created_time AS start_time
                FROM (
                    SELECT *,
                        ROW_NUMBER() OVER (
                            PARTITION BY message_id
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM deduplicated_data
                    WHERE state = 'consume'
                ) AS sub
                WHERE row_num = 1
            ),

            end_state_ranked AS (
                SELECT 
                    message_id,
                    state,
                    created_time,
                    reason,
                    ROW_NUMBER() OVER (
                        PARTITION BY message_id
                        ORDER BY 
                            CASE 
                                WHEN state = 'processed' THEN 1
                                WHEN state = 'error' THEN 2
                                ELSE 3
                            END
                    ) AS rank,
                    result
                FROM deduplicated_data
                WHERE state IN ('processed', 'error')
            ),

            end_time_data AS (
                SELECT 
                    message_id,
                    created_time AS end_time,
                    state AS end_state,
                    reason,
                    result
                FROM end_state_ranked
                WHERE rank = 1
            )

            SELECT 
                COALESCE(s.message_id, e.message_id) AS message_id,
                s.session_id AS session_id,
                e.result AS result,
                s.start_time AS start_time,
                e.end_time AS end_time,
                e.end_state AS end_state,
                e.reason AS reason
            FROM start_time_data s
            LEFT JOIN end_time_data e ON s.message_id = e.message_id
            ORDER BY s.start_time DESC
            LIMIT :skip_number, :per_page
            """
        results = DataFlowDialect().process_query(
            smt1, {"connector_id": connector_id, "skip_number": skip_number, "per_page": per_page}
        )
        if results:
            return results, self.generate_after_token_by_page_index(page_index + 1)
        return [], ""
    
    def get_detail_log_message(self, connector_id, message_id):
        smt1 = f"""
            WITH deduplicated_data AS (
                SELECT *
                FROM (
                    SELECT 
                        message_id,
                        session_id, 
                        state, 
                        result, 
                        created_time, 
                        connector_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY session_id, message_id, state
                            ORDER BY created_time ASC
                        ) AS row_num
                    FROM data_flow.dataflow_log
                    WHERE connector_id = :connector_id and message_id = :message_id
                ) AS tmp
                WHERE row_num = 1 -- Giữ duy nhất 1 bản ghi trong mỗi nhóm
            )
            SELECT 
                message_id,
                session_id,
                MIN(CASE WHEN state = 'consume' THEN created_time END) AS start_time,
                MAX(CASE WHEN state = 'processed' THEN created_time END) AS end_time,
                MAX(CASE WHEN state = 'processed' THEN result END) AS result
            FROM deduplicated_data
            """
        results = DataFlowDialect().process_query(
            smt1, {"connector_id": connector_id, "message_id": message_id}
        )
        if results:
            return results.first()
        return None
        
    
# if __name__ == "__main__":
#     print(DataFlowDialect().get_sync_log_streaming(2389, 10, ""))

