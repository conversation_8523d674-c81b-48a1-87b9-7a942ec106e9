#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: TungDD
Company: Mobio
Date Created: 10/10/2021
"""
import json
import re
import uuid
from datetime import datetime

from bson.objectid import ObjectId
from mobio.libs.logging import Mo<PERSON>Logging
from pymongo import MongoClient, ReadPreference, WriteConcern

from configs import MongoConfig
from src.common import DatetimeTypeKeys
from src.common.json_encoder import J<PERSON><PERSON><PERSON><PERSON>
from src.common.utils import Base64, utf8_to_ascii

base_client = MongoClient(MongoConfig.MONGO_URI)
db_name = re.search(r"^mongodb://[^@]+@[^/]+/([^?$]+).*$", MongoConfig.MONGO_URI).group(1)
# db_name = 'test_note'


class BaseModel:
    client = base_client
    collection_name = ""

    def _db(self):
        return self.client[db_name]

    def get_db(self):
        db = self.client[db_name]
        collection = db[self.collection_name]
        return collection

    def insert(self, dictionary):
        db = self.client[db_name]
        return db[self.collection_name].insert_one(dictionary)

    def insert_many(self, document):
        db = self.client[db_name]
        return db[self.collection_name].insert_many(document)

    def insert_document(self, dictionary):
        db = self.client[db_name]
        return db[self.collection_name].insert_one(dictionary)

    def update_set_dictionary(self, search_option, dictionary):
        db = self.client[db_name]

        document = db[self.collection_name].find_one(search_option)
        if document:
            return (
                db[self.collection_name]
                .update_one(filter=search_option, update={"$set": dictionary}, upsert=True)
                .matched_count
                >= 1
            )
        return None

    def update_dictionary(self, document_id, dictionary):
        if isinstance(document_id, str):
            document_id = ObjectId(document_id)
        return self.collector().update_one({"_id": document_id}, dictionary).matched_count

    def update_one_query(self, query, data):
        return self.client.get_database(db_name)[self.collection_name].update_one(query, {"$set": data}).matched_count

    def update_many(self, filter_option, update_option):
        db = self.client[db_name]
        return db[self.collection_name].update_many(filter_option, update_option).matched_count

    def update(self, filter_option, update_option, upsert=False, multi=False):
        db = self.client[db_name]
        db[self.collection_name].update(filter_option, update_option, upsert=upsert, multi=multi)

    def update_by_set(self, filter_option, update_option, upsert=False, multi=False):
        db = self.client[db_name]
        return db[self.collection_name].update_many(filter_option, {"$set": update_option}).matched_count

    def delete_one(self, delete_options):
        db = self.client[db_name]
        return db[self.collection_name].delete_one(delete_options)

    def delete_many(self, delete_options):
        db = self.client[db_name]
        return db[self.collection_name].delete_many(delete_options).deleted_count

    def upsert(self, search_option, dictionary):
        db = self.client[db_name]
        document = db[self.collection_name].find_one(search_option)
        if document:
            document.update(dictionary)
            self.collector().replace_one({"_id": document.get("_id")}, dictionary, upsert=True)
            return document.get("_id")
        else:
            return db[self.collection_name].insert_one(dictionary).inserted_id

    def find(self, search_option, obj_field_select: dict = None):
        db = self.client[db_name]
        if obj_field_select:
            return db[self.collection_name].find(search_option, obj_field_select)
        return db[self.collection_name].find(search_option)

    def find_one(self, search_option, fields_select: dict = None):
        if fields_select:
            return self.collector().find_one(search_option, fields_select)
        return self.collector().find_one(search_option)

    def collector(self):
        return self._db()[self.collection_name]

    def count_by_query(self, count_option):
        db = self.client[db_name]
        return db[self.collection_name].count_documents(count_option)

    def count(self, search_option=None):
        if not search_option:
            search_option = {}
        return self._db()[self.collection_name].count_documents(search_option)

    def select_all(self, search_option, projection=None):
        return self.collector().find(search_option, projection)

    def find_paginate(self, search_option, page=0, per_page=None, sort_option=None, projection=None, order=-1):
        collection = self.collector().find(search_option, projection)
        if sort_option:
            collection = collection.sort(sort_option, order)

        if page != -1:
            if per_page:
                collection = collection.limit(per_page)
            if page > 0:
                page -= 1
                offset = int(page) * int(per_page)
                collection = collection.skip(offset)

        return collection

    def _aggregate(self, group, match: object, sort=None, project=None):
        db = self.client[db_name]
        pipeline = []
        if match:
            pipeline.append({"$match": match})
        pipeline.append({"$group": group})
        if sort:
            pipeline.append({"$sort": sort})
        if project:
            pipeline.append({"$project": project})
        return db[self.collection_name].aggregate(pipeline)

    def aggregate(self, pipeline):
        db = self.client[db_name]
        return db[self.collection_name].aggregate(pipeline)

    def distinct(self, fields, query):
        db = self.client[db_name]

        if type(fields) is str:
            return db[self.collection_name].distinct(fields, query)

        return None

    def bulk_write_data(self, data):
        db = self.client[db_name]
        return db[self.collection_name].bulk_write(data)

    @staticmethod
    def normalize_uuid(some_uuid):
        if isinstance(some_uuid, str):
            return uuid.UUID(some_uuid)
        return some_uuid

    @staticmethod
    def normalize_object_id(some_object_id):
        if isinstance(some_object_id, str):
            return ObjectId(some_object_id)
        return some_object_id

    def get_collector(self, read_preference=False, write_concern=None):
        w = None
        r = None
        if write_concern is not None:
            w = WriteConcern(w=write_concern)
        if read_preference:
            r = ReadPreference.SECONDARY_PREFERRED
        return self._db().get_collection(self.collection_name, read_preference=r, write_concern=w)

    @classmethod
    def generate_after_token(cls, item, sort_keys):
        """
        Hàm dùng để generate token cho page tiếp theo. (dùng cho Mongo)
        :param item: item cuối cùng của page trước.
        :param sort_keys: Danh sách các field được sắp xếp (tuple)
        :return: token: giá trị dùng để client request lấy page tiếp theo.
        """
        data_gen_token = []
        for key in sort_keys:
            data_gen_token.append(item.get(key))
        if data_gen_token:
            data_token = JSONEncoder().json_loads(data_gen_token)
            next_token = Base64.encode(json.dumps(data_token, ensure_ascii=False))
            return next_token

        return ""

    @staticmethod
    def generate_paging_token(result, order_by, per_page):
        paging_token = None
        order_by = order_by.split(".")[-1]
        if result:
            # Generate token
            row = result[-1]
            token = {
                "last_id": str(row.get("_id")) if "_id" in row else row["id"],
                "field": {"key": order_by, "value": row.get(order_by)},
            }
            if isinstance(token["field"]["value"], datetime):
                token["field"]["value"] = token["field"]["value"].timestamp()
            if isinstance(token["field"]["value"], ObjectId):
                token["field"]["value"] = str(token["field"]["value"])
            json_token = json.dumps(token)
            paging_token = Base64.encode(json_token)
        return paging_token

    staticmethod

    def generate_paging_int_token(result, page_index, per_page):
        """
        Generate paging token from page index
        """
        page_token = None
        if result and len(result) == per_page:
            page_token = Base64.encode(str(page_index))
        return page_token

    staticmethod

    def decode_paging_int_token(token):
        """
        Generate paging index from page token
        """
        page_index = int(Base64.decode(str(token)))
        return page_index

    @classmethod
    def parse_token(cls, token):
        if not token:
            return None
        condition = json.loads(Base64.decode(token))
        return condition

    def find_paginate_load_more(
        self,
        search_option={},
        per_page=20,
        after_token=None,
        sort_option=[("_id", -1)],
        projection=None,
        list_field_datetime=DatetimeTypeKeys.VALUES,
    ):
        """
        Support sort field type: number, _id
        :param search_option:
        :param per_page:
        :param after_token:
        :param sort_option: (chỉ support sort 1 field)
        :param projection: (Bao gồm field được sort)
        :return:
        """
        sort_key = sort_option[0][0]
        sort_value = sort_option[0][1]
        sort_filter = {}
        # Check token
        if after_token:
            operator_query = "$lt" if sort_value == -1 else "$gt"
            token = self.parse_token(after_token)
            field = token.get("field")
            last_id = token.get("last_id")
            if "key" in field and "value" in field:
                key_sorted = field["key"]
                value_sorted = field["value"]
                if key_sorted in list_field_datetime:
                    value_sorted = datetime.fromtimestamp(float(value_sorted))

                try:
                    last_id = ObjectId(last_id)
                except:
                    last_id = int(last_id)

                item_sort_filter = [
                    {key_sorted: {operator_query: value_sorted}},
                    {key_sorted: value_sorted, "_id": {operator_query: last_id}},
                ]

                if "$or" in search_option:
                    sort_filter = {
                        "$or": [
                            {"$and": [cond1, cond2]} for cond1 in search_option["$or"] for cond2 in item_sort_filter
                        ]
                    }
                else:
                    sort_filter = {"$or": item_sort_filter}
        find_filter = {**search_option, **sort_filter}
        MobioLogging().info("find_paginate_load_more :: find_filter :: {}".format(find_filter))
        collection = self.get_collector().find(find_filter, projection).limit(per_page)
        sort_option.append(("_id", sort_value))
        collection = collection.sort(sort_option)
        data = [x for x in collection]
        next_token = BaseModel.generate_paging_token(result=data, order_by=sort_key, per_page=per_page)
        return data, next_token

    def _gen_data_build_search_query(self, search_keywords):
        if search_keywords:
            normalize_search = utf8_to_ascii(search_keywords)
            normalize_search = normalize_search.lower()
            import re

            rgx = re.compile(".*{}.*".format(normalize_search), re.IGNORECASE)
            return rgx
        return None

    def generate_after_token_by_page_index(self, page_index):
        data_gen_token = {"page_index": page_index}
        next_token = Base64.encode(json.dumps(data_gen_token, ensure_ascii=False))
        return next_token

    def parse_token_token_by_page_index(self, token):
        if not token:
            return None
        condition = json.loads(Base64.decode(token))
        return condition
