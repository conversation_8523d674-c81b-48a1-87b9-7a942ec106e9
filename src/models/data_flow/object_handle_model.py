#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 14/09/2024
"""
import copy

from src.common import lru_redis_cache
from src.common.data_flow_constant import ConstantObjectHandle
from src.models import BaseModel


class SettingObjectHandleModel(BaseModel):
    object_handled_data_init = [
        {
            "data_type": "data_in",
            "source_key": "default",
            "configs": [
                {
                    "name": {"vi": "Profile", "en": "Profile"},
                    "key": "profiles",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_in",
                    "config_attributes": [
                        {
                            "name": {"vi": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h của đối tượng", "en": "Attribute"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE,
                            "status": 1,
                            "order": 1,
                        },
                        {
                            "name": {"vi": "Event tuỳ biến", "en": "Dynamic Event"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
                            "status": 1,
                            "order": 2,
                        },
                        {
                            "name": {"vi": "Product holding", "en": "Product Holding"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
                            "status": 1,
                            "order": 3,
                        },
                    ],
                },
                {
                    "name": {"vi": "Công ty", "en": "Company"},
                    "key": "company",
                    "status": 0,
                    "order": 2,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Cơ hội bán", "en": "Sale"},
                    "key": "sale",
                    "status": 1,
                    "order": 3,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Ticket", "en": "Ticket"},
                    "key": "ticket",
                    "status": 1,
                    "order": 4,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
            ],
        },
        {
            "data_type": "data_in",
            "source_key": "api",
            "configs": [
                {
                    "name": {"vi": "Profile", "en": "Profile"},
                    "key": "profiles",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_in",
                    "config_attributes": [
                        {
                            "name": {"vi": "Thuộc tính của đối tượng", "en": "Attribute"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE,
                            "status": 1,
                            "order": 1,
                        },
                        {
                            "name": {"vi": "Event tuỳ biến", "en": "Dynamic Event"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
                            "status": 1,
                            "order": 2,
                        },
                        {
                            "name": {"vi": "Product holding", "en": "Product Holding"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
                            "status": 1,
                            "order": 3,
                        },
                    ],
                },
                {
                    "name": {"vi": "Công ty", "en": "Company"},
                    "key": "company",
                    "status": 0,
                    "order": 2,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Cơ hội bán", "en": "Sale"},
                    "key": "sale",
                    "status": 1,
                    "order": 3,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Ticket", "en": "Ticket"},
                    "key": "ticket",
                    "status": 1,
                    "order": 4,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
            ],
        },
        {
            "data_type": "data_in",
            "source_key": "postgres",
            "configs": [
                {
                    "name": {"vi": "Profile", "en": "Profile"},
                    "key": "profiles",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_in",
                    "config_attributes": [
                        {
                            "name": {"vi": "Thuộc tính của đối tượng", "en": "Attribute"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE,
                            "status": 1,
                            "order": 1,
                        },
                        {
                            "name": {"vi": "Event tuỳ biến", "en": "Dynamic Event"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
                            "status": 1,
                            "order": 2,
                        },
                        {
                            "name": {"vi": "Product holding", "en": "Product holding"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
                            "status": 1,
                            "order": 3,
                        },
                    ],
                },
                {
                    "name": {"vi": "Công ty", "en": "Company"},
                    "key": "company",
                    "status": 0,
                    "order": 2,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Cơ hội bán", "en": "Sale"},
                    "key": "sale",
                    "status": 0,
                    "order": 3,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Ticket", "en": "Ticket"},
                    "key": "ticket",
                    "status": 0,
                    "order": 4,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
            ],
        },
        {
            "data_type": "data_in",
            "source_key": "oracle",
            "configs": [
                {
                    "name": {"vi": "Profile", "en": "Profile"},
                    "key": "profiles",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_in",
                    "config_attributes": [
                        {
                            "name": {"vi": "Thuộc tính của đối tượng", "en": "Attribute"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE,
                            "status": 1,
                            "order": 1,
                        },
                        {
                            "name": {"vi": "Event tuỳ biến", "en": "Dynamic Event"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
                            "status": 1,
                            "order": 2,
                        },
                        {
                            "name": {"vi": "Product holding", "en": "Product Holding"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
                            "status": 1,
                            "order": 3,
                        },
                    ],
                },
                {
                    "name": {"vi": "Công ty", "en": "Company"},
                    "key": "company",
                    "status": 0,
                    "order": 2,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Cơ hội bán", "en": "Sale"},
                    "key": "sale",
                    "status": 0,
                    "order": 3,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Ticket", "en": "Ticket"},
                    "key": "ticket",
                    "status": 0,
                    "order": 4,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
            ],
        },
        {
            "data_type": "data_in",
            "source_key": "api",
            "configs": [
                {
                    "name": {"vi": "Profile", "en": "Profile"},
                    "key": "profiles",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_in",
                    "config_attributes": [
                        {
                            "name": {"vi": "Thuộc tính của đối tượng", "en": "Attribute"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE,
                            "status": 1,
                            "order": 1,
                        },
                        {
                            "name": {"vi": "Event tuỳ biến", "en": "Dynamic Event"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
                            "status": 1,
                            "order": 2,
                        },
                        {
                            "name": {"vi": "Product holding", "en": "Product Holding"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
                            "status": 1,
                            "order": 3,
                        },
                    ],
                },
                {
                    "name": {"vi": "Công ty", "en": "Company"},
                    "key": "company",
                    "status": 0,
                    "order": 2,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Cơ hội bán", "en": "Sale"},
                    "key": "sale",
                    "status": 1,
                    "order": 3,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Ticket", "en": "Ticket"},
                    "key": "ticket",
                    "status": 1,
                    "order": 4,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
            ],
        },
        {
            "data_type": "data_in",
            "source_key": "contact_form",
            "configs": [
                {
                    "name": {"vi": "Profile", "en": "Profile"},
                    "key": "profiles",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_in",
                    "config_attributes": [
                        {
                            "name": {"vi": "Thuộc tính của đối tượng", "en": "Attribute"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE,
                            "status": 1,
                            "order": 1,
                        },
                        {
                            "name": {"vi": "Event tuỳ biến", "en": "Dynamic Event"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
                            "status": 0,
                            "order": 2,
                        },
                        {
                            "name": {"vi": "Product holding", "en": "Product Holding"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
                            "status": 0,
                            "order": 3,
                        },
                    ],
                },
                {
                    "name": {"vi": "Công ty", "en": "Company"},
                    "key": "company",
                    "status": 0,
                    "order": 2,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Cơ hội bán", "en": "Sale"},
                    "key": "sale",
                    "status": 1,
                    "order": 3,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Ticket", "en": "Ticket"},
                    "key": "ticket",
                    "status": 1,
                    "order": 4,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
            ],
        },
        {
            "data_type": "data_in",
            "source_key": "mysql",
            "configs": [
                {
                    "name": {"vi": "Profile", "en": "Profile"},
                    "key": "profiles",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_in",
                    "config_attributes": [
                        {
                            "name": {"vi": "Thuộc tính của đối tượng", "en": "Attribute"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE,
                            "status": 1,
                            "order": 1,
                        },
                        {
                            "name": {"vi": "Event tuỳ biến", "en": "Dynamic Event"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
                            "status": 1,
                            "order": 2,
                        },
                        {
                            "name": {"vi": "Product holding", "en": "Product Holding"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
                            "status": 1,
                            "order": 3,
                        },
                    ],
                },
                {
                    "name": {"vi": "Công ty", "en": "Company"},
                    "key": "company",
                    "status": 0,
                    "order": 2,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Cơ hội bán", "en": "Sale"},
                    "key": "sale",
                    "status": 0,
                    "order": 3,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Ticket", "en": "Ticket"},
                    "key": "ticket",
                    "status": 0,
                    "order": 4,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
            ],
        },
        {
            "data_type": "data_out",
            "source_key": "webhooks",
            "configs": [
                {
                    "name": {"vi": "Profile", "en": "Profile"},
                    "key": "profile",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_out",
                },
                {
                    "name": {"vi": "Journey builder", "en": "Journey builder"},
                    "key": "journey_builder",
                    "status": 1,
                    "order": 3,
                    "data_type": "data_out",
                },
                {
                    "name": {"vi": "Cơ hội bán", "en": "Sale"},
                    "key": "sale",
                    "status": 1,
                    "order": 4,
                    "data_type": "data_out",
                },
                {
                    "name": {"vi": "Công việc", "en": "Task"},
                    "key": "task",
                    "status": 1,
                    "order": 6,
                    "data_type": "data_out",
                },
                {
                    "name": {"vi": "Company", "en": "Company"},
                    "key": "company",
                    "status": 1,
                    "order": 2,
                    "data_type": "data_out",
                },
                {
                    "name": {"vi": "Ticket", "en": "Ticket"},
                    "key": "ticket",
                    "status": 1,
                    "order": 5,
                    "data_type": "data_out",
                },
                {
                    "name": {"vi": "Ghi chú", "en": "Note"},
                    "key": "note",
                    "status": 1,
                    "order": 7,
                    "data_type": "data_out",
                },
            ],
        },
        {
            "data_type": "data_out",
            "source_key": "google_sheet",
            "configs": [
                {
                    "name": {"vi": "Contact Form", "en": "Contact Form"},
                    "key": "contact_form",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_out",
                },
            ],
        },
        {
            "data_type": "data_in",
            "source_key": "db2",
            "configs": [
                {
                    "name": {"vi": "Profile", "en": "Profile"},
                    "key": "profiles",
                    "status": 1,
                    "order": 1,
                    "data_type": "data_in",
                    "config_attributes": [
                        {
                            "name": {"vi": "Thuộc tính của đối tượng", "en": "Attribute"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE,
                            "status": 1,
                            "order": 1,
                        },
                        {
                            "name": {"vi": "Event tuỳ biến", "en": "Dynamic Event"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
                            "status": 1,
                            "order": 2,
                        },
                        {
                            "name": {"vi": "Product holding", "en": "Product Holding"},
                            "key": ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
                            "status": 1,
                            "order": 3,
                        },
                    ],
                },
                {
                    "name": {"vi": "Công ty", "en": "Company"},
                    "key": "company",
                    "status": 0,
                    "order": 2,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Cơ hội bán", "en": "Sale"},
                    "key": "sale",
                    "status": 0,
                    "order": 3,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
                {
                    "name": {"vi": "Ticket", "en": "Ticket"},
                    "key": "ticket",
                    "status": 0,
                    "order": 4,
                    "data_type": "data_in",
                    "config_attributes": [],
                },
            ],
        },
    ]

    def __init__(self):
        super().__init__()
        self.collection_name = "setting_object_handle"

    def init_list_source_handled(self, merchant_id):
        data_inserts = copy.deepcopy(self.object_handled_data_init)
        for data_init in data_inserts:
            data_init["merchant_id"] = merchant_id

            filter_option = {
                "merchant_id": merchant_id,
                "data_type": data_init["data_type"],
                "source_key": data_init["source_key"],
            }
            self.upsert(filter_option, data_init)
        return data_inserts

    def get_list_source_handled(self, merchant_id, data_type, source_key):
        if source_key:
            source_key = source_key.split(",")
        filter_option = {
            "merchant_id": merchant_id,
            "data_type": data_type,
            "source_key": {"$in": source_key},
        }
        details = self.find(filter_option)
        details = [*details]
        if not details:
            self.init_list_source_handled(merchant_id)
            details = self.find(filter_option)
        results = []
        for detail in details:
            results.extend(detail.get("configs", []))
        return results

    @lru_redis_cache.add_for_class(expiration=86400)
    def get_list_key_source_handled_active(self, merchant_id, data_type, source_key):
        filter_option = {
            "merchant_id": merchant_id,
            "data_type": data_type,
            "source_key": source_key,
        }
        detail = self.find_one(filter_option)
        if not detail:
            detail = self.init_list_source_handled(merchant_id)
            detail = self.find_one(filter_option)
        configs = detail.get("configs", [])
        results = [obj["key"] for obj in configs if obj["status"] == 1]
        return results

    @lru_redis_cache.add_for_class(expiration=86400)
    def get_list_key_source_handled_active(self, merchant_id, data_type, source_key):
        filter_option = {
            "merchant_id": merchant_id,
            "data_type": data_type,
            "source_key": source_key,
        }
        detail = self.find_one(filter_option)
        if not detail:
            detail = self.init_list_source_handled(merchant_id)
            detail = self.find_one(filter_option)
        configs = detail.get("configs", [])
        results = [obj["key"] for obj in configs if obj["status"] == 1]
        return results

    @lru_redis_cache.add_for_class(expiration=86400)
    def get_list_key_config_attribute_handled_active(self, merchant_id, data_type, source_key, object_primary):
        filter_option = {
            "merchant_id": merchant_id,
            "data_type": data_type,
            "source_key": source_key,
        }
        detail = self.find_one(filter_option)
        if not detail:
            detail = self.init_list_source_handled(merchant_id)
            detail = self.find_one(filter_option)
        configs = detail.get("configs", [])
        for config in configs:
            if config["key"] == object_primary and config["status"] == 1:
                config_attributes = config.get("config_attributes", [])
                if not config_attributes:
                    return []
                results = [obj["key"] for obj in config_attributes if obj["status"] == 1]
                return results

        return []

    def get_object_handled_name(self, merchant_id, data_type, object_primary, language, source_key):
        objects = self.get_list_source_handled(merchant_id, data_type, source_key)
        for obj in objects:
            if obj["key"] == object_primary:
                return obj["name"].get(language)
        return None

    def get_object_relate_by_object_primary(self, merchant_id, data_type, object_primary, source_key):
        map_object = {"profiles": "profile", "sale": "deal"}
        lst_objects = self.get_list_source_handled(merchant_id, data_type, source_key)
        results = []
        for obj in lst_objects:
            obj_key = obj["key"]

            if obj_key != object_primary:
                continue
            obj_key = map_object.get(obj_key, obj_key)
            if obj_key in ["deal", "ticket"]:
                results.append("profile")

            if obj.get("config_attributes"):
                for config_attribute in obj.get("config_attributes"):
                    if config_attribute["status"] == 1:
                        results.append(config_attribute["key"])
            else:
                results.append(obj_key)
        return results
