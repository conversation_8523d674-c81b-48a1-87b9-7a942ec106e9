from unittest import result

from src.common import CommonKeys, ProviderKeys
from src.common.utils import get_time_now
from src.models import BaseModel


class ProviderModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "provider"

    def get_provider_by_id(self, provider_id):
        return self.find_one({"_id": provider_id})

    def get_provider_by_code(self, provider_type, service, fields_select=None, lang="vi"):
        provider = self.find_one(
            {ProviderKeys.PROVIDER_TYPE: provider_type, ProviderKeys.SERVICE: service},
            fields_select=fields_select,
        )

        if not provider:
            return None
        for config in provider.get(ProviderKeys.CONNECT_CONFIG_INFO):
            description = config.get(ProviderKeys.DESCRIPTION)
            key_name = config.get(ProviderKeys.KEY_NAME)
            note = config.get(ProviderKeys.NOTE)

            if key_name:
                config[ProviderKeys.KEY_NAME] = key_name[lang]
            if description:
                config[ProviderKeys.DESCRIPTION] = description[lang]
            if note:
                config[ProviderKeys.NOTE] = note[lang]
        return provider
    
    def get_all_provider_type_by_service(self, service):
        results = self.distinct("provider_type", {ProviderKeys.SERVICE: service})
        return results

    def upsert_provider(self, payload, created_by):
        query = {
            ProviderKeys.PROVIDER: payload.get(ProviderKeys.PROVIDER),
            ProviderKeys.PROVIDER_TYPE: payload.get(ProviderKeys.PROVIDER_TYPE),
        }

        payload.update(
            {
                CommonKeys.CREATED_BY: created_by,
                CommonKeys.CREATED_TIME: get_time_now(),
                CommonKeys.UPDATED_TIME: get_time_now(),
                CommonKeys.UPDATED_BY: created_by,
            }
        )

        return self.upsert(query, payload)

    def get_providers(self, service, lang="vi"):
        results = list(self.find({"service": service}))
        for item in results:
            provider_type = item.get(ProviderKeys.PROVIDER_TYPE)
            connect_config_info = item.get(ProviderKeys.CONNECT_CONFIG_INFO)
            if provider_type == 203 and item.get(ProviderKeys.PROVIDER_NAME):
                item[ProviderKeys.PROVIDER] = item.get(ProviderKeys.PROVIDER_NAME)
            if provider_type == 212:
                item[ProviderKeys.CONNECT_CONFIG_INFO] = []
                item.pop(ProviderKeys.HOST)
                item.pop(ProviderKeys.TOKEN)
            for config in connect_config_info:
                key_name = config.get(ProviderKeys.KEY_NAME)
                description = config.get(ProviderKeys.DESCRIPTION)
                note = config.get(ProviderKeys.NOTE)

                if key_name:
                    config[ProviderKeys.KEY_NAME] = key_name[lang]
                if description:
                    config[ProviderKeys.DESCRIPTION] = description[lang]
                if note:
                    config[ProviderKeys.NOTE] = note[lang]

        return results
