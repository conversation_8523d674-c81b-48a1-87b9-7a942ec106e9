import math

from bson import ObjectId

from src.common import CommonKeys
from src.common.utils import get_time_now
from src.models import BaseModel


class LogActionModel(BaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "log_action"

    def insert_log_action(
        self, merchant_id, account_id, provider_config_id, action, status, service, username, fullname, email
    ):
        payload = {
            CommonKeys.CREATED_TIME: get_time_now(),
            "provider_config_id": provider_config_id,
            "merchant_id": merchant_id,
            "created_by": account_id,
            "action": action,
            "status": status,
            "type": service,
            "username": username,
            "fullname": fullname,
            "email": email,
        }
        return self.insert(payload).inserted_id

    def get_logs(self, query, order, sort, page, per_page):
        results = list(self.find_paginate(query, page, per_page, sort_option=sort, order=order))
        total_count = self.count(query)
        total_page = math.ceil(total_count / per_page)
        return results, {
            "page": page,
            "per_page": per_page,
            "total_page": total_page,
            "total_count": total_count,
        }

    def find_paginate_load_more(
        self, search_option, per_page=10, after_token=None, sort_option=None, projection=None
    ):
        """
        Support sort field type: number, _id
        :param search_option:
        :param per_page:
        :param after_token:
        :param sort_option: (chỉ support sort 1 field)
        :param projection: (Bao gồm field được sort)
        :return:
        """
        if not sort_option:
            sort_option = [("_id", 1)]
        else:
            check_id = next((sort for sort in sort_option if sort[0] == "_id"), None)
            order_type = next(sort[1] for sort in sort_option)
            if not check_id:
                sort_option.append(("_id", order_type))
        sort_keys = [k[0] for k in sort_option]

        if after_token:
            token = self.parse_token(after_token)
            if len(sort_option) == 1:
                if sort_option[0][0] == "_id":
                    token[0] = ObjectId(token[0])
                if sort_option[0][1] == 1:
                    search_extend = {sort_option[0][0]: {"$gt": token[0]}}
                else:
                    search_extend = {sort_option[0][0]: {"$lt": token[0]}}
            else:
                filter_id = {}
                for index, sort in enumerate(sort_option):
                    if not token[index]:
                        continue
                    if sort[0] == "_id":
                        token[index] = ObjectId(token[index])
                        if sort[1] == 1:
                            filter_id = {sort[0]: {"$gt": token[index]}}
                        else:
                            filter_id = {sort[0]: {"$lt": token[index]}}

                condition_compare = {}
                condition_combine = {}
                for index, sort in enumerate(sort_option):
                    if sort[0] != "_id":
                        condition_combine.update(
                            {
                                sort[0]: token[index],
                            }
                        )

                        if sort[1] == 1:
                            condition_compare.update({sort[0]: {"$gt": token[index]}})
                        else:
                            condition_compare.update({sort[0]: {"$lt": token[index]}})
                condition_combine.update(filter_id)
                lst_search_extend = [condition_compare, condition_combine]

                search_extend = {"$or": lst_search_extend}

            if search_option:
                search_option.update(search_extend)
            else:
                search_option = search_extend

        collection = self.collector().find(search_option, projection).limit(per_page)
        if sort_option:
            collection = collection.sort(sort_option)
        data = [x for x in collection]
        next_token = None
        if data:
            last_item = data[-1]
            next_token = self.generate_after_token(last_item, sort_keys)

        return data, next_token