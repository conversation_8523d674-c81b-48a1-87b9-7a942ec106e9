# -*- coding: utf-8 -*-
"""
Author: TruongNV
Company: MobioVN
Created Date: 08/11/2019
Describe:
"""
######################### Report Profile Detail  ###########################
# version: 1.1.0
#################################################################
"""
@api {get} /events/api/v1.0/report_profile_detail/get_list_activity  E38 - Activity Detail -- Email 
@apiDescription API report activity in detail on Email_MKT and Email_single module of profile
@apiVersion 1.1.0
@apiGroup Profile Detail
@apiName E38 Activity Detail -- Email

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)   {string}            profile_id                 UUID của profile  
@apiParam (Query:)   {string}               [event_type]               Event code của event cần lấy
<li>Allowed values: 
    <ul>
        <li><code>Email</code>: single email + email của MKT</li>
    </ul>
</li>
@apiParam (Query:)   {int}               [from_time]                Timestamp thời điểm bắt đầu filter (lấy theo giờ UTC)
<li><code>Lấy đến second</code></li>
<li>Default value: <code>thời điểm hiện tại</code></li>
@apiParam (Query:)   {int}               [to_time]                  Timestamp thời điểm cuối cùng filter (lấy theo giờ UTC) 
<li><code>Lấy đến second</code></li> 
<li>Default value: <code>0</code></li>      
@apiParam (Query:)   {int}               [per_page]                 Số lượng item lấy trong page
<li>Default value: <code>5</code></li> 
@apiParam (Query:)   {int}               [last_action_time]     Timespamp của bản ghi cuối cùng trong page trước
<li>Default value: <code>page đầu tiên</code></li>
@apiParam (Query:)   {string}            [account_id]            Id của account đang log-in 
<li>Default value: <code>"tương tác từ tất cả"</code></li>


@apiSuccess                   {array}               data                                Danh sách note của khách hàng
@apiSuccess                   {int}                 code                                Mã response
@apiSuccess                   {text}                lang                                Ngôn ngữ hiển 
@apiSuccess                   {text}                message                             Thông tin từ hệ thống

@apiSuccess (data)            {float}               last_action_time                   Created_time của bản tin cuối cùng trong page
<li><code>-1: Trường hợp page cuối cùng không có item nào </code></li>
@apiSuccess (data)            {int}                 last_page                           Trạng thái của paging
<li><code>1: page cuối cùng</code></li>
<li><code>0: chưa phải page cuối</code></li>
@apiSuccess (data)            {array}               event_list                           Thông tin của các bản ghi trong page


<!--#################################################-->
<!--################## Email           ##############-->
<!--################## source = MKT    ##############-->
<!--#################################################-->

@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          event_type                 Tên event 
<li>Default value: <code>Email</code></li>
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          source_event               Tên source của event
<li>Default value: <code>MKT</code></li>
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          merchant_id                 ID merchant
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          profile_id                  ID profile
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {float}           action_time                Thời gian xảy ra action
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          action_type                Loại hành động tương tác 
<li>Allow values: 
    <ul>
        <li><code>"sent"</code></li>
        <li><code>"open"</code></li>
        <li><code>"click_link"</code></li>
        <li><code>"fill_form"</code></li>
    </ul>
</li>
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          email_title                   Title email đã gửi cho khách hàng
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          [campaign_name]            Tên chiến dịch Marketing
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          [master_campaign_id]             ID  MasterCampaign
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          [root_message_id]         Tham số query nội dung mail mkt
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          [campaign_id]             Tham số query nội dung mail mkt
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          [created_account_id]          
@apiSuccess ([event_list] - khi event_type = Email, source_event = MKT)       {string}          [receiver]              Địa chỉ email profile nhận tin email


<!--#################################################-->
<!--################## Email                ##############-->
<!--################## source = SingleEmail ##############-->
<!--#################################################-->

@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          event_type                 Tên event 
<li>Default value: <code>Email</code></li>
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          source_event               Tên source của event
<li>Default value: <code>SingleEmail</code></li>
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          merchant_id                 ID merchant
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          profile_id                  ID profile
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {float}           action_time                Thời gian xảy ra action
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          action_type                Loại hành động tương tác 
<li>Allow values: 
    <ul>
        <li><code>"single_mail"</code></li>
    </ul>
</li>
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          created_account_id          Nhân viên khởi tạo single mail
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          current_status             Trạng thái xử lý của single email
<li>Allow values: 
    <ul>
        <li><code>"processing"</code></li>
        <li><code>"sent"</code></li>
        <li><code>"error"</code></li>
        <li><code>"user_open_mail"</code></li>
    </ul>
</li>          
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          email_title                   Title email đã gửi cho khách hàng
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          [email_body]             Nội dung email gửi đi, trong trường hợp single_mail
@apiSuccess ([event_list] - khi event_type = Email, source_event = SingleEmail)       {string}          [list_email_sent]             Danh sách các email của profile đã được gửi trong sinle-mailnull


@apiSuccessExample     {json}    Response: Email
{
    "code": 200,
    "data": {
        "last_action_time": 1550222834.33679,
        "last_page": 1,
        "lang": "vi",
        "message": "request thành công."
        "event_list": [
            # source_event = MKT
            {
                "event_type": "Email",
                "source_event": "MKT",
                "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                "profile_id": "d7546bdd-cb73-46dd-88af-d2365bb31782",
                "action_time": **********.144767,
                "action_type": "open",
                "campaign_name": "[31/10] Test gửi e-mail",    
                "email_title": "Gửi e-mail 1",    
                "master_campaign_id": "d7546bdd-cb73-46dd-88af-d2365bb31782",
                "root_message_id": "0ad4f66b-6eb2-492f-b6fc-7ba766c57009",
                "campaign_id": "ee8f0a7a-3c68-4008-bbe4-71d5970c1c61",
                "created_account_id": null,
                "receiver": "<EMAIL>",
            },
            
            # source_event = SingleEmail
            {
                "event_type": "Email",
                "source_event": "SingleEmail"
                "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                "profile_id": "4699c36f-1867-4f27-9b0e-af26128e701b",
                "action_time": **********.253684,
                "action_type": "single_mail",
                "created_account_id": "72f2adce-e38b-4904-a952-ba30605416f7",
                "current_status": "sent",
                "email_body": "<p>&nbsp;Năm n&agraveasdas nhất.</p>\n",
                "email_title": "Năm nà2 ",
                "list_email_sent": [
                  "<EMAIL>"
                ]
            },
            ....
        ]
    },

}

"""