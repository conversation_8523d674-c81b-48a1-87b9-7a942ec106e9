from datetime import timedelta

import dns.resolver
import schedule
from mobio.libs.logging import <PERSON><PERSON>Logging
from mobio.libs.schedule import BaseScheduler

from configs import MarketPlaceApplicationConfig
from src.common import (
    CommonKeys,
    DnsRecordKeys,
    ProviderConfigKeys,
    ProviderKeys,
    ServiceKeys,
    mobio_notify_sdk,
)
from src.internal_module.mobio_mailer import <PERSON><PERSON>MailerHelper
from src.internal_module.notify_management import NotifyManagementHelper
from src.models.log_action import LogActionModel
from src.models.provider_config_model import ProviderConfigModel
from src.models.provider_model import ProviderModel

resolver = dns.resolver.Resolver()


class CheckDnsRecordIsDeletedScheduler(BaseScheduler):
    def get_schedule(self):
        return schedule.every(7).days

    def owner_do(self):
        CheckDnsRecordIsDeletedScheduler.handle_process_check_record_is_deleted()

    @staticmethod
    def get_dns_records(domain, record_type="A"):
        try:
            dns_records = resolver.resolve(domain, record_type)
        except:
            return []
        return [record.target.to_text().strip(".") for record in dns_records]

    @staticmethod
    def send_mail_noti_verify_fail(provider_config):
        verify_by = provider_config.get(ProviderConfigKeys.VERIFY_BY)
        created_by = provider_config.get(CommonKeys.CREATED_BY)
        merchant_id = provider_config.get(CommonKeys.MERCHANT_ID)
        domain = provider_config.get(ProviderConfigKeys.NAME)

        send_to = verify_by if verify_by else created_by

        mobio_notify_sdk.send_message_notify_email(
            sender_name="Mobio Alert",
            merchant_id=merchant_id,
            key_config="market_place_verify_domain_email_fail",
            domain=domain,
            url_config_email=f"{MarketPlaceApplicationConfig.BASE_HOST}setting/tenant/integration/channel-send-message/email?license=activation",
            url_file=f"{MarketPlaceApplicationConfig.BASE_HOST}setting/tenant/integration/channel-send-message/email?license=activation",
            account_ids=[send_to],
        )
        return True

    @staticmethod
    def thread_handle_fail_config_when_delete_record_verify(provider_config, connect_config_info):
        # send request update fail config to Notify Management
        log_action_model: LogActionModel = LogActionModel()
        notify_management_helper: NotifyManagementHelper = NotifyManagementHelper()

        provider_config_id = str(provider_config.get(ProviderConfigKeys.ID))
        merchant_id = provider_config.get(CommonKeys.MERCHANT_ID)
        domain = provider_config.get(ProviderConfigKeys.NAME)
        provider_type = (provider_config.get(ProviderConfigKeys.PROVIDER_TYPE),)

        payload_send = {
            ProviderConfigKeys.AUTH_NAME: "",
            ProviderConfigKeys.AUTH_PASS: "",
            ProviderConfigKeys.PROVIDER_API: "",
            ProviderConfigKeys.AUTH_ATTACHMENT: domain,
            ProviderConfigKeys.PROVIDER_TYPE: provider_type,
            ProviderConfigKeys.STATUS: 2,
            ProviderConfigKeys.OTHERS: {},
        }

        for config in connect_config_info:
            payload_send[config.get(ProviderConfigKeys.ConfigInfoKeys.FIELD)] = config.get(
                ProviderConfigKeys.ConfigInfoKeys.VALUE
            )

        notify_management_helper.upsert_provider_config(provider_config.get(CommonKeys.MERCHANT_ID), payload_send)
        MobioLogging().debug(
            "VerifyDnsActiveScheduler :: thread_handle_fail_config_when_delete_record_verify :: %s :: deactivate"
            % provider_config.get(ProviderConfigKeys.NAME)
        )
        # Send email for user status success
        CheckDnsRecordIsDeletedScheduler.send_mail_noti_verify_fail(provider_config)
        log_action_model.insert_log_action(
            merchant_id,
            "admin",
            provider_config_id,
            "verify",
            0,
            "email",
            "admin",
            "admin",
            "admin",
        )
        return {ProviderConfigKeys.STATUS: 0}

    @staticmethod
    def handle_process_check_record_is_deleted():
        mobio_mailer_helper: MobioMailerHelper = MobioMailerHelper()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        provider_model: ProviderModel = ProviderModel()

        provider_configs = list(
            provider_config_model.find(
                {
                    ProviderConfigKeys.STATUS: 1,  # success
                    ProviderConfigKeys.PROVIDER_TYPE: 212,
                }
            )
        )

        mobio_mailer_provider = provider_model.get_provider_by_code(provider_type=212, service=ServiceKeys.EMAIL)
        host = mobio_mailer_provider.get(ProviderKeys.HOST)
        token = mobio_mailer_provider.get(ProviderKeys.TOKEN)
        connect_config_info = mobio_mailer_provider.get(ProviderKeys.CONNECT_CONFIG_INFO)

        for provider_config in provider_configs:
            provider_id = str(provider_config.get(ProviderConfigKeys.ID))
            domain = provider_config.get(ProviderConfigKeys.NAME)
            merchant_id = provider_config.get(CommonKeys.MERCHANT_ID)
            dns_records = provider_config.get(ProviderConfigKeys.DNS_RECORDS, [])

            checker = True
            for dns_record in dns_records:
                record_name = dns_record.get(DnsRecordKeys.RECORD_NAME)
                record_value = dns_record.get(DnsRecordKeys.RECORD_VALUE)
                record_type = dns_record.get(DnsRecordKeys.RECORD_TYPE)
                value_endpoint = provider_config.get(DnsRecordKeys.VALUE_ENDPOINT)

                name_query = record_name if not value_endpoint else record_value
                value_compare = record_value if not value_endpoint else value_endpoint
                value_pointed = CheckDnsRecordIsDeletedScheduler.get_dns_records(name_query, record_type)
                MobioLogging().info(
                    "CheckDnsRecordIsDeletedScheduler :: handle_process_check_record_is_deleted :: merchant_id :: %s :: domain :: %s :: record :: %s :: value_compare :: %s :: value_pointed :: %s"
                    % (merchant_id, domain, dns_record, value_compare, value_pointed)
                )

                if value_compare not in value_pointed:
                    checker = False
                    MobioLogging().debug(
                        "CheckDnsRecordIsDeletedScheduler :: handle_process_check_record_is_deleted :: merchant_id :: %s :: domain :: %s :: record :: %s :: value_compare :: %s :: value_pointed :: %s"
                        % (merchant_id, domain, dns_record, value_compare, value_pointed)
                    )

            if not checker:
                payload_send = {
                    CommonKeys.MERCHANT_ID: merchant_id,
                    ProviderConfigKeys.DOMAIN: domain,
                }
                response = mobio_mailer_helper.send_request_check_status_domain(
                    host=host, token=token, merchant_id=merchant_id, payload=payload_send
                )
                MobioLogging().debug(
                    "CheckDnsRecordIsDeletedScheduler :: handle_process_check_record_is_deleted :: response :: %s"
                    % response
                )
                status = response.get("status")

                if status == "fail":
                    data_update = CheckDnsRecordIsDeletedScheduler.thread_handle_fail_config_when_delete_record_verify(
                        provider_config, connect_config_info
                    )
                    provider_config_model.update_provider_config_by_id(provider_id, data_update, "admin")
        MobioLogging().debug("CheckDnsRecordIsDeletedScheduler :: handle_process_check_record_is_deleted :: done")
        return


if __name__ == "__main__":
    CheckDnsRecordIsDeletedScheduler.handle_process_check_record_is_deleted()
