from datetime import timedelta
import dns.resolver
import schedule
from mobio.libs.logging import Mo<PERSON>Logging
from mobio.libs.schedule import BaseScheduler
from mobio.sdks.admin import MobioAdminSDK
from configs import MarketPlaceApplicationConfig, RedisConfig
from src.common import CommonKeys, DnsRecordKeys, ProviderConfigKeys, ProviderKeys, mobio_notify_sdk
from src.common.utils import get_time_now, to_as_tzinfo
from src.internal_module.mobio_mailer import <PERSON><PERSON>MailerHelper
from src.internal_module.notify_management import NotifyManagementHelper
from src.models.log_action import LogActionModel
from src.models.provider_config_model import ProviderConfigModel
from src.models.provider_model import ProviderModel

resolver = dns.resolver.Resolver()


class VerifyDnsActiveScheduler(BaseScheduler):
    def get_schedule(self):
        return schedule.every(5).minutes

    def owner_do(self):
        VerifyDnsActiveScheduler.handle_process_verify_dns()

    @staticmethod
    def get_dns_records(domain, record_type: str = "A"):
        try:
            dns_records = resolver.resolve(domain, record_type)
        except:
            return []
        return [record.target.to_text().strip(".") for record in dns_records]

    @staticmethod
    def send_mail_noti_verify_fail(provider_config):
        verify_by = provider_config.get("verify_by")
        created_by = provider_config.get("created_by")
        merchant_id = provider_config.get(CommonKeys.MERCHANT_ID)
        domain = provider_config.get(ProviderConfigKeys.NAME)

        send_to = verify_by if verify_by else created_by

        MobioLogging().info(
            "VerifyDnsActiveScheduler :: send_mail_noti_verify_fail :: merchant_id :: %s :: domain :: %s :: send_to :: %s"
            % (merchant_id, domain, send_to)
        )
        mobio_notify_sdk.send_message_notify_email(
            sender_name="Mobio Alert",
            merchant_id=merchant_id,
            key_config="market_place_verify_domain_email_fail",
            domain=domain,
            url_config_email=f"{MarketPlaceApplicationConfig.BASE_HOST}setting/tenant/integration/channel-send-message/email?license=activation",
            url_file=f"{MarketPlaceApplicationConfig.BASE_HOST}setting/tenant/integration/channel-send-message/email?license=activation",
            account_ids=[send_to],
        )
        return True

    @staticmethod
    def send_mail_noti_verify_success(provider_config):
        verify_by = provider_config.get(ProviderConfigKeys.VERIFY_BY)
        created_by = provider_config.get(CommonKeys.CREATED_BY)
        merchant_id = provider_config.get(CommonKeys.MERCHANT_ID)
        domain = provider_config.get(ProviderConfigKeys.NAME)

        send_to = verify_by if verify_by else created_by

        MobioLogging().info(
            "VerifyDnsActiveScheduler :: send_mail_noti_verify_success :: merchant_id :: %s :: domain :: %s :: send_to :: %s"
            % (merchant_id, domain, send_to)
        )
        mobio_notify_sdk.send_message_notify_email(
            sender_name="Mobio Alert",
            merchant_id=merchant_id,
            key_config="market_place_verify_domain_email_success",
            domain=domain,
            url_file=f"{MarketPlaceApplicationConfig.BASE_HOST}setting/tenant/integration/channel-send-message/email?license=activation",
            account_ids=[send_to],
        )
        return True

    @staticmethod
    def thread_handle_success_verify(provider_config, *args, **kwargs):
        # send request create config to Notify Management
        log_action_model: LogActionModel = LogActionModel()
        notify_management_helper: NotifyManagementHelper = NotifyManagementHelper()

        connect_config_info = args[0]
        provider_config_id = str(provider_config.get(ProviderConfigKeys.ID))
        merchant_id = provider_config.get(CommonKeys.MERCHANT_ID)
        domain = provider_config.get(ProviderConfigKeys.NAME)
        provider_type = provider_config.get(ProviderConfigKeys.PROVIDER_TYPE)

        payload_send = {
            ProviderConfigKeys.AUTH_NAME: "",
            ProviderConfigKeys.AUTH_PASS: "",
            ProviderConfigKeys.PROVIDER_API: "",
            ProviderConfigKeys.AUTH_ATTACHMENT: domain,
            ProviderConfigKeys.PROVIDER_TYPE: provider_type,
            ProviderConfigKeys.STATUS: 1,
            ProviderConfigKeys.OTHERS: {},
        }

        for config in connect_config_info:
            payload_send[config.get(ProviderConfigKeys.ConfigInfoKeys.FIELD)] = config.get(
                ProviderConfigKeys.ConfigInfoKeys.VALUE
            )

        notify_management_helper.upsert_provider_config(provider_config.get(CommonKeys.MERCHANT_ID), payload_send)
        MobioLogging().debug(
            "VerifyDnsActiveScheduler :: thread_handle_success_verify :: verify :: %s :: success"
            % provider_config.get(ProviderConfigKeys.NAME)
        )
        # Send email for user status success
        VerifyDnsActiveScheduler.send_mail_noti_verify_success(provider_config)
        log_action_model.insert_log_action(
            merchant_id,
            "admin",
            provider_config_id,
            "verify",
            1,
            "email",
            "admin",
            "admin",
            "admin"
        )
        return {ProviderConfigKeys.STATUS: 1}

    @staticmethod
    def thread_handle_fail_verify(provider_config, *args, **kwargs):
        provider_config_id = str(provider_config.get(ProviderConfigKeys.ID))
        domain = provider_config.get(ProviderConfigKeys.NAME)
        merchant_id = provider_config.get(CommonKeys.MERCHANT_ID)

        log_action_model: LogActionModel = LogActionModel()
        MobioLogging().debug("VerifyDnsActiveScheduler :: thread_handle_fail_verify :: verify :: %s :: failed" % domain)
        # Send email for user status fail
        VerifyDnsActiveScheduler.send_mail_noti_verify_fail(provider_config)
        log_action_model.insert_log_action(
            merchant_id,
            "admin",
            provider_config_id,
            "verify",
            0,
            "email",
            "admin",
            "admin",
            "admin"
        )
        return {ProviderConfigKeys.STATUS: 0}

    @staticmethod
    def thread_handle_pending_verify(provider_config, *args, **kwargs):
        verify_count = provider_config.get("verify_count")
        domain = provider_config.get(ProviderConfigKeys.NAME)

        MobioLogging().debug(
            "VerifyDnsActiveScheduler :: thread_handle_pending_verify :: verify :: %s :: pending" % domain
        )
        return {ProviderConfigKeys.STATUS: 2, "verify_time_loop": get_time_now(), "verify_count": verify_count + 1}

    @staticmethod
    def handle_process_verify_dns():
        # Khi vừa tạo xong kiểm tra xem nó đã có đủ danh sách bản ghi chưa, nếu chưa thì mới bắt đầu call sang bên Mobio Mailer, và khi active rồi thì mới bắt đầu gửi sang bên Notify Management
        # 3 lần đầu call 5p, những lần sau 6 tiếng
        log_action_model: LogActionModel = LogActionModel()
        mobio_mailer_helper: MobioMailerHelper = MobioMailerHelper()
        provider_config_model: ProviderConfigModel = ProviderConfigModel()
        provider_model: ProviderModel = ProviderModel()

        mobio_mailer_provider = provider_model.find_one({ProviderKeys.PROVIDER_TYPE: 212})

        provider_configs = list(
            provider_config_model.find(
                {
                    ProviderConfigKeys.STATUS: 2,
                    ProviderConfigKeys.PROVIDER_TYPE: 212,
                }
            )
        )

        MobioLogging().debug(
            "VerifyDnsActiveScheduler :: handle_process_verify_dns :: provider_configs :: %s" % provider_configs
        )

        # Xác thực với từng domain
        for provider_config in provider_configs:
            provider_id = str(provider_config.get(ProviderConfigKeys.ID))
            domain = provider_config.get(ProviderConfigKeys.NAME)
            dns_records = provider_config.get(ProviderConfigKeys.DNS_RECORDS, [])
            merchant_id = provider_config.get(CommonKeys.MERCHANT_ID)
            verify_time = provider_config.get(ProviderConfigKeys.VERIFY_TIME)
            verify_count = provider_config.get(ProviderConfigKeys.VERIFY_COUNT)
            verify_time_loop = provider_config.get(ProviderConfigKeys.VERIFY_TIME_LOOP)

            # Sau 72h thì hết hạn verify domain
            if to_as_tzinfo(verify_time) + timedelta(hours=72) <= get_time_now():
                provider_config_model.update_provider_config_by_id(provider_id, {ProviderConfigKeys.STATUS: 0})
                VerifyDnsActiveScheduler.send_mail_noti_verify_fail(provider_config)
                log_action_model.insert_log_action(
                    merchant_id,
                    "admin",
                    provider_id,
                    "verify",
                    0,
                    "email",
                    "admin",
                    "admin",
                    "admin"
                )
                MobioLogging().info(
                    "VerifyDnsActiveScheduler :: handle_process_verify_dns :: domain :: {} :: verify_start_time :: {} :: fail".format(
                        domain, verify_time
                    )
                )
                continue

            # Trong trạng thái kiểm tra domain : 3 lần đầu 5p, những lần sau 6 tiếng
            if (verify_count < 3) or (to_as_tzinfo(verify_time_loop) >= get_time_now() - timedelta(hours=6)):
                verify_check = True
                for dns_record in dns_records:
                    record_type = dns_record.get(DnsRecordKeys.RECORD_TYPE)
                    name = dns_record.get(DnsRecordKeys.RECORD_NAME)
                    value = dns_record.get(DnsRecordKeys.RECORD_VALUE)
                    value_endpoint = dns_record.get(DnsRecordKeys.VALUE_ENDPOINT)

                    name_query = name if not value_endpoint else value
                    value_compare = value if not value_endpoint else value_endpoint
                    value_pointed = VerifyDnsActiveScheduler.get_dns_records(name_query, record_type)
                    MobioLogging().info(
                        "VerifyDnsActiveScheduler :: handle_process_verify_dns :: name_query :: {} :: value_compare :: {} :: value_pointed :: {}".format(
                            name_query, value_compare, value_pointed
                        )
                    )

                    if value_compare not in value_pointed:
                        verify_check = False
                        MobioLogging().debug(
                            "VerifyDnsActiveScheduler :: handle_process_verify_dns :: name_query :: {} :: value_compare :: {} :: value_pointed :: {} :: record_value_not_match".format(
                                name_query, value_compare, value_pointed
                            )
                        )

                # update lại giờ lặp và số lần verify_count
                if not verify_check:
                    provider_config_model.update_provider_config_by_id(
                        provider_id, {"verify_time_loop": get_time_now(), "verify_count": verify_count + 1}
                    )
                    MobioLogging().debug(
                        "VerifyDnsActiveScheduler :: handle_process_verify_dns :: verify :: %s :: pending" % domain
                    )
                    continue

                # verify_thành công
                # Send request to mobio mailer check status
                payload_send = {CommonKeys.MERCHANT_ID: merchant_id, ProviderConfigKeys.DOMAIN: domain}

                token = mobio_mailer_provider.get(ProviderKeys.TOKEN)
                host = mobio_mailer_provider.get(ProviderKeys.HOST)
                connect_config_info = mobio_mailer_provider.get(ProviderKeys.CONNECT_CONFIG_INFO)

                response = mobio_mailer_helper.send_request_check_status_domain(host, token, merchant_id, payload_send)
                MobioLogging().info(
                    "VerifyDnsActiveScheduler :: handle_process_verify_dns :: send_request_check_status_domain :: response"
                    % response
                )
                if not response:
                    continue

                # send request verify to Mobio Mailer
                status_response = response.get("status")
                status_mapping = {
                    "fail": VerifyDnsActiveScheduler.thread_handle_fail_verify,
                    "success": VerifyDnsActiveScheduler.thread_handle_success_verify,
                    "pending": VerifyDnsActiveScheduler.thread_handle_pending_verify,
                }
                data_update = status_mapping.get(status_response)(provider_config, connect_config_info)
                MobioLogging().info(
                    "VerifyDnsActiveScheduler :: handle_process_verify_dns :: send_request_check_status_domain :: data_update"
                    % data_update
                )

                # update status
                provider_config_model.update_provider_config_by_id(provider_id, data_update)
                return
        MobioLogging().debug("VerifyDnsActiveScheduler :: handle_process_verify_dns :: done")
        return


if __name__ == "__main__":
    VerifyDnsActiveScheduler(redis_uri=RedisConfig.REDIS_URI).handle_process_verify_dns()
