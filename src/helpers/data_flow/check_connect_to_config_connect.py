#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 26/06/2024
"""


import requests
from bson import ObjectId
from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import LANG, lru_redis_cache
from src.common.data_flow_constant import (
    ConstantParamApi,
    ConstantStatusCheckConnection,
    ConstantStatusConnectionDatabases,
)
from src.common.get_func_name import GetFuncNameHandler
from src.common.utils import convert_isoformat_to_date
from src.helpers.data_flow.connect_config_database import HelperConnectConfigDatabase
from src.helpers.integration_account.google_hepler import IntegrationAccountGoogleHelper
from src.internal_module.data_out import DataOutHelper
from src.models.integration_account.integration_account_model import (
    IntegrationAccountModel,
)


class ConstantHttpStatusCode:
    OK = 200


class HelperCheckConnection(object):
    def call_check_connection(self, source_key, source_type, data_type, language, config_connect, merchant_id):
        func_check_connection = GetFuncNameHandler().get_func_name_check_connect_connection(
            source_key, source_type, data_type
        )
        MobioLogging().info(f"Calling Check Connection: {func_check_connection}")
        try:
            return getattr(self, func_check_connection)(language, config_connect, merchant_id)
        except AttributeError:
            raise CustomError(f"Func Check Connection {func_check_connection} not found")

    @lru_redis_cache.add_for_class(expiration=300)
    def check_connection_data_in_databases_postgres(self, language, config_connect, merchant_id):
        connect_information = []
        status_connect = ConstantStatusConnectionDatabases.SUCCESS
        result_check_connection = HelperConnectConfigDatabase.postgres_check_connection(config_connect)
        if result_check_connection:
            for key, value in result_check_connection.items():
                status = value.get("status")
                information = {
                    "name": language.get(key).get("title"),
                    "status": (
                        ConstantStatusConnectionDatabases.SUCCESS if status else ConstantStatusConnectionDatabases.FAIL
                    ),
                }
                if not status:
                    status_connect = ConstantStatusConnectionDatabases.FAIL
                    information["errors"] = [
                        language.get(key)
                        .get("message")
                        .format(
                            host=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_HOST),
                            port=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_PORT),
                            username=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_USERNAME
                            ),
                            password=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_PASSWORD
                            ),
                            database=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_NAME
                            ),
                        )
                    ]

                connect_information.append(information)
                if not status:
                    break
        return status_connect, connect_information

    @lru_redis_cache.add_for_class(expiration=300)
    def check_connection_data_in_server_api(self, language, config_connect, merchant_id):
        return ConstantStatusCheckConnection.SUCCESS, ""

    @lru_redis_cache.add_for_class(expiration=300)
    def check_connection_data_in_form_contact_form(self, language, config_connect, merchant_id):
        return ConstantStatusCheckConnection.SUCCESS, ""

    @lru_redis_cache.add_for_class(expiration=300)
    def check_connection_data_in_databases_mysql(self, language, config_connect, merchant_id):
        connect_information = []
        status_connect = ConstantStatusConnectionDatabases.SUCCESS
        result_check_connection = HelperConnectConfigDatabase.mysql_check_connection(config_connect)
        if result_check_connection:
            for key, value in result_check_connection.items():
                status = value.get("status")
                information = {
                    "name": language.get(key).get("title"),
                    "status": (
                        ConstantStatusConnectionDatabases.SUCCESS if status else ConstantStatusConnectionDatabases.FAIL
                    ),
                }
                if not status:
                    status_connect = ConstantStatusConnectionDatabases.FAIL
                    information["errors"] = [
                        language.get(key)
                        .get("message")
                        .format(
                            host=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_HOST),
                            port=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_PORT),
                            username=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_USERNAME
                            ),
                            password=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_PASSWORD
                            ),
                            database=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_NAME
                            ),
                        )
                    ]

                connect_information.append(information)
                if not status:
                    break
        return status_connect, connect_information

    # @lru_redis_cache.add_for_class(expiration=300)
    def check_connection_data_in_databases_oracle(self, language, config_connect, merchant_id):
        connect_information = []
        status_connect = ConstantStatusConnectionDatabases.SUCCESS
        result_check_connection = HelperConnectConfigDatabase.oracle_check_connection(config_connect)
        if result_check_connection:
            for key, value in result_check_connection.items():
                status = value.get("status")
                information = {
                    "name": language.get(key, {}).get("title"),
                    "status": (
                        ConstantStatusConnectionDatabases.SUCCESS if status else ConstantStatusConnectionDatabases.FAIL
                    ),
                }
                if not status:
                    status_connect = ConstantStatusConnectionDatabases.FAIL
                    information["errors"] = [
                        language.get(key)
                        .get("message")
                        .format(
                            host=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_HOST),
                            port=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_PORT),
                            username=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_USERNAME
                            ),
                            password=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_PASSWORD
                            ),
                            database=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_NAME
                            ),
                        )
                    ]

                connect_information.append(information)
                if not status:
                    break
        return status_connect, connect_information

    @lru_redis_cache.add_for_class(expiration=300)
    def check_connection_data_out_server_webhooks(self, language, config_connect, merchant_id):
        connect_information = []
        status_connect = ConstantStatusCheckConnection.FAIL

        url_webhooks = config_connect.get(ConstantParamApi.BodyWebhookCheckConnection.CONFIG_URL)

        param_headers = config_connect.get(ConstantParamApi.BodyWebhookCheckConnection.CONFIG_CONNECT_PARAM_HEADERS)
        parse_header = {v["key"].strip().encode("utf-8"): v["value"].strip().encode("utf-8") for v in param_headers}
        errors = []
        status_code = "Không xác định"
        try:
            result_check_connection = requests.get(url_webhooks, headers=parse_header, timeout=2)
            result_check_connection.raise_for_status()
            status_code = ConstantHttpStatusCode.OK
            status_connect = ConstantStatusCheckConnection.SUCCESS
        except ConnectionError as ce:
            MobioLogging().error(f"ConnectionError: {ce}")
            errors.append(f"Không thể kết nối tới máy chủ. Chi tiết lỗi :: {ce}")
        except requests.Timeout as te:
            MobioLogging().error(f"TimeoutError: {te}")
            errors.append(f"Yêu cầu đã bị vượt quá thời gian chờ. Chi tiết lỗi :: {te}")
        except requests.HTTPError as he:
            status_code = he.response.status_code
            MobioLogging().error(f"HTTPError: {he.response.status_code}")
            errors.append(f"Đã xảy ra lỗi HTTP: {he.response.status_code}. Chi tiết lỗi :: {he.response}")
        except requests.RequestException as re:
            MobioLogging().error(f"RequestException: {re}")
            errors.append(f"Đã xảy ra lỗi trong quá trình yêu cầu. Chi tiết lỗi :: {re}")
        except Exception as e:

            raise CustomError(str(e))

        connect_information = {
            "name": language.get("status_code_check_connect_webhooks")
            .get("title")
            .format(status_code=(status_code if status_code != ConstantHttpStatusCode.OK else "200 OK")),
            "status": ConstantStatusCheckConnection.SUCCESS,
        }
        if status_code != ConstantHttpStatusCode.OK:
            status_connect = ConstantStatusCheckConnection.FAIL
            connect_information.update({"status": ConstantStatusCheckConnection.FAIL, "errors": errors})
            return status_connect, [connect_information]

        body = {
            "app_id": "default",
            "app_secret": "default",
            "event_key": "default",
            "data_test": {},
            "headers": param_headers,
            "webhook_url": url_webhooks,
            "connector_id": "",
        }
        MobioLogging().info("DataFlowController :: send_data_test_data_out :: body :: {}".format(body))
        # status_connect, lst_connect_information = DataOutHelper().send_data_test_data_out(merchant_id, body)
        return status_connect, [connect_information]

    # @lru_redis_cache.add_for_class(expiration=300)
    def check_connection_data_out_raw_data_google_sheet(self, language, config_connect, merchant_id):
        lst_connect_information = []
        status_connect = ConstantStatusCheckConnection.FAIL

        integration_account_id = config_connect.get(
            ConstantParamApi.BodyGoogleSheetConfigConnector.INTEGRATION_ACCOUNT_ID
        )
        type_use_spreadsheet = config_connect.get(ConstantParamApi.BodyGoogleSheetConfigConnector.TYPE_USE_SPREADSHEET)
        spreadsheet_id = config_connect.get(ConstantParamApi.BodyGoogleSheetConfigConnector.SPREADSHEET_ID)
        sheet_id = config_connect.get(ConstantParamApi.BodyGoogleSheetConfigConnector.SHEET_ID)
        folder_id = config_connect.get(ConstantParamApi.BodyGoogleSheetConfigConnector.FOLDER_ID)
        integration_account = IntegrationAccountModel().get_by_id(merchant_id, integration_account_id)
        if not integration_account:
            return status_connect, lst_connect_information

        integration_account_config = integration_account.get("config", {})
        folder_drive_create_id = integration_account_config.get("folder_drive_create_id")
        if folder_id and folder_id != folder_drive_create_id:
            return status_connect, lst_connect_information

        # if not folder_id:
        #     raise CustomError("Folder ID not found in integration account config")
        (
            spreadsheet,
            new_token,
            status_trashed,
        ) = IntegrationAccountGoogleHelper().detail_sheets_in_spreadsheet(spreadsheet_id, integration_account_config)
        if not spreadsheet:
            raise CustomError(language.get(LANG.SPREADSHEET_NOT_EXIST).get("message"))
        sheets = spreadsheet.get("sheets")
        if not sheets:
            lst_connect_information.append(
                {
                    "status": ConstantStatusCheckConnection.FAIL,
                    "message": "not_exist_sheet",
                }
            )
            return status_connect, lst_connect_information
        exist_sheet = False
        for sheet in sheets:
            s_id = sheet.get("properties", {}).get("sheetId")
            if s_id == int(sheet_id):
                exist_sheet = True
                break
        if not exist_sheet:
            lst_connect_information.append(
                {
                    "status": ConstantStatusCheckConnection.FAIL,
                    "message": "not_exist_sheet",
                }
            )
            return status_connect, lst_connect_information

        if new_token:
            expiry = convert_isoformat_to_date(new_token.get("expiry"))
            new_token["expiry"] = expiry
            integration_account_config.update(new_token)
            # Log and update the integration account config
            MobioLogging().info("integration_account_config :: {}".format(integration_account_config))
            IntegrationAccountModel().update_by_set(
                {"_id": ObjectId(integration_account_id)},
                {"config": integration_account_config},
            )
        status_connect = ConstantStatusCheckConnection.SUCCESS
        return status_connect, lst_connect_information

    # @lru_redis_cache.add_for_class(expiration=300)
    def check_connection_data_in_databases_db2(self, language, config_connect, merchant_id):
        connect_information = []
        status_connect = ConstantStatusConnectionDatabases.SUCCESS
        result_check_connection = HelperConnectConfigDatabase.db2_check_connection(config_connect)
        if result_check_connection:
            for key, value in result_check_connection.items():
                status = value.get("status")
                information = {
                    "name": language.get(key).get("title"),
                    "status": (
                        ConstantStatusConnectionDatabases.SUCCESS if status else ConstantStatusConnectionDatabases.FAIL
                    ),
                }
                if not status:
                    status_connect = ConstantStatusConnectionDatabases.FAIL
                    information["errors"] = [
                        language.get(key)
                        .get("message")
                        .format(
                            host=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_HOST),
                            port=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_PORT),
                            username=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_USERNAME
                            ),
                            password=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_PASSWORD
                            ),
                            database=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_NAME
                            ),
                        )
                    ]

                connect_information.append(information)
                if not status:
                    break
        return status_connect, connect_information

    # @lru_redis_cache.add_for_class(expiration=300)
    def check_connection_data_in_databases_sqlserver(self, language, config_connect, merchant_id):
        connect_information = []
        status_connect = ConstantStatusConnectionDatabases.SUCCESS
        result_check_connection = HelperConnectConfigDatabase.sqlserver_check_connection(config_connect)
        if result_check_connection:
            for key, value in result_check_connection.items():
                status = value.get("status")
                information = {
                    "name": language.get(key).get("title"),
                    "status": (
                        ConstantStatusConnectionDatabases.SUCCESS if status else ConstantStatusConnectionDatabases.FAIL
                    ),
                }
                if not status:
                    status_connect = ConstantStatusConnectionDatabases.FAIL
                    information["errors"] = [
                        language.get(key)
                        .get("message")
                        .format(
                            host=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_HOST),
                            port=config_connect.get(ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_PORT),
                            username=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_USERNAME
                            ),
                            password=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_PASSWORD
                            ),
                            database=config_connect.get(
                                ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_NAME
                            ),
                        )
                    ]

                connect_information.append(information)
                if not status:
                    break
        return status_connect, connect_information