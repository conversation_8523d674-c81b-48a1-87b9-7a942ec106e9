#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 27/03/2024
"""
import copy
import re

import ibm_db_dbi
from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError
from psycopg2 import OperationalError, connect

from src.apis import LANG

VALID_COLUMN = r"^[a-zA-Z][a-zA-Z0-9_]+$"

MAPPING_ERROR = {
    "connect_database": {
        "word_check": [
            "connection refused",
            "timeout expired",
            "nodename nor servname provided",
            "name or service not known",
            "invalid port number",
            "invalid integer value",
            "can't connect to",
            "received invalid response to SSL negotiation",
            "destination host unreachable",
            "cannot connect",
            "connect failed because target host or object does not exist",
            "communication function detecting the error",
            "sqlcode=-1336",
            "cannot open database",
        ],
        "status": True,
    },
    "authorization": {
        "word_check": [
            "password authentication failed for use",
            " invalid username/password; logon denied",
            "username and/or password invalid",
            "login failed for user",
        ],
        "status": True,
    },
    "database_not_exist": {
        "word_check": [
            'database "{}" does not exist',
            "cannot connect to database",
            "sqlcode=-30061",
            "cannot open database",
        ],
        "status": True,
    },
    "not_permission_read_database": {"word_check": ["not exist"], "status": True},
}

DATATYPE_SUPPORT = {
    "db2": {
        "supported": [
            "SMALLINT",
            "INTEGER",
            "BIGINT",
            "REAL",
            "DOUBLE",
            "DECIMAL",
            "VARCHAR",
            "CHAR",
            "CHARACTER",
            "GRAPHIC",
            "VARGRAPHIC",
            "BOOLEAN",
            "DATE",
            "TIMESTAMP",
        ], 
        "not_supported": [],
        "type_mapping": {
            "TINYINT": [],
            "SMALLINT": ["SMALLINT"],
            "INT": ["INTEGER"],
            "BIGINT": ["BIGINT"],
            "FLOAT": ["REAL"],
            "DOUBLE": ["DOUBLE"],
            "DECIMAL": ["DECIMAL"],
            "STRING": ["VARCHAR", "CHAR", "CHARACTER", "GRAPHIC", "VARGRAPHIC"],
            "BOOLEAN": ["BOOLEAN"],
            "DATE": ["DATE"],
            "TIMESTAMP": ["TIMESTAMP"]
        }
    },
    "oracle": {
        "supported": [
            "SMALLINT",
            "BINARY_FLOAT",
            "REAL",
            "BINARY_DOUBLE",
            "FLOAT",
            "NUMBER",
            "CHAR",
            "NCHAR",
            "NVARCHAR2",
            "VARCHAR",
            "VARCHAR2",
            "CLOB",
            "NCLOB",
            "DATE",
            "TIMESTAMP",
            "CHARACTER",
            "TIMESTAMP(3)",
            "TIMESTAMP(6)",
            "TIMESTAMP(9)",
        ],
        "not_supported": [
            "TIMESTAMP(3) WITH TIME ZONE",
            "TIMESTAMP(3) WITH LOCAL TIME ZONE",
            "TIMESTAMP(6) WITH TIME ZONE",
            "TIMESTAMP(6) WITH LOCAL TIME ZONE",
            "TIMESTAMP(9) WITH TIME ZONE",
            "TIMESTAMP(9) WITH LOCAL TIME ZONE"
        ],
        "type_mapping": {
            "TINYINT": [],
            "SMALLINT": ["SMALLINT"],
            "INT": [], 
            "BIGINT": [],  
            "FLOAT": ["BINARY_FLOAT", "REAL", "FLOAT"],
            "DOUBLE": ["BINARY_DOUBLE"],
            "DECIMAL": ["NUMBER"], 
            "STRING": ["CHAR","NCHAR", "NVARCHAR2", "VARCHAR", "VARCHAR2", "CLOB", "NCLOB", "CHARACTER"
            ],
            "BOOLEAN": [],  
            "DATE": ["DATE"],
            "TIMESTAMP": ["TIMESTAMP","TIMESTAMP(3)", "TIMESTAMP(6)", "TIMESTAMP(9)"]
        } 
    },
    "mysql": {
        "supported": [
            "BIT",
            "TINYINT",
            "BOOLEAN",
            "BOOL",
            "SMALLINT",
            "INT",
            "MEDIUMINT",
            "BIGINT",
            "INT",
            "FLOAT",
            "REAL",
            "DOUBLE",
            "NUMERIC",
            "DECIMAL",
            "CHAR",
            "VARCHAR",
            "TINYTEXT",
            "TEXT",
            "MEDIUMTEXT",
            "LONGTEXT",
            "ENUM",
            "DATE",
            "TIMESTAMP",
            "DATETIME",
        ],
        "not_supported": [],
        "type_mapping": {
            "TINYINT": ["TINYINT"],
            "SMALLINT": ["SMALLINT"],
            "INT": ["INT", "MEDIUMINT"],
            "BIGINT": ["BIGINT"],
            "FLOAT": ["FLOAT", "REAL"],
            "DOUBLE": ["DOUBLE"],
            "DECIMAL": ["DECIMAL", "NUMERIC"],
            "STRING": ["CHAR", "VARCHAR", "TINYTEXT", "TEXT", "MEDIUMTEXT", "LONGTEXT", "ENUM"],
            "BOOLEAN": ["BOOLEAN", "BOOL", "BIT"],
            "DATE": ["DATE"],
            "TIMESTAMP": ["TIMESTAMP", "DATETIME"]
        }
    },
    "postgres": {
        "supported": [
            "SERIAL",
            "INTEGER",
            "INT4",
            "BIGSERIAL",
            "BIGINT",
            "INT8",
            "REAL",
            "FLOAT4",
            "DOUBLE",
            "FLOAT8",
            "DOUBLE PRECISION",
            "DECIMAL",
            "NUMERIC",
            "TEXT",
            "CHARACTER VARYING",
            "VARCHAR",
            "CHARACTER",
            "CHAR",
            "BPCHAR",
            "BOOLEAN",
            "DATE",
            "TIMESTAMP",
            "SERIAL2",
            "SMALLSERIAL",
            "INT2",
            "SMALLINT",
            "TIMESTAMP WITH TIME ZONE",
            "TIMESTAMP WITH LOCAL TIME ZONE",
        ],
        "not_supported": [],
        "type_mapping": {
            "TINYINT": [],
            "SMALLINT": ["INT2", "SMALLINT", "SERIAL2", "SMALLSERIAL"],
            "INT": ["INTEGER", "INT4", "SERIAL"],
            "BIGINT": ["BIGINT", "INT8", "BIGSERIAL"],
            "FLOAT": ["REAL", "FLOAT4"],
            "DOUBLE": ["DOUBLE", "FLOAT8", "DOUBLE PRECISION"],
            "DECIMAL": ["DECIMAL", "NUMERIC"],
            "STRING": ["TEXT", "CHARACTER VARYING", "VARCHAR", "CHARACTER", "CHAR", "BPCHAR"],
            "BOOLEAN": ["BOOLEAN"],
            "DATE": ["DATE"],
            "TIMESTAMP": ["TIMESTAMP", "TIMESTAMP WITH TIME ZONE", "TIMESTAMP WITH LOCAL TIME ZONE"]
        },
    },
    "sqlserver": {
        "supported": [
            "BIGINT",
            "BIT",
            "DECIMAL",
            "INT",
            "MONEY",
            "NUMERIC",
            "SMALLINT",
            "SMALLMONEY",
            "TINYINT",
            "FLOAT",
            "REAL",
            "DATE",
            "DATETIME",
            "DATETIME2",
            "SMALLDATETIME",
            "CHAR",
            "VARCHAR",
            "TEXT",
            "NCHAR",
            "NVARCHAR",
            "NTEXT",
        ],
        "not_supported": ["XML", "HIERARCHYID", "UNIQUEIDENTIFIER", "SQL_VARIANT", "GEOMETRY", "GEOGRAPHY"],
    },
}

INCREMENTAL_COLUMN_TYPE_SUPPORTED = ["TINYINT", "SMALLINT", "INT", "BIGINT", "FLOAT", "DOUBLE", "DECIMAL", "TIMESTAMP" ]


class HelperConnectConfigDatabase:
    @staticmethod
    def _format_data_type(data_type, max_length, precision, scale):
        """
        Format SQL Server data type with precision, scale, and max_length
        """
        data_type = data_type.lower()
        
        # Kiểu dữ liệu số có precision và scale
        if data_type in ["numeric", "decimal", "float", "real"]:
            if precision and scale is not None:
                return f"{data_type}({precision},{scale})"
            elif precision:
                return f"{data_type}({precision})"
        
        # Kiểu char, varchar, nchar, nvarchar có max_length
        elif data_type in ["char", "varchar", "nchar", "nvarchar"]:
            if max_length == -1:
                return f"{data_type}(max)"
            elif max_length:
                # Với nchar và nvarchar, max_length lưu số byte nên cần chia 2 để ra số ký tự
                if data_type.startswith('n'):
                    max_length = max_length // 2
                return f"{data_type}({max_length})"
        
        # Các kiểu binary và varbinary
        elif data_type in ["binary", "varbinary"]:
            if max_length == -1:
                return f"{data_type}(max)"
            elif max_length:
                return f"{data_type}({max_length})"
                
        # Trả về kiểu dữ liệu nguyên bản nếu không có thông số bổ sung
        return data_type

    # @staticmethod
    # def _init_lib_oracle():
    #     import cx_Oracle

    #     try:
    #         if platform.system() == "Darwin":
    #             lib_dir = os.environ.get("LIB_DIR_ORACLE")
    #             if lib_dir:
    #                 cx_Oracle.init_oracle_client(lib_dir=lib_dir)
    #         else:
    #             cx_Oracle.init_oracle_client()
    #     except cx_Oracle.ProgrammingError as e:
    #         if "already been initialized" in str(e):
    #             pass  # Oracle Client library is already initialized
    #         else:
    #             raise  # Re-raise the exception if it's a different error
    #     return cx_Oracle

    # @staticmethod
    # def _int_dns_connect_oracle(*args):
    #     _cx_oracle = HelperConnectConfigDatabase._init_lib_oracle()

    #     # Create a table in Oracle database
    #     dsn = _cx_oracle.makedsn(
    #         args[0]["host"],
    #         args[0]["port"],
    #         sid=args[0]["database_name"],
    #         service_name=args[0]["database_name"],
    #     )
    #     return _cx_oracle, dsn
    @staticmethod
    def normalize_value(val):
        if val is None:
            return None
        if isinstance(val, bytes):
            try:
                decoded = val.decode("utf-8")
                if decoded.isprintable():
                    return decoded
                else:
                    return str(int.from_bytes(val, byteorder="big"))
            except UnicodeDecodeError:
                return str(int.from_bytes(val, byteorder="big"))
        elif isinstance(val, memoryview):
            bytes_data = bytes(val)  # chuyển sang bytes
            try:
                text = bytes_data.decode("utf-8")  # nếu là chuỗi
                return text
            except UnicodeDecodeError:
                # nếu là binary như ảnh, file nhị phân
                return bytes_data.hex()
        elif hasattr(val, "read"):  # Là LOB
            try:
                content = val.read()
                if isinstance(content, bytes):
                    try:
                        return content.decode("utf-8")
                    except UnicodeDecodeError:
                        return content
                else:
                    return content
            except:
                return None
        else:
            if val is not None:
                try:
                    return str(val)
                except Exception:
                    return None
            return None

    @staticmethod
    def _valid_format_column(s):
        return bool(re.fullmatch(VALID_COLUMN, s))

    @staticmethod
    def _process_error(message_error_connect):
        mapping_error_status = copy.deepcopy(MAPPING_ERROR)
        if message_error_connect:
            for error_type, error_info in MAPPING_ERROR.items():
                if any(word.lower() in message_error_connect.lower() for word in error_info["word_check"]):
                    mapping_error_status[error_type]["status"] = False
        return mapping_error_status

    @staticmethod
    def _get_postgres_connection(db_config):
        return connect(
            dbname=db_config["database_name"],
            user=db_config["database_username"],
            host=db_config["host"],
            password=db_config["database_password"],
            port=db_config["port"],
            connect_timeout=10,
        )

    @staticmethod
    def _get_mysql_connection(db_config):
        import MySQLdb

        return MySQLdb.connect(
            database=db_config["database_name"],
            user=db_config["database_username"],
            host=db_config["host"],
            passwd=db_config["database_password"],
            port=db_config["port"],
            connect_timeout=10,
        )

    @staticmethod
    def _get_oracle_connection(db_config):
        # cx_oracle, dsn = HelperConnectConfigDatabase._int_dns_connect_oracle(db_config)
        # return cx_oracle.connect(
        #     user=db_config["database_username"],
        #     password=db_config["database_password"],
        #     dsn=dsn,
        # )
        import oracledb

        dns = db_config["host"] + ":" + str(db_config["port"]) + "/" + db_config["database_name"]
        return oracledb.connect(
            user=db_config["database_username"],
            password=db_config["database_password"],
            dsn=dns,
            mode=oracledb.DEFAULT_AUTH,
        )

    @staticmethod
    def _get_sqlserver_connection(db_config):
        import pyodbc

        driver_name = "{ODBC Driver 17 for SQL Server}"
        return pyodbc.connect(
            "Driver="
            + driver_name
            + ";Server="
            + db_config["host"]
            + ";Database="
            + db_config["database_name"]
            + ";UID="
            + db_config["database_username"]
            + ";PWD="
            + db_config["database_password"]
        )

    @staticmethod
    def sqlserver_check_connection(db_config):
        message_error_connect = None
        import pyodbc

        driver_name = "{ODBC Driver 17 for SQL Server}"
        try:
            conn = pyodbc.connect(
                "Driver="
                + driver_name
                + ";Server="
                + db_config["host"]
                + ","
                + str(db_config["port"])
                + ";Database="
                + db_config["database_name"]
                + ";UID="
                + db_config["database_username"]
                + ";PWD="
                + db_config["database_password"]
            )
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT @@VERSION
                    """
                )
                version = cursor.fetchone()[0]
                MobioLogging().debug(
                    "connect_config_database :: sqlserver_check_connection :: version :: {}".format(version)
                )
            conn.close()
        except Exception as err:
            message_error_connect = str(err)
            MobioLogging().error(
                f"HelperConnectConfigDatabase :: sqlserver_check_connection :: message_error_connect :: {message_error_connect}"
            )
        return HelperConnectConfigDatabase._process_error(message_error_connect)

    @staticmethod
    def oracle_check_connection(db_config):
        message_error_connect = None
        import oracledb

        try:

            dns = db_config["host"] + ":" + str(db_config["port"]) + "/" + db_config["database_name"]
            conn = oracledb.connect(
                user=db_config["database_username"],
                password=db_config["database_password"],
                dsn=dns,
                mode=oracledb.DEFAULT_AUTH,
            )
            conn.close()
        except oracledb.DatabaseError as e:
            message_error_connect = str(e)
        MobioLogging().info(
            f"HelperConnectConfigDatabase :: oracle_check_connection :: message_error_connect :: {message_error_connect}"
        )
        return HelperConnectConfigDatabase._process_error(message_error_connect)

    @staticmethod
    def postgres_check_connection(*args):
        message_error_connect = None
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_postgres_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_type = 'BASE TABLE'")
            conn.close()
        except OperationalError as err:
            message_error_connect = err.args[0]
        MobioLogging().info(
            f"HelperConnectConfigDatabase :: postgres_check_connection :: message_error_connect :: {message_error_connect}"
        )
        return HelperConnectConfigDatabase._process_error(message_error_connect)

    @staticmethod
    def mysql_check_connection(*args):
        db_config = args[0]
        try:
            import MySQLdb
            from MySQLdb import OperationalError
        except ImportError as e:
            MobioLogging().error("HelperConnectConfigDatabase :: mysql_check_connection :: ImportError :: {}".format(e))
            raise CustomError("MySQLdb module is not installed")

        message_error_connect = None
        try:
            conn = HelperConnectConfigDatabase._get_mysql_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute("SHOW TABLES")
            conn.close()
        except OperationalError as err:
            message_error_connect = str(err)
        MobioLogging().info(
            f"HelperConnectConfigDatabase :: mysql_check_connection :: message_error_connect :: {message_error_connect}"
        )
        return HelperConnectConfigDatabase._process_error(message_error_connect)

    @staticmethod
    def postgres_tables(*args):
        try:
            conn = HelperConnectConfigDatabase._get_postgres_connection(args[0])
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public' AND table_type='BASE TABLE'
                    ORDER BY table_name
                    """
                )
                tables = [row[0] for row in cursor.fetchall()]
            conn.close()
        except OperationalError:
            raise CustomError("Not get information tables")

        return tables

    @staticmethod
    def mysql_tables(*args):
        db_config = args[0]
        try:
            import MySQLdb
            from MySQLdb import OperationalError
        except ImportError:
            raise CustomError("MySQLdb module is not installed")

        try:
            conn = HelperConnectConfigDatabase._get_mysql_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    f"""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = '{db_config["database_name"]}' AND table_type='BASE TABLE'
                    ORDER BY table_name
                    """
                )
                tables = [row[0] for row in cursor.fetchall()]
            conn.close()
        except OperationalError:
            raise CustomError("Cannot retrieve table information")
        return tables

    @staticmethod
    def db2_tables(*args):
        db_config = args[0]
        """Lấy danh sách table trong schema"""
        tables = []
        try:
            conn = HelperConnectConfigDatabase._get_db2_connection(db_config)
            if conn:
                sql_check_schema = """SELECT TABNAME
                    FROM SYSCAT.TABLES 
                    WHERE TYPE IN ('T') 
                    AND TABSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSSTAT', 'SYSTOOLS', 'SYSIBMADM')"""

                cursor = conn.cursor()
                cursor.execute(sql_check_schema)  # Truy vấn an toàn

                # Lấy tất cả kết quả
                rows = cursor.fetchall()
                tables = [row[0] for row in rows]  # Chỉ lấy tên view

                # Đóng kết nối
                cursor.close()
                conn.close()
        except OperationalError:
            raise CustomError("Cannot retrieve tables information")
        return tables

    @staticmethod
    def oracle_tables(*args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_oracle_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT table_name
                    FROM all_tables
                    WHERE owner = :owner
                    ORDER BY table_name
                    """,
                    {"owner": db_config["database_username"].upper()},
                )
                tables = [row[0] for row in cursor.fetchall()]
            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve table information: {str(err)}")
        return tables

    @staticmethod
    def sqlserver_tables(*args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_sqlserver_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_type = 'BASE TABLE'
                    """
                )
                tables = [row[0] for row in cursor.fetchall()]
            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve table information: {str(err)}")
        return tables

    @staticmethod
    def postgres_get_data_sample(table_name, number_item, schema, language, *args):
        db_config = args[0]
        if not schema:
            schema = "public"
        try:
            conn = HelperConnectConfigDatabase._get_postgres_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        c.column_name,
                        c.ordinal_position,
                        c.data_type,
                        CASE WHEN kcu.column_name IS NOT NULL THEN TRUE ELSE FALSE END AS is_primary
                    FROM 
                        information_schema.columns c
                    LEFT JOIN 
                        information_schema.key_column_usage kcu
                        ON c.table_name = kcu.table_name
                        AND c.column_name = kcu.column_name
                        AND c.table_schema = kcu.table_schema
                    LEFT JOIN 
                        information_schema.table_constraints tc
                        ON kcu.constraint_name = tc.constraint_name
                        AND tc.constraint_type = 'PRIMARY KEY'
                        AND kcu.table_schema = tc.table_schema
                        AND kcu.table_name = tc.table_name
                    WHERE 
                        c.table_name = %s
                        AND c.table_schema = %s
                    ORDER BY 
                        c.ordinal_position
                    """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()
                column_names = [
                    {
                        "column_name": col[0],
                        "order": col[1],
                        "data_type": col[2].split(" ")[0],
                        "valid_column_format": HelperConnectConfigDatabase()._valid_format_column(col[0]),
                        "valid_datatype": (
                            True
                            if col[2].split(" ")[0].upper() in DATATYPE_SUPPORT.get("postgres").get("supported")
                            else False
                        ),
                        "primary_key": col[3],
                    }
                    for col in columns
                ]
                full_table_name = f'"{schema}"."{table_name}"'
                cursor.execute(f"SELECT * FROM {full_table_name} LIMIT %s", (number_item,))
                rows = cursor.fetchall()
                # ep kieu ve string tranh truong hop du lieu binary/object khong json dump duoc
                stringified_rows = [
                    [HelperConnectConfigDatabase().normalize_value(cell) if cell is not None else None for cell in row]
                    for row in rows
                ]
            conn.close()
        except Exception as err:
            message_error = language.get(LANG.CANNOT_RETRIEVE_DATA_SAMPLE).get("message") + str(err)
            raise CustomError(message_error)
        return column_names, stringified_rows

    @staticmethod
    def oracle_get_data_sample(table_name, number_item, language, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_oracle_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        c.column_name,
                        c.column_id,
                        c.data_type,
                        CASE 
                            WHEN pk.column_name IS NOT NULL THEN 1
                            ELSE 0
                        END AS is_primary
                    FROM 
                        all_tab_columns c
                    LEFT JOIN (
                        SELECT acc.owner, acc.table_name, acc.column_name
                        FROM all_constraints ac
                        JOIN all_cons_columns acc 
                        ON ac.owner = acc.owner 
                        AND ac.constraint_name = acc.constraint_name
                        WHERE ac.constraint_type = 'P'
                    ) pk
                        ON c.owner = pk.owner 
                        AND c.table_name = pk.table_name 
                        AND c.column_name = pk.column_name
                    WHERE 
                        c.table_name = :table_name
                        AND c.owner = :owner
                    ORDER BY 
                        c.column_id
                    """,
                    {"table_name": table_name, "owner": db_config["database_username"].upper()},
                )
                columns = cursor.fetchall()
                column_names = [
                    {
                        "column_name": col[0],
                        "order": col[1],
                        "data_type": col[2].split(" ")[0],
                        "valid_column_format": HelperConnectConfigDatabase()._valid_format_column(col[0]),
                        "valid_datatype": (
                            True
                            if col[2].split(" ")[0].upper() in DATATYPE_SUPPORT.get("oracle").get("supported")
                            and col[2].upper() not in DATATYPE_SUPPORT.get("oracle").get("not_supported")
                            else False
                        ),
                        "primary_key": True if col[3] else False,
                    }
                    for col in columns
                ]
                full_table_name = f'"{table_name}"'
                cursor.execute(
                    f"""
                            SELECT *
                            FROM {full_table_name}
                            WHERE ROWNUM <= {number_item}
                            """
                )
                rows = cursor.fetchall()
                # ep kieu ve string tranh truong hop du lieu binary/object khong json dump duoc
                stringified_rows = [
                    [HelperConnectConfigDatabase().normalize_value(cell) if cell is not None else None for cell in row]
                    for row in rows
                ]
            conn.close()
        except Exception as err:
            message_error = language.get(LANG.CANNOT_RETRIEVE_DATA_SAMPLE).get("message") + str(err)
            raise CustomError(message_error)
        return column_names, stringified_rows

    @staticmethod
    def mysql_get_data_sample(table_name, number_item, language, *args):
        db_config = args[0]
        try:
            import MySQLdb
            from MySQLdb import OperationalError, ProgrammingError
        except ImportError:
            raise CustomError("MySQLdb module is not installed")

        try:
            conn = HelperConnectConfigDatabase._get_mysql_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        c.COLUMN_NAME,
                        c.ORDINAL_POSITION,
                        c.DATA_TYPE,
                        CASE 
                            WHEN tc.CONSTRAINT_TYPE = 'PRIMARY KEY' THEN TRUE 
                            ELSE FALSE 
                        END AS is_primary
                    FROM 
                        INFORMATION_SCHEMA.COLUMNS c
                    LEFT JOIN 
                        INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu 
                        ON c.TABLE_SCHEMA = kcu.TABLE_SCHEMA 
                        AND c.TABLE_NAME = kcu.TABLE_NAME 
                        AND c.COLUMN_NAME = kcu.COLUMN_NAME
                    LEFT JOIN 
                        INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc 
                        ON kcu.CONSTRAINT_NAME = tc.CONSTRAINT_NAME 
                        AND kcu.TABLE_SCHEMA = tc.TABLE_SCHEMA 
                        AND kcu.TABLE_NAME = tc.TABLE_NAME 
                        AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
                    WHERE 
                        c.TABLE_NAME = %s 
                        AND c.TABLE_SCHEMA = %s
                    ORDER BY 
                        c.ORDINAL_POSITION;
                    """,
                    (table_name, db_config["database_name"]),
                )
                columns = cursor.fetchall()
                column_names = [
                    {
                        "column_name": col[0],
                        "order": col[1],
                        "data_type": col[2].split(" ")[0],
                        "valid_column_format": HelperConnectConfigDatabase()._valid_format_column(col[0]),
                        "valid_datatype": (
                            True
                            if any(
                                item in col[2].split(" ")[0].upper()
                                for item in DATATYPE_SUPPORT.get("mysql").get("supported")
                            )
                            else False
                        ),
                        "primary_key": True if col[3] else False,
                    }
                    for col in columns
                ]
                cursor.execute(f"SELECT * FROM {table_name} LIMIT %s", (number_item,))
                rows = cursor.fetchall()
                # ep kieu ve string tranh truong hop du lieu binar/object khong json dump duoc
                stringified_rows = [
                    [HelperConnectConfigDatabase().normalize_value(cell) if cell is not None else None for cell in row]
                    for row in rows
                ]
            conn.close()
        except (OperationalError, ProgrammingError) as err:
            message_error = language.get(LANG.CANNOT_RETRIEVE_DATA_SAMPLE).get("message") + str(err)
            raise CustomError(message_error)
        return column_names, stringified_rows

    @staticmethod
    def db2_get_data_sample(table_name, number_item, schema, language, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_db2_connection(db_config)
            if conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT 
                        col.COLNAME AS column_name,
                        col.COLNO AS ordinal_position,
                        col.TYPENAME AS data_type,
                        CASE 
                            WHEN pk.COLNAME IS NOT NULL THEN 1 
                            ELSE 0 
                        END AS is_primary
                    FROM 
                        SYSCAT.COLUMNS col
                    LEFT JOIN 
                        (
                            SELECT kcu.TABSCHEMA, kcu.TABNAME, kcu.COLNAME
                            FROM SYSCAT.KEYCOLUSE kcu
                            JOIN SYSCAT.TABCONST tc 
                            ON kcu.CONSTNAME = tc.CONSTNAME 
                            AND kcu.TABSCHEMA = tc.TABSCHEMA
                            WHERE tc.TYPE = 'P'
                        ) pk
                        ON col.TABSCHEMA = pk.TABSCHEMA 
                        AND col.TABNAME = pk.TABNAME 
                        AND col.COLNAME = pk.COLNAME
                    WHERE 
                        col.TABNAME = ?
                        AND col.TABSCHEMA = ?
                    ORDER BY 
                        col.COLNO;
                        """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()
                column_names = [
                    {
                        "column_name": col[0],
                        "order": col[1] + 1,
                        "data_type": col[2].split(" ")[0],
                        "valid_column_format": HelperConnectConfigDatabase()._valid_format_column(col[0]),
                        "valid_datatype": (
                            True
                            if col[2].split(" ")[0].upper() in DATATYPE_SUPPORT.get("db2").get("supported")
                            else False
                        ),
                        "primary_key": True if col[3] else False,
                    }
                    for col in columns
                ]
                full_table_name = f'"{schema}"."{table_name}"'
                cursor.execute(
                    f"""SELECT * FROM {full_table_name} FETCH FIRST ? ROWS ONLY""",
                    (number_item,),
                )
                rows = cursor.fetchall()
                # ep kieu ve string tranh truong hop du lieu binar/object khong json dump duoc
                stringified_rows = [
                    [HelperConnectConfigDatabase().normalize_value(cell) if cell is not None else None for cell in row]
                    for row in rows
                ]
            conn.close()
        except Exception as err:
            message_error = language.get(LANG.CANNOT_RETRIEVE_DATA_SAMPLE).get("message") + str(err)
            raise CustomError(message_error)
        return column_names, stringified_rows

    @staticmethod
    def sqlserver_get_data_sample(table_name, number_item, schema, language, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_sqlserver_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        c.name AS column_name,
                        c.column_id AS ordinal_position,
                        t.name AS data_type,
                        CASE 
                            WHEN ic.column_id IS NOT NULL THEN 1 
                            ELSE 0 
                        END AS is_primary
                    FROM 
                        sys.columns c
                    JOIN 
                        sys.types t ON c.user_type_id = t.user_type_id
                    JOIN 
                        sys.objects o ON c.object_id = o.object_id
                    JOIN 
                        sys.schemas s ON o.schema_id = s.schema_id
                    LEFT JOIN 
                        (
                            SELECT 
                                ic.object_id, 
                                ic.column_id
                            FROM 
                                sys.indexes i
                            JOIN 
                                sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                            WHERE 
                                i.is_primary_key = 1
                        ) ic ON c.object_id = ic.object_id AND c.column_id = ic.column_id
                    WHERE 
                        o.name = ?
                        AND s.name = ?
                        AND o.type IN ('U', 'V') -- U = Table, V = View
                    ORDER BY 
                        c.column_id
                    """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()
                column_names = [
                    {
                        "column_name": col[0],
                        "order": col[1],
                        "data_type": col[2],
                        "valid_column_format": HelperConnectConfigDatabase()._valid_format_column(col[0]),
                        "valid_datatype": (
                            True if col[2].upper() in DATATYPE_SUPPORT.get("sqlserver").get("supported") else False
                        ),
                        "primary_key": True if col[3] else False,
                    }
                    for col in columns
                ]

                # Lấy danh sách các cột và loại dữ liệu tương ứng
                select_parts = []
                # Các kiểu dữ liệu đặc biệt cần xử lý riêng

                lst_data_type_support = DATATYPE_SUPPORT.get("sqlserver").get("supported")

                for col in columns:
                    col_name = col[0]  # Cột đầu tiên là tên cột (column_name)
                    data_type = col[2].lower()  # Cột thứ 3 là kiểu dữ liệu (data_type)

                    if data_type in ["image", "uniqueidentifier", "varbinary", "xml"]:
                        select_parts.append("CAST(NULL AS VARCHAR) AS [{col_name}]")
                    elif (
                        data_type.lower() in ["text", "timestamp", "time", "varchar"]
                        or data_type.upper() not in lst_data_type_support
                    ):
                        select_parts.append(f"CAST([{col_name}] AS VARCHAR) AS [{col_name}]")
                    elif data_type.lower() == "tinyint":
                        select_parts.append(f"CAST([{col_name}] AS INT) AS [{col_name}]")
                    else:
                        select_parts.append(f"[{col_name}] AS [{col_name}]")

                # Tạo câu truy vấn với chuyển đổi kiểu dữ liệu
                select_statement = ", ".join(select_parts)
                full_table_name = f"[{schema}].[{table_name}]"
                query = f"SELECT TOP {number_item} {select_statement} FROM {full_table_name}"
                cursor.execute(query)
                rows = cursor.fetchall()
                # ep kieu ve string tranh truong hop du lieu binar/object khong json dump duoc
                stringified_rows = [
                    [HelperConnectConfigDatabase().normalize_value(cell) for cell in row] for row in rows
                ]
            conn.close()
        except Exception as err:
            message_error = language.get(LANG.CANNOT_RETRIEVE_DATA_SAMPLE).get("message") + str(err)
            raise CustomError(message_error)
        return column_names, stringified_rows

    @staticmethod
    def postgres_get_schema(table_name, schema, *args):
        db_config = args[0]
        if not schema:
            schema = "public"
        try:
            conn = HelperConnectConfigDatabase._get_postgres_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT column_name, 
                        ordinal_position, 
                        is_nullable, 
                        data_type, 
                        character_maximum_length
                    FROM information_schema.columns
                    WHERE table_name = %s
                    AND table_schema = %s
                    ORDER BY ordinal_position;
                    """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()
                result_schema = [
                    {
                        "column_name": row[0],
                        "ordinal_position": row[1],
                        "is_nullable": row[2],
                        "data_type": row[3].split(" ")[0],
                        "character_maximum_length": row[4],
                        "type": row[3],
                    }
                    for row in columns
                ]
                # chi lay field dung format
                valid_field_schema = [
                    field
                    for field in result_schema
                    if HelperConnectConfigDatabase()._valid_format_column(field.get("column_name"))
                ]
                valid_datatype_schema = [
                    field
                    for field in valid_field_schema
                    if field["data_type"].upper() in DATATYPE_SUPPORT.get("postgres").get("supported")
                ]
            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return valid_datatype_schema

    @staticmethod
    def sqlserver_get_schema(table_name, schema, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_sqlserver_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        c.name AS column_name, 
                        c.column_id AS ordinal_position,
                        CASE WHEN c.is_nullable = 1 THEN 'YES' ELSE 'NO' END AS is_nullable,
                        t.name AS data_type,
                        c.max_length
                    FROM 
                        sys.columns c
                    JOIN 
                        sys.types t ON c.user_type_id = t.user_type_id
                    JOIN 
                        sys.objects o ON c.object_id = o.object_id
                    JOIN 
                        sys.schemas s ON o.schema_id = s.schema_id
                    WHERE 
                        o.name = ?
                        AND s.name = ?
                        AND o.type IN ('U', 'V') -- U for tables, V for views
                    ORDER BY 
                        c.column_id
                    """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()
                result_schema = [
                    {
                        "column_name": row[0],
                        "ordinal_position": row[1],
                        "is_nullable": row[2],
                        "data_type": row[3],
                        "character_maximum_length": row[4],
                        "type": row[3],
                    }
                    for row in columns
                ]
                # chi lay field dung format
                valid_field_schema = [
                    field
                    for field in result_schema
                    if HelperConnectConfigDatabase()._valid_format_column(field.get("column_name"))
                ]
                valid_datatype_schema = [
                    field
                    for field in valid_field_schema
                    if field["data_type"].upper() in DATATYPE_SUPPORT.get("sqlserver").get("supported")
                ]
            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return valid_datatype_schema

    @staticmethod
    def sqlserver_get_information_column_table(table_name, schema, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_sqlserver_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        c.name AS column_name,
                        c.column_id AS ordinal_position,
                        t.name AS data_type,
                        c.max_length AS max_length,
                        c.precision AS precision,
                        c.scale AS scale,
                        CASE WHEN c.is_nullable = 1 THEN 'YES' ELSE 'NO' END AS is_nullable,
                        CASE 
                            WHEN ic.column_id IS NOT NULL THEN 1 
                            ELSE 0 
                        END AS is_primary
                    FROM 
                        sys.columns c
                    JOIN 
                        sys.types t ON c.user_type_id = t.user_type_id
                    JOIN 
                        sys.objects tbl ON c.object_id = tbl.object_id
                    JOIN 
                        sys.schemas s ON tbl.schema_id = s.schema_id
                    LEFT JOIN 
                        (
                            SELECT 
                                ic.object_id, 
                                ic.column_id
                            FROM 
                                sys.indexes i
                            JOIN 
                                sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                            WHERE 
                                i.is_primary_key = 1
                        ) ic ON c.object_id = ic.object_id AND c.column_id = ic.column_id
                    WHERE 
                        tbl.name = ?
                        AND s.name = ?
                        AND tbl.type IN ('U', 'V') -- U for tables, V for views
                    ORDER BY 
                        c.column_id
                    """,
                    (table_name, schema),
                )
                result = cursor.fetchall()

                if result:
                    primary_key = []
                    for row in result:
                        if row[7] == 1:  # is_primary
                            primary_key.append(row[0])  # column_name

                    columns = [
                        {
                            "column_name": row[0],
                            "ordinal_position": row[1],
                            "data_type": HelperConnectConfigDatabase._format_data_type(row[2], row[3], row[4], row[5]),
                            "is_nullable": row[6],
                            "is_primary_key": row[7] == 1,
                            "primary_key": primary_key,
                        }
                        for row in result
                    ]
                    return columns
                return []
        except Exception as err:
            raise CustomError(f"Cannot retrieve table information: {str(err)}")

    @staticmethod
    def postgres_get_information_column_table(table_name, schema, *args):
        db_config = args[0]
        if not schema:
            schema = "public"
        try:
            conn = HelperConnectConfigDatabase._get_postgres_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                        SELECT 
                            col.column_name,
                            CASE
                                WHEN col.data_type = 'numeric' THEN 
                                    CONCAT(col.data_type, '(', col.numeric_precision, ',', col.numeric_scale, ')')
                                WHEN col.data_type = 'character varying' THEN 
                                    CONCAT('varchar', '(', col.character_maximum_length, ')')
                                WHEN col.data_type = 'bigint' THEN 'int8'
                                WHEN col.data_type = 'integer' THEN 'int4'
                                WHEN col.data_type = 'real' THEN 'float4'
                                WHEN col.data_type = 'double precision' THEN 'float8'
                                ELSE col.data_type
                            END AS data_type,
                            BOOL_OR(tc.constraint_type = 'PRIMARY KEY') AS is_primary_key
                        FROM information_schema.columns col
                        LEFT JOIN information_schema.key_column_usage kcu
                            ON col.table_name = kcu.table_name
                            AND col.table_schema = kcu.table_schema
                            AND col.column_name = kcu.column_name
                        LEFT JOIN information_schema.table_constraints tc
                            ON tc.table_name = kcu.table_name
                            AND tc.table_schema = kcu.table_schema
                            AND tc.constraint_name = kcu.constraint_name
                        AND tc.constraint_type = 'PRIMARY KEY'
                        WHERE col.table_name = %s
                        AND col.table_schema = %s
                        GROUP BY col.column_name, col.data_type, col.numeric_precision, col.numeric_scale, col.character_maximum_length, col.ordinal_position
                        ORDER BY col.ordinal_position;
                    """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()
                result_columns = [
                    {"column_name": col[0], "data_type": col[1], "is_primary_key": col[2] if col[2] else False} for col in columns
                ]

            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return result_columns

    @staticmethod
    def mysql_get_information_column_table(table_name, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_mysql_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                        SELECT 
                            col.COLUMN_NAME AS column_name,
                            col.COLUMN_TYPE AS data_type,
                            CASE 
                                WHEN tc.CONSTRAINT_TYPE = 'PRIMARY KEY' THEN TRUE
                                ELSE FALSE
                            END AS is_primary_key
                        FROM 
                            information_schema.columns col
                        LEFT JOIN 
                            information_schema.key_column_usage kcu
                            ON col.TABLE_NAME = kcu.TABLE_NAME
                            AND col.TABLE_SCHEMA = kcu.TABLE_SCHEMA
                            AND col.COLUMN_NAME = kcu.COLUMN_NAME
                        LEFT JOIN 
                            information_schema.table_constraints tc
                            ON tc.TABLE_NAME = kcu.TABLE_NAME
                            AND tc.TABLE_SCHEMA = kcu.TABLE_SCHEMA
                            AND tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
                            AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
                        WHERE 
                            col.TABLE_NAME = %s
                            AND col.TABLE_SCHEMA = %s
                        ORDER BY 
                            col.ORDINAL_POSITION;
                    """,
                    (table_name, db_config.get("database_name")),
                )
                columns = cursor.fetchall()
                result_columns = [
                    {"column_name": col[0], "data_type": col[1], "is_primary_key": col[2]} for col in columns
                ]

            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return result_columns

    @staticmethod
    def oracle_get_information_column_table(table_name, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_oracle_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                        SELECT 
                            cols.COLUMN_NAME AS column_name,
                            cols.DATA_TYPE AS data_type,
                            cols.DATA_LENGTH AS character_maximum_length,
                            CASE 
                                WHEN cols.DATA_TYPE = 'VARCHAR2' OR COLS.DATA_TYPE = 'CHAR' THEN 
                                    cols.DATA_TYPE || '(' || cols.DATA_LENGTH || ')'
                                WHEN cols.DATA_TYPE = 'NUMBER' AND cols.DATA_PRECISION IS NOT NULL AND cols.DATA_SCALE IS NOT NULL THEN 
                                    cols.DATA_TYPE || '(' || cols.DATA_PRECISION || ',' || cols.DATA_SCALE || ')'
                                WHEN cols.DATA_TYPE = 'NUMBER' AND cols.DATA_PRECISION IS NOT NULL THEN 
                                    cols.DATA_TYPE || '(' || cols.DATA_PRECISION || ')'
                                ELSE 
                                    cols.DATA_TYPE
                            END,
                            CASE 
                                WHEN cons.CONSTRAINT_TYPE = 'P' THEN 1
                                ELSE 0
                            END AS is_primary_key
                        FROM 
                            all_tab_columns cols
                        LEFT JOIN 
                            (SELECT 
                                acc.TABLE_NAME,
                                acc.COLUMN_NAME,
                                acc.OWNER,
                                ac.CONSTRAINT_TYPE
                            FROM 
                                all_cons_columns acc
                            JOIN 
                                all_constraints ac
                            ON acc.CONSTRAINT_NAME = ac.CONSTRAINT_NAME
                            AND acc.OWNER = ac.OWNER
                            WHERE ac.CONSTRAINT_TYPE = 'P') cons
                        ON 
                            cols.TABLE_NAME = cons.TABLE_NAME
                            AND cols.COLUMN_NAME = cons.COLUMN_NAME
                            AND cols.OWNER = cons.OWNER
                        WHERE 
                            cols.TABLE_NAME = :table_name
                            AND cols.OWNER = :owner
                        ORDER BY 
                            cols.COLUMN_ID
                    """,
                    {
                        "table_name": table_name,
                        "owner": db_config["database_username"].upper(),
                    },
                )
                columns = cursor.fetchall()
                result_columns = [
                    {"column_name": col[0], "data_type": col[3], "is_primary_key": col[4] == 1} for col in columns
                ]

            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return result_columns

    @staticmethod
    def db2_get_information_column_table(table_name, schema, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_db2_connection(db_config)
            if conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                        SELECT 
                            cols.COLNAME AS column_name,
                            cols.TYPENAME AS data_type,
                            CASE 
                                WHEN cols.TYPENAME IN ('VARCHAR', 'CHAR', 'GRAPHIC', 'VARGRAPHIC') THEN 
                                    cols.TYPENAME || '(' || cols.LENGTH || ')'
                                WHEN cols.TYPENAME = 'DECIMAL' THEN 
                                    cols.TYPENAME || '(' || cols.LENGTH || ',' || cols.SCALE || ')'
                                WHEN cols.TYPENAME = 'CHARACTER' THEN 
                                    'CHAR(' || cols.LENGTH || ')'
                                ELSE 
                                    cols.TYPENAME
                            END AS formatted_data_type,
                            CASE 
                                WHEN EXISTS (
                                    SELECT 1 
                                    FROM SYSCAT.KEYCOLUSE k
                                    JOIN SYSCAT.TABCONST t ON k.CONSTNAME = t.CONSTNAME
                                    WHERE t.TYPE = 'P'
                                    AND k.TABNAME = cols.TABNAME
                                    AND k.COLNAME = cols.COLNAME
                                ) THEN 1
                                ELSE 0
                            END AS is_primary_key
                        FROM SYSCAT.COLUMNS cols
                        WHERE 
                            cols.TABNAME = ?
                            AND cols.TABSCHEMA = ?
                        ORDER BY 
                            cols.COLNO;
                    """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()
                result_columns = [
                    {"column_name": col[0], "data_type": col[2], "is_primary_key": col[3] == 1} for col in columns
                ]
            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return result_columns

    @staticmethod
    def oracle_get_schema(table_name, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_oracle_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT  column_name,
                            column_id AS ordinal_position,
                            nullable AS is_nullable,
                            data_type,
                            data_length AS character_maximum_length,
                            CASE 
                                WHEN data_type = 'VARCHAR2' OR data_type = 'CHAR' THEN 
                                    data_type || '(' || data_length || ')'
                                WHEN data_type = 'NUMBER' AND data_precision IS NOT NULL AND data_scale IS NOT NULL THEN 
                                    data_type || '(' || data_precision || ',' || data_scale || ')'
                                WHEN data_type = 'NUMBER' AND data_precision IS NOT NULL THEN 
                                    data_type || '(' || data_precision || ')'
                                ELSE 
                                    data_type
                            END
                    FROM all_tab_columns
                    WHERE table_name = :table_name AND owner = :owner
                    ORDER BY column_id
                    """,
                    {
                        "table_name": table_name,
                        "owner": db_config["database_username"].upper(),
                    },
                )
                columns = cursor.fetchall()
                result_schema = [
                    {
                        "column_name": row[0],
                        "ordinal_position": row[1],
                        "is_nullable": "YES" if row[2] == "Y" else "NO",
                        "data_type": row[3].split(" ")[0],
                        "character_maximum_length": row[4],
                        "type": row[5],
                    }
                    for row in columns
                ]
                # chi lay field dung format
                valid_field_schema = [
                    field
                    for field in result_schema
                    if HelperConnectConfigDatabase()._valid_format_column(field.get("column_name"))
                ]
                valid_datatype_schema = [
                    field
                    for field in valid_field_schema
                    if field["data_type"] in DATATYPE_SUPPORT.get("oracle").get("supported")
                    and field["type"] not in DATATYPE_SUPPORT.get("oracle").get("not_supported")
                ]
            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return valid_datatype_schema

    @staticmethod
    def mysql_get_schema(table_name, *args):
        db_config = args[0]
        try:
            import MySQLdb
            from MySQLdb import OperationalError
        except ImportError:
            raise CustomError("MySQLdb module is not installed")

        try:
            conn = HelperConnectConfigDatabase._get_mysql_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT column_name, ordinal_position, is_nullable, data_type, character_maximum_length
                    FROM information_schema.columns
                    WHERE table_name = %s AND table_schema = %s
                    ORDER BY ordinal_position
                    """,
                    (table_name, db_config["database_name"]),
                )
                columns = cursor.fetchall()
                result_schema = [
                    {
                        "column_name": row[0],
                        "ordinal_position": row[1],
                        "is_nullable": row[2],
                        "data_type": row[3].split(" ")[0],
                        "character_maximum_length": row[4],
                        "type": row[3],
                    }
                    for row in columns
                ]
                # chi lay field dung format
                valid_field_schema = [
                    field
                    for field in result_schema
                    if HelperConnectConfigDatabase()._valid_format_column(field.get("column_name"))
                ]
                valid_datatype_schema = [
                    field
                    for field in valid_field_schema
                    if any(
                        item in field["data_type"].upper() for item in DATATYPE_SUPPORT.get("mysql").get("supported")
                    )
                ]
            conn.close()
        except OperationalError as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return valid_datatype_schema

    @staticmethod
    def db2_get_schema(table_name, schema, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_db2_connection(db_config)
            if conn:
                cursor = conn.cursor()
                # cursor.execute(
                #     """
                #     SELECT TABSCHEMA, TABNAME 
                #     FROM SYSCAT.TABLES 
                #     WHERE TABNAME = ?;
                #     """,
                #     (table_name,),
                # )
                # schema = cursor.fetchone()[0]
                cursor.execute(
                    """
                        SELECT COLNAME AS column_name,
                           COLNO AS ordinal_position,  
                           NULLS AS is_nullable,
                           TYPENAME AS data_type,
                           LENGTH AS character_maximum_length
                            FROM SYSCAT.COLUMNS
                            WHERE TABNAME = ?  
                            AND TABSCHEMA = ?
                            ORDER BY COLNO;
                        """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()
                result_schema = [
                    {
                        "column_name": row[0],
                        "ordinal_position": row[1] + 1,
                        "is_nullable": "YES" if row[2] == "Y" else "NO",
                        "data_type": row[3].split(" ")[0],
                        "character_maximum_length": row[4],
                        "type": row[3],
                    }
                    for row in columns
                ]
                # chi lay field dung format va datatype duoc support
                valid_field_schema = [
                    field
                    for field in result_schema
                    if HelperConnectConfigDatabase()._valid_format_column(field.get("column_name"))
                ]
                valid_datatype_schema = [
                    field
                    for field in valid_field_schema
                    if field["data_type"] in DATATYPE_SUPPORT.get("db2").get("supported")
                ]
            conn.close()
        except OperationalError as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return valid_datatype_schema

    @staticmethod
    def _get_db2_connection(db_config):
        # Thông tin kết nối
        database = db_config["database_name"]
        user = db_config["database_username"]
        host = db_config["host"]
        passwd = db_config["database_password"]
        port = db_config["port"]
        dsn = f"DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={user};PWD={passwd};"
        conn = ibm_db_dbi.connect(dsn, "", "")
        return conn

    @staticmethod
    def db2_check_connection(db_config):
        """Kiểm tra kết nối Db2 và schema"""
        message_error_connect = None
        try:
            conn = HelperConnectConfigDatabase._get_db2_connection(db_config)
            if conn:
                conn.close()  # Đóng kết nối đúng cách
        except Exception as e:
            message_error_connect = str(e)
        return HelperConnectConfigDatabase._process_error(message_error_connect)

    @staticmethod
    def db2_views(db_config):
        """Lấy danh sách views trong schema"""
        views = []
        try:
            conn = HelperConnectConfigDatabase._get_db2_connection(db_config)
            if conn:
                schema = db_config["schema"]
                sql_check_schema = "SELECT VIEWNAME FROM SYSCAT.VIEWS WHERE VIEWSCHEMA = ?"

                cursor = conn.cursor()
                cursor.execute(sql_check_schema, (schema,))  # Truy vấn an toàn

                # Lấy tất cả kết quả
                rows = cursor.fetchall()
                views = [row[0] for row in rows]  # Chỉ lấy tên view

                # Đóng kết nối
                cursor.close()
                conn.close()
        except OperationalError:
            raise CustomError("Cannot retrieve view information")
        return views

    @staticmethod
    def postgres_tables_views(*args):
        search = args[1].strip()
        try:
            conn = HelperConnectConfigDatabase._get_postgres_connection(args[0])
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT table_name, table_schema, table_type
                    FROM information_schema.tables
                    WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
                    AND table_schema NOT LIKE 'pg_toast%'
                    AND table_schema NOT LIKE 'pg_temp%'
                    AND table_type IN ('BASE TABLE', 'VIEW')
                    ORDER BY table_schema, table_name;
                    """
                )
                tables = []
                for row in cursor.fetchall():
                    query_primary_key = """
                        SELECT
                            kcu.column_name
                        FROM 
                            information_schema.table_constraints tc
                        JOIN 
                            information_schema.key_column_usage kcu
                            ON tc.constraint_name = kcu.constraint_name
                            AND tc.table_schema = kcu.table_schema
                        WHERE 
                            tc.constraint_type = 'PRIMARY KEY'
                            AND tc.table_name = %s
                            AND tc.table_schema = %s; 
                    """
                    cursor.execute(query_primary_key, (row[0], row[1]))
                    primary_keys = [pk[0] for pk in cursor.fetchall()]
                    table = {
                        "name": row[0],
                        "schema": row[1],
                        "type": "table" if row[2] == "BASE TABLE" else "view",
                        "primary_keys": primary_keys
                    }
                    tables.append(table)
                if search:
                    tables = [table for table in tables if search.lower() in table["name"].lower()]
            conn.close()
        except OperationalError:
            raise CustomError("Not get information tables")

        return tables

    @staticmethod
    def mysql_tables_views(*args):
        search = args[1].strip()
        db_config = args[0]
        try:
            import MySQLdb
            from MySQLdb import OperationalError
        except ImportError:
            raise CustomError("MySQLdb module is not installed")

        try:
            conn = HelperConnectConfigDatabase._get_mysql_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    f"""
                    SELECT TABLE_NAME, TABLE_SCHEMA, TABLE_TYPE
                    FROM information_schema.tables
                    WHERE TABLE_SCHEMA = '{db_config["database_name"]}' AND table_type IN ('BASE TABLE', 'VIEW')
                    ORDER BY TABLE_NAME
                    """
                )
                tables = []
                for row in cursor.fetchall():
                    # Truy vấn lấy primary key của bảng hiện tại
                    cursor.execute(
                        """
                        SELECT COLUMN_NAME
                        FROM information_schema.KEY_COLUMN_USAGE
                        WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND CONSTRAINT_NAME = 'PRIMARY'
                        ORDER BY ORDINAL_POSITION
                        """,
                        (row[1], row[0])
                    )
                    primary_keys = [pk[0] for pk in cursor.fetchall()]

                    table = {
                        "name": row[0],
                        "schema": row[1],
                        "type": "table" if row[2] == "BASE TABLE" else "view",
                        "primary_keys": primary_keys
                    }
                    tables.append(table)
                if search:
                    tables = [table for table in tables if search.lower() in table["name"].lower()]
            conn.close()
        except OperationalError:
            raise CustomError("Cannot retrieve table information")
        return tables

    @staticmethod
    def db2_tables_views(*args):
        search = args[1].strip()
        db_config = args[0]
        """Lấy danh sách table và view trong database"""
        tables = []
        try:
            conn = HelperConnectConfigDatabase._get_db2_connection(db_config)
            if conn:
                sql_check_tables = """
                    SELECT TABNAME, TABSCHEMA, TYPE 
                    FROM SYSCAT.TABLES 
                    WHERE TYPE IN ('T', 'V') 
                    AND TABSCHEMA NOT IN ('SYSIBM', 'SYSCAT', 'SYSSTAT', 'SYSTOOLS', 'SYSIBMADM')
                """

                cursor = conn.cursor()
                cursor.execute(sql_check_tables)  # Truy vấn lấy cả bảng và view

                # Lấy tất cả kết quả
                rows = cursor.fetchall()
                tables = []
                for row in rows:
                    table_name, table_schema, table_type = row

                    # Truy vấn lấy primary key
                    sql_primary_keys = """
                        SELECT COLNAME
                        FROM SYSCAT.KEYCOLUSE
                        WHERE TABSCHEMA = ? AND TABNAME = ?
                        ORDER BY COLSEQ
                    """
                    cursor.execute(sql_primary_keys, (table_schema, table_name))
                    primary_keys = [r[0] for r in cursor.fetchall()]

                    table = {
                        "name": table_name,
                        "schema": table_schema,
                        "type": "table" if table_type == "T" else "view",
                        "primary_keys": primary_keys
                    }
                    tables.append(table)
                if search:
                    tables = [table for table in tables if search.lower() in table["name"].lower()]
                # Đóng kết nối
                cursor.close()
                conn.close()
        except OperationalError:
            raise CustomError("Cannot retrieve tables and views information")
        return tables

    @staticmethod
    def oracle_tables_views(*args):
        search = args[1].strip()
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_oracle_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT  OBJECT_NAME, OWNER, OBJECT_TYPE 
                    FROM ALL_OBJECTS 
                    WHERE OBJECT_TYPE IN ('TABLE', 'VIEW') 
                    AND OWNER = :owner
                    ORDER BY OBJECT_NAME
                    """,
                    {"owner": db_config["database_username"].upper()},
                )
                tables = []
                for row in cursor.fetchall():
                    table_name, owner, object_type = row

                    # Truy vấn lấy danh sách primary key
                    cursor.execute(
                        """
                        SELECT cols.COLUMN_NAME
                        FROM ALL_CONSTRAINTS cons
                        JOIN ALL_CONS_COLUMNS cols ON cons.CONSTRAINT_NAME = cols.CONSTRAINT_NAME AND cons.OWNER = cols.OWNER
                        WHERE cons.CONSTRAINT_TYPE = 'P'
                        AND cons.OWNER = :owner
                        AND cons.TABLE_NAME = :table_name
                        ORDER BY cols.POSITION
                        """,
                        {"owner": owner, "table_name": table_name},
                    )
                    primary_keys = [pk[0] for pk in cursor.fetchall()]

                    tables.append({
                        "name": table_name,
                        "schema": owner,
                        "type": "table" if object_type == "TABLE" else "view",
                        "primary_keys": primary_keys
                    })
                if search:
                    tables = [table for table in tables if search.lower() in table["name"].lower()]
            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve table information: {str(err)}")
        return tables
    
    staticmethod
    def normalize_data_type(data_type, type_mapping):
        data_type = data_type.upper()        
        for normalized_type, pg_types in type_mapping.items():
            if data_type in pg_types:
                return normalized_type
        
        return None 
    
    @staticmethod
    def postgres_get_incremental_columns(table_name, schema, *args):
        db_config = args[0]
        if not schema:
            schema = "public"
        try:
            conn = HelperConnectConfigDatabase._get_postgres_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        c.column_name, 
                        c.ordinal_position, 
                        c.is_nullable, 
                        c.data_type, 
                        c.character_maximum_length,
                        CASE WHEN kcu.column_name IS NOT NULL THEN TRUE ELSE FALSE END AS is_primary_key
                    FROM information_schema.columns c
                    LEFT JOIN information_schema.key_column_usage kcu
                        ON c.table_name = kcu.table_name
                        AND c.column_name = kcu.column_name
                        AND c.table_schema = kcu.table_schema
                    LEFT JOIN information_schema.table_constraints tc
                        ON kcu.constraint_name = tc.constraint_name
                        AND tc.constraint_type = 'PRIMARY KEY'
                        AND kcu.table_schema = tc.table_schema
                        AND kcu.table_name = tc.table_name
                    WHERE c.table_name = %s
                        AND c.table_schema = %s
                    ORDER BY c.ordinal_position;
                    """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()
                result_schema = [
                    {
                        "column_name": row[0],
                        "data_type": row[3].split(" ")[0],
                        "type": row[3],
                        "primary_key": row[5],
                        "normalized_type": HelperConnectConfigDatabase.normalize_data_type(row[3].split(" ")[0], DATATYPE_SUPPORT.get("postgres").get("type_mapping"))
                    }
                    for row in columns
                ]
                # chi lay field dung format
                valid_field_schema = [
                    field
                    for field in result_schema
                    if HelperConnectConfigDatabase()._valid_format_column(field.get("column_name"))
                ]
                valid_datatype_schema = [
                    field for field in valid_field_schema if field["data_type"].upper() in DATATYPE_SUPPORT.get("postgres").get("supported")
                ]
                
                incremental_column_supported = [
                    field for field in valid_datatype_schema if field["normalized_type"] in INCREMENTAL_COLUMN_TYPE_SUPPORTED
                ]
                
            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return incremental_column_supported
    
    @staticmethod
    def mysql_get_incremental_columns(table_name, *args):
        db_config = args[0]
        try:
            import MySQLdb
            from MySQLdb import OperationalError
        except ImportError:
            raise CustomError("MySQLdb module is not installed")

        try:
            conn = HelperConnectConfigDatabase._get_mysql_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        cols.column_name, 
                        cols.ordinal_position, 
                        cols.is_nullable, 
                        cols.data_type, 
                        cols.character_maximum_length,
                        CASE WHEN kcu.constraint_name = 'PRIMARY' THEN TRUE ELSE FALSE END AS primary_key
                    FROM information_schema.columns AS cols
                    LEFT JOIN information_schema.key_column_usage AS kcu
                        ON cols.table_schema = kcu.table_schema
                        AND cols.table_name = kcu.table_name
                        AND cols.column_name = kcu.column_name
                        AND kcu.constraint_name = 'PRIMARY'
                    WHERE cols.table_name = %s 
                        AND cols.table_schema = %s
                    ORDER BY cols.ordinal_position;
                    """,
                    (table_name, db_config["database_name"]),
                )

                columns = cursor.fetchall()
                result_schema = [
                    {
                        "column_name": row[0],
                        "data_type": row[3].split(" ")[0],
                        "type": row[3],
                        "primary_key": row[5] or False,
                        "normalized_type": HelperConnectConfigDatabase.normalize_data_type(row[3].split(" ")[0], DATATYPE_SUPPORT.get("mysql").get("type_mapping"))

                    }
                    for row in columns
                ]
                # chi lay field dung format
                valid_field_schema = [
                    field
                    for field in result_schema
                    if HelperConnectConfigDatabase()._valid_format_column(field.get("column_name"))
                ]
                valid_datatype_schema = [
                    field
                    for field in valid_field_schema
                    if any(item in field["data_type"].upper() for item in DATATYPE_SUPPORT.get("mysql").get("supported"))
                ]
                incremental_column_supported = [
                    field for field in valid_datatype_schema if field["normalized_type"] in INCREMENTAL_COLUMN_TYPE_SUPPORTED
                ]

            conn.close()
        except OperationalError as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return incremental_column_supported
    
    
    @staticmethod
    def oracle_get_incremental_columns(table_name, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_oracle_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        col.column_name,
                        col.column_id AS ordinal_position,
                        col.nullable AS is_nullable,
                        col.data_type,
                        col.data_length AS character_maximum_length,
                        CASE 
                            WHEN col.data_type IN ('VARCHAR2', 'CHAR') THEN 
                                col.data_type || '(' || col.data_length || ')'
                            WHEN col.data_type = 'NUMBER' AND col.data_precision IS NOT NULL AND col.data_scale IS NOT NULL THEN 
                                col.data_type || '(' || col.data_precision || ',' || col.data_scale || ')'
                            WHEN col.data_type = 'NUMBER' AND col.data_precision IS NOT NULL THEN 
                                col.data_type || '(' || col.data_precision || ')'
                            ELSE 
                                col.data_type
                        END AS full_type,
                        CASE 
                            WHEN pk.column_name IS NOT NULL THEN 'YES' 
                            ELSE 'NO' 
                        END AS primary_key
                    FROM all_tab_columns col
                    LEFT JOIN (
                        SELECT acc.table_name, acc.column_name, acc.owner
                        FROM all_constraints ac
                        JOIN all_cons_columns acc
                            ON ac.constraint_name = acc.constraint_name
                            AND ac.owner = acc.owner
                        WHERE ac.constraint_type = 'P'
                    ) pk
                        ON col.table_name = pk.table_name
                        AND col.column_name = pk.column_name
                        AND col.owner = pk.owner
                    WHERE col.table_name = :table_name
                    AND col.owner = :owner
                    ORDER BY col.column_id
                    """,
                    {
                        "table_name": table_name,
                        "owner": db_config["database_username"].upper(),
                    },
                )
                columns = cursor.fetchall()
                result_schema = [
                    {
                        "column_name": row[0],
                        "data_type": row[3].split(" ")[0],
                        "type": row[5],
                        "primary_key": row[6] == "YES",
                        "normalized_type": HelperConnectConfigDatabase.normalize_data_type(row[3].split(" ")[0], DATATYPE_SUPPORT.get("oracle").get("type_mapping"))
                    }
                    for row in columns
                ]
                # chi lay field dung format
                valid_field_schema = [
                    field
                    for field in result_schema
                    if HelperConnectConfigDatabase()._valid_format_column(field.get("column_name"))
                ]
                valid_datatype_schema = [
                    field for field in valid_field_schema if field["data_type"] in DATATYPE_SUPPORT.get("oracle").get("supported")\
                        and field["type"] not in DATATYPE_SUPPORT.get("oracle").get("not_supported")
                ]
                incremental_column_supported = [
                    field for field in valid_datatype_schema if field["normalized_type"] in INCREMENTAL_COLUMN_TYPE_SUPPORTED
                ]

            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return incremental_column_supported
    
    
    @staticmethod
    def db2_get_incremental_columns(table_name, schema, *args):
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_db2_connection(db_config)
            if conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT COLNAME 
                    FROM SYSCAT.KEYCOLUSE k
                    JOIN SYSCAT.TABCONST c
                    ON k.CONSTNAME = c.CONSTNAME
                    AND k.TABSCHEMA = c.TABSCHEMA
                    WHERE c.TYPE = 'P' 
                    AND k.TABNAME = ? 
                    AND k.TABSCHEMA = ?
                    """,
                    (table_name, schema),
                )
                pk_columns = {row[0] for row in cursor.fetchall()}  # set để tra nhanh

                # Lấy thông tin schema chi tiết
                cursor.execute(
                    """
                   SELECT 
                    COLNAME AS column_name,
                    COLNO AS ordinal_position,  
                    NULLS AS is_nullable,
                    TYPENAME AS data_type,
                    LENGTH AS character_maximum_length,
                    CASE 
                        WHEN TYPENAME IN ('CHARACTER', 'VARCHAR') THEN 
                            TYPENAME || '(' || LENGTH || ')'
                        WHEN TYPENAME = 'DECIMAL' AND SCALE IS NOT NULL THEN 
                            TYPENAME || '(' || LENGTH || ',' || SCALE || ')'
                        WHEN TYPENAME = 'DECIMAL' THEN 
                            TYPENAME || '(' || LENGTH || ')'
                        ELSE 
                            TYPENAME
                    END AS full_type
                FROM SYSCAT.COLUMNS
                WHERE TABNAME = ?  
                AND TABSCHEMA = ?
                ORDER BY COLNO
                    """,
                    (table_name, schema),
                )
                columns = cursor.fetchall()

                # Build kết quả schema
                result_schema = [
                    {
                        "column_name": row[0],
                        "data_type": row[3].split(" ")[0],
                        "type": row[5],
                        "primary_key": row[0] in pk_columns,
                        "normalized_type": HelperConnectConfigDatabase.normalize_data_type(row[3].split(" ")[0], DATATYPE_SUPPORT.get("db2").get("type_mapping"))
                    }
                    for row in columns
                ]
                # chi lay field dung format va datatype duoc support
                valid_field_schema = [
                    field
                    for field in result_schema
                    if HelperConnectConfigDatabase()._valid_format_column(field.get("column_name"))
                ]
                valid_datatype_schema = [
                    field for field in valid_field_schema if field["data_type"] in DATATYPE_SUPPORT.get("db2").get("supported")
                ]
                incremental_column_supported = [
                    field for field in valid_datatype_schema if field["normalized_type"] in INCREMENTAL_COLUMN_TYPE_SUPPORTED
                ]

            conn.close()
        except OperationalError as err:
            raise CustomError(f"Cannot retrieve schema: {str(err)}")
        return incremental_column_supported

    @staticmethod
    def sqlserver_tables_views(*args):
        search = args[1].strip() if len(args) > 1 else ""
        db_config = args[0]
        try:
            conn = HelperConnectConfigDatabase._get_sqlserver_connection(db_config)
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        o.name AS object_name,
                        s.name AS schema_name,
                        CASE 
                            WHEN o.type = 'U' THEN 'TABLE'
                            WHEN o.type = 'V' THEN 'VIEW'
                        END AS object_type
                    FROM 
                        sys.objects o
                    JOIN 
                        sys.schemas s ON o.schema_id = s.schema_id
                    WHERE 
                        o.type IN ('U', 'V') -- U = Table, V = View
                    ORDER BY 
                        object_name
                    """
                )
                tables = [
                    {"name": row[0], "type": "table" if row[2] == "TABLE" else "view", "schema": row[1]}
                    for row in cursor.fetchall()
                ]
                if search:
                    tables = [table for table in tables if search.lower() in table["name"].lower()]
            conn.close()
        except Exception as err:
            raise CustomError(f"Cannot retrieve tables and views: {str(err)}")
        return tables


if __name__ == "__main__":

    config = {
        "database_name": "dataflow",
        "database_username": "sa",
        "host": "*************",
        "database_password": "Mobio123@A",
        "port": 1433,
    }
    print(HelperConnectConfigDatabase.sqlserver_get_information_column_table("tdt_full_datatype", "dbo", config))
    # print(HelperConnectConfigDatabase.sqlserver_tables(config))
    # print(HelperConnectConfigDatabase.sqlserver_tables_views(config))
    # print(HelperConnectConfigDatabase.sqlserver_get_data_sample("Folio", 5, "dbo", config))
    # print(HelperConnectConfigDatabase.sqlserver_get_information_column_table("Folio", "dbo", config))

    # print(
    #     HelperConnectConfigDatabase.oracle_get_incremental_columns(
    #         "FULL_DATATYPE_ORACLE",
    #         {
    #             "database_name": "mobio",
    #             "database_username": "mobio",
    #             "host": "*************",
    #             "database_password": "mobio",
    #             "port": 1521,
    #         }
    #     )
    # )
    print(
        HelperConnectConfigDatabase.db2_get_schema(
            "SAMPLE_TABLE",
            "DB2INST1",
            {
                "database_name": "testdb",
                "database_username": "db2inst1",
                "host": "*************",
                "database_password": "Mobio123A",
                "port": 50000
            }

        )
    )

    # print(
    #     HelperConnectConfigDatabase.postgres_get_incremental_columns(
    #         "test_postgres_types_support",
    #         "TDT",
    #         {
    #             "database_name": "Dataflow",
    #             "database_username": "postgres",
    #             "host": "*************",
    #             "database_password": "postgres",
    #             "port": 5432
    #         }
            
    #     )
    # )


    # print(
    #     HelperConnectConfigDatabase.mysql_get_data_sample(
    #         "test_data_types_mobio_support",
    #         3,

    
    # print(
    #     HelperConnectConfigDatabase.mysql_get_incremental_columns(
    #         "test_data_types_mobio_support",

    #         {
    #             "database_name": "dataflow",
    #             "database_username": "mobio",
    #             "host": "*************",
    #             "database_password": "mobio123",
    #             "port": 3306
    #         }
    #     )
    # )
