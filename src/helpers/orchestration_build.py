#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 09/05/2024
"""

import copy
import datetime
import os

from mobio.libs.kafka_lib.helpers.ensure_kafka_topic import EnsureKafkaTopic
from mobio.libs.logging import Mo<PERSON>Logging
from mobio.sdks.base.common.mobio_exception import CustomError

from configs import KafkaReplication
from src.common.data_flow_constant import (
    ConstantActionMappingData,
    ConstantObjectHandle,
    ConstantScheduleConfigType,
    ConstantScheduleType,
)
from src.common.lang_config import LangConfig
from src.helpers.data_flow.get_information_from_database import (
    HelperGetInformationFromDatabase,
)
from src.helpers.kafka.kafka import KafkaHelper
from src.internal_module.orchestration import OrchestrationApiHelper
from src.models.data_flow.config_connectors_model import ConfigConnectorsModel
from src.models.data_flow.setting_add_on_connector_model import (
    SettingAddOnConnectorModel,
)
from src.models.log_sync_data_to_module_other_model import LogSyncDataToModuleOtherModel


class OrchestrationBuildHelper:
    def handle_sync_data(self, merchant_id, connect_config_id, need_stop_connect=False):
        log_sync_orchestration = LogSyncDataToModuleOtherModel().get_detail_log_sync_orchestration_by_connector_id(
            merchant_id, connect_config_id
        )
        orchestration_connector_name = (
            log_sync_orchestration.get("module_data", {}).get("connector_name") if log_sync_orchestration else None
        )
        body_data_sync, module_data_update = self.build_body_sync_pipeline_orchestration(
            merchant_id,
            connect_config_id,
            orchestration_connector_name=orchestration_connector_name,
        )
        orchestration_id = None
        if body_data_sync:
            log_sync_orchestration_id = log_sync_orchestration.get("_id") if log_sync_orchestration else None
            orchestration_id = log_sync_orchestration.get("module_id") if log_sync_orchestration else None
            MobioLogging().info("OrchestrationHelper :: body_data_sync :: {}".format(body_data_sync))
            if not orchestration_id:
                response = OrchestrationApiHelper().create_pipeline(merchant_id, body_data_sync)
                data_logs = [
                    {
                        "response": response,
                        "action_time": datetime.datetime.now(datetime.UTC),
                    }
                ]
                orchestration_id = response.get("_id") if response else None
                if not log_sync_orchestration_id:
                    connector_name = response.get("pipeline_name") if response else None
                    module_data = {"connector_name": connector_name}
                    if module_data_update:
                        module_data.update(module_data_update)
                    log_sync_orchestration_id = (
                        LogSyncDataToModuleOtherModel().insert_log_sync_data_to_module_orchestration(
                            merchant_id=merchant_id,
                            connector_id=connect_config_id,
                            orchestration_id=orchestration_id,
                            data_logs=data_logs,
                            module_data=module_data,
                            body_data_sync=body_data_sync,
                        )
                    )
                else:
                    LogSyncDataToModuleOtherModel().update_log_sync_data_to_module_orchestration(
                        log_id=log_sync_orchestration_id,
                        orchestration_id=orchestration_id,
                        body_data_sync=body_data_sync,
                    )
                MobioLogging().info("OrchestrationBuildHelper :: log_id :: {}".format(str(log_sync_orchestration_id)))
                # data_update = {
                #     "state": {
                #         "status_handle_sync": ConstantStatusHandleSyncData.DONE,
                #         "type": ConstantConnectorStateCode.CREATE_SYNC_ORCHESTRATION,
                #         "orchestration_id": orchestration_id,
                #         "response": response,
                #         "request": body_data_sync,
                #     }
                # }

            else:
                MobioLogging().info(
                    "OrchestrationBuildHelper :: update :: orchestration_id :: {}".format(str(orchestration_id))
                )

                response = OrchestrationApiHelper().update_pipeline(
                    merchant_id,
                    body_data_sync,
                    orchestration_id,
                )
                # data_update = {
                #     "state": {
                #         "status_handle_sync": ConstantStatusHandleSyncData.DONE,
                #         "type": ConstantConnectorStateCode.UPDATE_SYNC_ORCHESTRATION,
                #         "orchestration_id": orchestration_id,
                #         "response": response,
                #         "request": body_data_sync,
                #     }
                # }
                LogSyncDataToModuleOtherModel().update_log_sync_data_to_module_orchestration(
                    log_id=log_sync_orchestration_id,
                    orchestration_id=orchestration_id,
                    body_data_sync=body_data_sync,
                )

            # HandleQueue.push_message_connector_config(
            #     connect_config_id=connect_config_id,
            #     data_before={},
            #     data_after=data_update,
            #     action="sync",
            #     merchant_id=merchant_id,
            #     account_id=None,
            # )
        return orchestration_id

    def build_body_sync_pipeline_orchestration(self, merchant_id, connector_id, orchestration_connector_name):
        connector_detail = ConfigConnectorsModel().detail_connector_by_id(merchant_id, connector_id)
        if not connector_detail:
            raise Exception("Could not find connector")
        config_sync_calendar = connector_detail.get("config_sync_calendar")
        # if not config_sync_calendar:
        #     raise CustomError("Could not find config_sync_calendar")
        data_add_pipeline = {
            # "deployment_name": (
            #     self.build_name_orchestration(connector_id)
            #     if not orchestration_connector_name
            #     else orchestration_connector_name
            # ),
            "name": connector_detail.get("name"),
            "pipeline_name": (
                self.build_name_orchestration(connector_id)
                if not orchestration_connector_name
                else orchestration_connector_name
            ),
            "merchant_id": merchant_id,
        }
        source_key = connector_detail.get("source_key")
        source_type = connector_detail.get("source_type")
        data_type = connector_detail.get("data_type")
        try:
            data_build, module_data_update = getattr(
                self,
                "build_data_pipeline_{}_{}_{}".format(data_type, source_type, source_key),
            )(merchant_id, connector_id, connector_detail)
        except Exception as e:
            MobioLogging().error(
                "OrchestrationBuildHelper :: build_body_sync_pipeline_orchestration :: {}".format(str(e))
            )
            raise CustomError(str(e))
        if not data_build:
            return None, None
        data_add_pipeline.update(data_build)
        if config_sync_calendar:
            data_add_pipeline.update(self.build_data_config_sync_calendar_sync_orchestration(config_sync_calendar))
        MobioLogging().info(
            "OrchestrationBuildHelper :: data_add_pipeline :: data_build :: {}".format(str(data_add_pipeline))
        )
        return data_add_pipeline, module_data_update

    def build_connector_name_sync_orchestration(self, primary_obj, obj_attr):
        if primary_obj == ConstantObjectHandle.Object.PROFILES:
            if obj_attr == ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT:
                return "dynamicevent-csm-df-insert-de"
            if obj_attr == ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING:
                return "mobioetl-csm-df-upsert-ph-profile"
            return (
                ConstantObjectHandle.Object.PROFILES
                if obj_attr == ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE or not obj_attr
                else obj_attr
            )
        return primary_obj

    def build_sinks_config_sync_orchestration(
        self, merchant_id, connector_id, connector_detail, setting_add_on_connector
    ):

        source_key = connector_detail.get("source_key")
        topic_handle_name = "am.connector.process.{}.{}.{}".format(source_key, merchant_id, connector_id)
        topic_report_name = "am.connector.report.{}.{}.{}".format(source_key, merchant_id, connector_id)

        result_create = self.check_topic_name_exists([topic_handle_name, topic_report_name])
        if not result_create:
            raise Exception("Not create topic name")
        topic_config = {
            "topic_name": topic_handle_name,
            "topic_report_name": topic_report_name,
        }

        target_push_data_transform = {}
        if setting_add_on_connector:
            target_push_data_transform = setting_add_on_connector.get("target_push_data_transform", {})

        result = {
            "process": {"callback_type": "queue", "target": topic_handle_name},
            "report": {"callback_type": "queue", "target": topic_report_name},
        }
        if target_push_data_transform and target_push_data_transform.get("queue_config", {}).get("target", ""):
            result.update(
                {
                    "transform": {
                        "callback_type": "queue",
                        "target": target_push_data_transform.get("queue_config", {}).get("target", ""),
                    }
                }
            )
            topic_config["topic_transform"] = target_push_data_transform.get("queue_config", {}).get("target", "")
        return result, topic_config

    def build_columns_source_database_sync_orchestration(self, connector_detail, table_name):
        source_key = connector_detail.get("source_key")
        source_type = connector_detail.get("source_type")
        data_type = connector_detail.get("data_type")
        config_connect = connector_detail.get("config_connect")
        merchant_id = connector_detail.get("config_connect")
        config_mapping_data = connector_detail.get("config_mapping_data", {})
        schema = config_mapping_data.get("schema")
        func_name = HelperGetInformationFromDatabase().get_func_get_information_from_database(
            "information_column_table", source_key, source_type, data_type
        )
        try:
            result = HelperGetInformationFromDatabase().call_get(
                func_name, LangConfig().lang_map("vi"), config_connect, merchant_id, table_name, schema
            )
        except Exception as e:
            MobioLogging().error(
                "OrchestrationBuildHelper :: build_columns_source_database_sync_orchestration :: {}".format(str(e))
            )
            raise CustomError(str(e))
        # chỉ trả về các col có trong mapping + primary key
        filtered_result_columns = [
            col for col in result 
            if col["column_name"] in {field["field_source"] for field in config_mapping_data.get("fields", [])} 
            or col.get("is_primary_key", False)
        ]

        return filtered_result_columns

    def build_data_pipeline_data_in_databases_postgres(self, merchant_id, connector_id, connector_detail):
        config_connect = connector_detail.get("config_connect")
        config_mapping_data = connector_detail.get("config_mapping_data")
        config_rule_unification = connector_detail.get("config_rule_unification")
        config_rule_unification_secondary_object = connector_detail.get("config_rule_unification_secondary_object")
        is_trust_source = connector_detail.get("is_trust_source", False)
        object_action = connector_detail.get("action", "upsert")
        connector_name = connector_detail.get("name", "")
        if not config_mapping_data or not config_rule_unification:
            return None, None
        primary_object = connector_detail.get("object")
        object_attribute = connector_detail.get("object_attribute")
        config_data_default_of_primary_object = connector_detail.get("config_data_default_of_primary_object")
        result = {
            "source": {
                "connector": "postgres-cdc",
                "hostname": config_connect.get("host"),
                "port": config_connect.get("port"),
                "username": config_connect.get("database_username"),
                "password": config_connect.get("database_password"),
                "database_name": config_connect.get("database_name"),
                "schema_name": config_mapping_data.get("schema") if config_mapping_data.get("schema") else "public",
                "table_name": config_mapping_data.get("table"),
                "columns": self.build_columns_source_database_sync_orchestration(
                    connector_detail, config_mapping_data.get("table")
                ),
            },
            "connector": {
                "id": int(connector_id),
                "connector_id": int(connector_id),
                "source_key": connector_detail.get("source_key"),
                "merchant_id": merchant_id,
                "name": connector_detail.get("name"),
                "deployment_name": self.build_connector_name_sync_orchestration(primary_object, object_attribute),
                "fields_verify": connector_detail.get("list_field_verify", []),
                "is_trust": is_trust_source,
            },
        }
        setting_add_on_connector = SettingAddOnConnectorModel().get_setting_by_merchant_id(
            merchant_id=merchant_id,
            source_key=connector_detail.get("source_key"),
            source_type=connector_detail.get("source_type"),
            data_type=connector_detail.get("data_type"),
            object_primary=primary_object,
            object_attribute=object_attribute,
        )

        result["sinks"], module_data = self.build_sinks_config_sync_orchestration(
            merchant_id=merchant_id,
            connector_id=connector_id,
            connector_detail=connector_detail,
            setting_add_on_connector=setting_add_on_connector,
        )

        data_connector_convert_sync, _ = self.build_data_config_connector_sync_orchestration(
            config_mapping_data=config_mapping_data,
            config_rule_unification=config_rule_unification,
            primary_object=primary_object,
            connector_name=connector_name,
            config_rule_unification_secondary_object=config_rule_unification_secondary_object,
            is_trust_source=is_trust_source,
            object_action=object_action,
            fields_verify=connector_detail.get("list_field_verify", []),
            object_attribute=object_attribute,
            setting_add_on_connector=setting_add_on_connector,
            config_data_default_of_primary_object=config_data_default_of_primary_object,
        )
        result["connector"].update(data_connector_convert_sync)

        return result, module_data

    def build_data_pipeline_data_in_databases_oracle(self, merchant_id, connector_id, connector_detail):
        config_connect = connector_detail.get("config_connect")
        config_mapping_data = connector_detail.get("config_mapping_data")
        config_rule_unification = connector_detail.get("config_rule_unification")
        config_rule_unification_secondary_object = connector_detail.get("config_rule_unification_secondary_object")
        is_trust_source = connector_detail.get("is_trust_source", False)
        object_action = connector_detail.get("action", "upsert")
        connector_name = connector_detail.get("name", "")
        if not config_mapping_data or not config_rule_unification:
            return None, None
        primary_object = connector_detail.get("object")
        object_attribute = connector_detail.get("object_attribute")
        config_data_default_of_primary_object = connector_detail.get("config_data_default_of_primary_object")
        result = {
            "source": {
                "connector": "oracle-cdc",
                "hostname": config_connect.get("host"),
                "port": config_connect.get("port"),
                "username": config_connect.get("database_username"),
                "password": config_connect.get("database_password"),
                "database_name": config_connect.get("database_name"),
                "schema_name": config_mapping_data.get("schema") if config_mapping_data.get("schema") else None,
                "table_name": config_mapping_data.get("table"),
                "columns": self.build_columns_source_database_sync_orchestration(
                    connector_detail, config_mapping_data.get("table")
                ),
            },
            "connector": {
                "id": int(connector_id),
                "connector_id": int(connector_id),
                "source_key": connector_detail.get("source_key"),
                "merchant_id": merchant_id,
                "name": connector_detail.get("name"),
                "deployment_name": self.build_connector_name_sync_orchestration(primary_object, object_attribute),
                "fields_verify": connector_detail.get("list_field_verify", []),
                "is_trust": is_trust_source,
            },
        }
        setting_add_on_connector = SettingAddOnConnectorModel().get_setting_by_merchant_id(
            merchant_id=merchant_id,
            source_key=connector_detail.get("source_key"),
            source_type=connector_detail.get("source_type"),
            data_type=connector_detail.get("data_type"),
            object_primary=primary_object,
            object_attribute=object_attribute,
        )

        result["sinks"], module_data = self.build_sinks_config_sync_orchestration(
            merchant_id=merchant_id,
            connector_id=connector_id,
            connector_detail=connector_detail,
            setting_add_on_connector=setting_add_on_connector,
        )

        data_connector_convert_sync, _ = self.build_data_config_connector_sync_orchestration(
            config_mapping_data=config_mapping_data,
            config_rule_unification=config_rule_unification,
            primary_object=primary_object,
            connector_name=connector_name,
            config_rule_unification_secondary_object=config_rule_unification_secondary_object,
            is_trust_source=is_trust_source,
            object_action=object_action,
            fields_verify=connector_detail.get("list_field_verify", []),
            object_attribute=object_attribute,
            setting_add_on_connector=setting_add_on_connector,
            config_data_default_of_primary_object=config_data_default_of_primary_object,
        )
        result["connector"].update(data_connector_convert_sync)
        return result, module_data

    def build_data_pipeline_data_in_databases_mysql(self, merchant_id, connector_id, connector_detail):
        config_connect = connector_detail.get("config_connect")
        config_mapping_data = connector_detail.get("config_mapping_data")
        config_rule_unification = connector_detail.get("config_rule_unification")
        config_rule_unification_secondary_object = connector_detail.get("config_rule_unification_secondary_object")
        is_trust_source = connector_detail.get("is_trust_source", False)
        connector_name = connector_detail.get("name", False)
        object_action = connector_detail.get("action", "upsert")
        if not config_mapping_data or not config_rule_unification:
            return None, None
        primary_object = connector_detail.get("object")
        object_attribute = connector_detail.get("object_attribute")
        config_data_default_of_primary_object = connector_detail.get("config_data_default_of_primary_object")
        result = {
            "source": {
                "connector": "mysql-cdc",
                "hostname": config_connect.get("host"),
                "port": config_connect.get("port"),
                "username": config_connect.get("database_username"),
                "password": config_connect.get("database_password"),
                "database_name": config_connect.get("database_name"),
                "schema_name": None,
                "table_name": config_mapping_data.get("table"),
                "columns": self.build_columns_source_database_sync_orchestration(
                    connector_detail, config_mapping_data.get("table")
                ),
            },
            "connector": {
                "id": int(connector_id),
                "connector_id": int(connector_id),
                "merchant_id": merchant_id,
                "source_key": connector_detail.get("source_key"),
                "name": connector_detail.get("name"),
                "deployment_name": self.build_connector_name_sync_orchestration(primary_object, object_attribute),
                "fields_verify": config_connect.get("list_field_verify", []),
                "is_trust": connector_detail.get("is_trust_source", False),
            },
        }
        setting_add_on_connector = SettingAddOnConnectorModel().get_setting_by_merchant_id(
            merchant_id=merchant_id,
            source_key=connector_detail.get("source_key"),
            source_type=connector_detail.get("source_type"),
            data_type=connector_detail.get("data_type"),
            object_primary=primary_object,
            object_attribute=object_attribute,
        )

        result["sinks"], module_data = self.build_sinks_config_sync_orchestration(
            merchant_id=merchant_id,
            connector_id=connector_id,
            connector_detail=connector_detail,
            setting_add_on_connector=setting_add_on_connector,
        )

        data_connector_convert_sync, _ = self.build_data_config_connector_sync_orchestration(
            config_mapping_data=config_mapping_data,
            config_rule_unification=config_rule_unification,
            primary_object=primary_object,
            connector_name=connector_name,
            config_rule_unification_secondary_object=config_rule_unification_secondary_object,
            is_trust_source=is_trust_source,
            object_action=object_action,
            fields_verify=connector_detail.get("list_field_verify", []),
            object_attribute=object_attribute,
            setting_add_on_connector=setting_add_on_connector,
            config_data_default_of_primary_object=config_data_default_of_primary_object,
        )
        result["connector"].update(data_connector_convert_sync)
        return result, module_data
    
    def build_data_pipeline_data_in_databases_db2(self, merchant_id, connector_id, connector_detail):
        config_connect = connector_detail.get("config_connect")
        config_mapping_data = connector_detail.get("config_mapping_data")
        config_rule_unification = connector_detail.get("config_rule_unification")
        config_rule_unification_secondary_object = connector_detail.get("config_rule_unification_secondary_object")
        is_trust_source = connector_detail.get("is_trust_source", False)
        connector_name = connector_detail.get("name", False)
        object_action = connector_detail.get("action", "upsert")
        if not config_mapping_data or not config_rule_unification:
            return None, None
        primary_object = connector_detail.get("object")
        object_attribute = connector_detail.get("object_attribute")
        config_data_default_of_primary_object = connector_detail.get("config_data_default_of_primary_object")
        result = {
            "source": {
                "connector": "db2-cdc",
                "hostname": config_connect.get("host"),
                "port": config_connect.get("port"),
                "username": config_connect.get("database_username"),
                "password": config_connect.get("database_password"),
                "database_name": config_connect.get("database_name"),
                "schema_name": config_mapping_data.get("schema") if config_mapping_data.get("schema") else "TEST_SCHEMA",
                "table_name": config_mapping_data.get("table"),
                "columns": self.build_columns_source_database_sync_orchestration(
                    connector_detail, config_mapping_data.get("table")
                ),
            },
            "connector": {
                "id": int(connector_id),
                "connector_id": int(connector_id),
                "merchant_id": merchant_id,
                "source_key": connector_detail.get("source_key"),
                "name": connector_detail.get("name"),
                "deployment_name": self.build_connector_name_sync_orchestration(primary_object, object_attribute),
                "fields_verify": config_connect.get("list_field_verify", []),
                "is_trust": connector_detail.get("is_trust_source", False),
            },
        }
        setting_add_on_connector = SettingAddOnConnectorModel().get_setting_by_merchant_id(
            merchant_id=merchant_id,
            source_key=connector_detail.get("source_key"),
            source_type=connector_detail.get("source_type"),
            data_type=connector_detail.get("data_type"),
            object_primary=primary_object,
            object_attribute=object_attribute,
        )

        result["sinks"], module_data = self.build_sinks_config_sync_orchestration(
            merchant_id=merchant_id,
            connector_id=connector_id,
            connector_detail=connector_detail,
            setting_add_on_connector=setting_add_on_connector,
        )

        data_connector_convert_sync, _ = self.build_data_config_connector_sync_orchestration(
            config_mapping_data=config_mapping_data,
            config_rule_unification=config_rule_unification,
            primary_object=primary_object,
            connector_name=connector_name,
            config_rule_unification_secondary_object=config_rule_unification_secondary_object,
            is_trust_source=is_trust_source,
            object_action=object_action,
            fields_verify=connector_detail.get("list_field_verify", []),
            object_attribute=object_attribute,
            setting_add_on_connector=setting_add_on_connector,
            config_data_default_of_primary_object=config_data_default_of_primary_object,
        )
        result["connector"].update(data_connector_convert_sync)
        return result, module_data

    def build_data_pipeline_data_out_server_webhooks(self, merchant_id, connector_id, connector_detail):
        result = {
            "source": {
                "connector": "dataout",
            },
            "connector": {
                "id": int(connector_id),
                "merchant_id": merchant_id,
                "name": connector_detail.get("name"),
                "deployment_name": "dataout",
            },
            "mode": "streaming",
        }
        return result, None

    def build_data_pipeline_data_out_raw_data_google_sheet(self, merchant_id, connector_id, connector_detail):
        result = {
            "source": {
                "connector": "google-sheets",
            },
            "connector": {
                "id": int(connector_id),
                "merchant_id": merchant_id,
                "name": connector_detail.get("name"),
                "deployment_name": "google-sheets",
            },
            "mode": "streaming",
        }
        return None, None

    def build_data_pipeline_data_in_server_api(self, merchant_id, connector_id, connector_detail):
        # topic_handle_name = "am.connector.process.api.{}.{}".format(merchant_id, connector_id)
        # topic_report_name = "am.connector.report.api.{}.{}".format(merchant_id, connector_id)
        # result_create = self.check_topic_name_exists([topic_handle_name, topic_report_name])
        config_mapping_data = connector_detail.get("config_mapping_data")
        config_rule_unification = connector_detail.get("config_rule_unification")
        primary_object = connector_detail.get("object")
        config_rule_unification_secondary_object = connector_detail.get("config_rule_unification_secondary_object")
        is_trust_source = connector_detail.get("is_trust_source", False)
        connector_name = connector_detail.get("name")
        object_attribute = connector_detail.get("object_attribute")
        object_action = connector_detail.get("action", "upsert")
        if not config_mapping_data or not config_rule_unification:
            return None, None
        result = {
            "source": {
                "connector": "api",
            },
            "connector": {
                "id": int(connector_id),
                "connector_id": int(connector_id),
                "merchant_id": merchant_id,
                "source_key": connector_detail.get("source_key"),
                "name": connector_detail.get("name"),
                "deployment_name": self.build_connector_name_sync_orchestration(primary_object, object_attribute),
                "fields_verify": connector_detail.get("list_field_verify", []),
                "is_trust": connector_detail.get("is_trust_source", False),
                "action": object_action,
            },
        }
        object_attribute = connector_detail.get("object_attribute")
        config_data_default_of_primary_object = connector_detail.get("config_data_default_of_primary_object")
        setting_add_on_connector = SettingAddOnConnectorModel().get_setting_by_merchant_id(
            merchant_id=merchant_id,
            source_key=connector_detail.get("source_key"),
            source_type=connector_detail.get("source_type"),
            data_type=connector_detail.get("data_type"),
            object_primary=primary_object,
            object_attribute=object_attribute,
        )

        result["sinks"], module_data = self.build_sinks_config_sync_orchestration(
            merchant_id=merchant_id,
            connector_id=connector_id,
            connector_detail=connector_detail,
            setting_add_on_connector=setting_add_on_connector,
        )

        result["source"].update(
            {
                "kafka_topic": module_data.get("topic_name"),
                "kafka_group": module_data.get("topic_name"),
            }
        )

        data_connector_convert_sync, _ = self.build_data_config_connector_sync_orchestration(
            config_mapping_data=config_mapping_data,
            config_rule_unification=config_rule_unification,
            primary_object=primary_object,
            connector_name=connector_name,
            config_rule_unification_secondary_object=config_rule_unification_secondary_object,
            is_trust_source=is_trust_source,
            object_action=object_action,
            fields_verify=connector_detail.get("list_field_verify", []),
            object_attribute=object_attribute,
            setting_add_on_connector=setting_add_on_connector,
            config_data_default_of_primary_object=config_data_default_of_primary_object,
        )
        result["connector"].update(data_connector_convert_sync)
        return result, module_data

    def build_data_pipeline_data_in_form_contact_form(self, merchant_id, connector_id, connector_detail):

        config_mapping_data = connector_detail.get("config_mapping_data")
        config_rule_unification = connector_detail.get("config_rule_unification")
        primary_object = connector_detail.get("object")
        object_attribute = connector_detail.get("object_attribute")
        config_rule_unification_secondary_object = connector_detail.get("config_rule_unification_secondary_object")
        is_trust_source = connector_detail.get("is_trust_source", False)
        connector_name = connector_detail.get("name", False)
        object_action = connector_detail.get("action", "add")
        if not config_mapping_data or not config_rule_unification:
            return None, None
        result = {
            "source": {"connector": "api"},
            "connector": {
                "id": int(connector_id),
                "connector_id": int(connector_id),
                "merchant_id": merchant_id,
                "source_key": connector_detail.get("source_key"),
                "name": connector_detail.get("name"),
                "deployment_name": self.build_connector_name_sync_orchestration(primary_object, object_attribute),
                "fields_verify": connector_detail.get("list_field_verify", []),
                "is_trust": connector_detail.get("is_trust_source", False),
                "action": object_action,
            },
        }

        setting_add_on_connector = SettingAddOnConnectorModel().get_setting_by_merchant_id(
            merchant_id=merchant_id,
            source_key=connector_detail.get("source_key"),
            source_type=connector_detail.get("source_type"),
            data_type=connector_detail.get("data_type"),
        )

        result["sinks"], module_data_update = self.build_sinks_config_sync_orchestration(
            merchant_id=merchant_id,
            connector_id=connector_id,
            connector_detail=connector_detail,
            setting_add_on_connector=setting_add_on_connector,
        )
        result["source"].update(
            {
                "kafka_topic": module_data_update.get("topic_name"),
                "kafka_group": module_data_update.get("topic_name"),
            }
        )

        data_connector_convert_sync, _ = self.build_data_config_connector_sync_orchestration(
            config_mapping_data=config_mapping_data,
            config_rule_unification=config_rule_unification,
            primary_object=primary_object,
            connector_name=connector_name,
            config_rule_unification_secondary_object=config_rule_unification_secondary_object,
            is_trust_source=is_trust_source,
            object_action=object_action,
            setting_add_on_connector=setting_add_on_connector,
        )
        result["connector"].update(data_connector_convert_sync)
        return result, module_data_update

    @classmethod
    def _convert_values_type_weeks(cls, schedule_config_values):
        day_mapping = {
            "mon": "monday",
            "tue": "tuesday",
            "wed": "wednesday",
            "thu": "thursday",
            "fri": "friday",
            "sat": "saturday",
            "sun": "sunday",
        }
        results = []
        if schedule_config_values:
            for item in schedule_config_values:
                results.append(day_mapping.get(item, item))
        return results

    def build_data_config_sync_calendar_sync_orchestration(self, data_config):
        mode = data_config.get("mode")
        result = {"mode": mode, "schedule": {}}
        if mode == "snapshot":
            data_config_schedule = data_config.get("schedule")
            schedule_type = data_config_schedule.get("type")
            schedule = {}
            if schedule_type == ConstantScheduleType.INTERVAL:
                schedule_config = data_config_schedule.get("config")
                schedule_config_hour = schedule_config.get("hour")
                schedule_config_type = schedule_config.get("type")
                schedule_config_values = schedule_config.get("values")
                schedule = {
                    "time_in_day": schedule_config_hour,
                    "day_in_week": [],
                    "day_in_month": [],
                    "type": schedule_config_type,
                }
                if schedule_config_type == ConstantScheduleConfigType.WEEK:
                    schedule_config_values = self._convert_values_type_weeks(schedule_config_values)
                    schedule.update({"day_in_week": schedule_config_values})
                if schedule_config_type == ConstantScheduleConfigType.MONTH:
                    schedule.update({"day_in_month": schedule_config_values})

            result.update({"mode": "batch", "schedule": schedule})
        return result

    def build_data_config_connector_sync_orchestration(
        self,
        config_mapping_data,
        config_rule_unification,
        primary_object,
        connector_name,
        config_rule_unification_secondary_object=None,
        is_trust_source=None,
        object_action=None,
        fields_verify=[],
        setting_add_on_connector=None,
        object_attribute=None,
        config_data_default_of_primary_object=None,
    ):
        """
        Example: {
            "primary_object": "profile",
            "profile_connector_config": {
                "name": "profiles",
                "fields_verify": [],
                "is_trust": true,
                "fields_replace": [],
                "fields_append": [],
                "fields_replace_ignore_empty": [
                    "source",
                    "cif",
                    "name",
                    "primary_phone",
                    "primary_email",
                    "profile_identify",
                    "profile_owner",
                    "address_personal"
                ],
                "unification_rules": {
                    "operators": [
                        {
                            "priority": 1,
                            "fields": {
                                "cif": {
                                    "match_type": "exact",
                                    "normalized_type": "string"
                                },
                                "source": {
                                    "match_type": "exact",
                                    "normalized_type": "string"
                                }
                            }
                        }
                    ],
                    "consent": {
                        "analytics_consent": "Có",
                        "mkt_consent": "Có",
                        "tracking_consent": "Có"
                    }
                },
                "data_recording_rules": {
                    "operators": [
                        {
                            "priority": 1,
                            "fields": {
                                "cif": {
                                    "match_type": "exact",
                                    "normalized_type": "string"
                                }
                            }
                        }
                    ],
                    "consent": {
                        "analytics_consent": "Có",
                        "mkt_consent": "Có",
                        "tracking_consent": "Có"
                    }
                },
            },
            "version": 1
        }
        """

        mapping_object_to_key = {
            ConstantObjectHandle.Object.PROFILES: "profile",
            ConstantObjectHandle.Object.SALE: "deal",
        }

        primary_object = (
            mapping_object_to_key.get(object_attribute, object_attribute)
            if object_attribute
            else mapping_object_to_key.get(primary_object, primary_object)
        )

        fields = config_mapping_data.get("fields", [])

        config_mapping_data_field_by_object = {}

        config_mapping_field_by_object = {}

        mapping_object_setting_add_on_include_fields = {}
        target_push_data_transform = {}
        if setting_add_on_connector:
            extract_config = setting_add_on_connector.get("extract_config", {})
            if extract_config:
                include_fields = extract_config.get("include_fields")
                for item in include_fields:
                    object_include_field = mapping_object_to_key.get(item.get("object"), item.get("object"))
                    source = item.get("source")
                    if object_include_field not in mapping_object_setting_add_on_include_fields:
                        mapping_object_setting_add_on_include_fields[object_include_field] = {}
                    mapping_object_setting_add_on_include_fields[object_include_field][
                        "fields_by_{}".format(source)
                    ] = item.get("fields")
            target_push_data_transform = setting_add_on_connector.get("target_push_data_transform", {})

        for field in fields:
            action = field.get("action")
            field_target = field.get("field_target")
            object_mapping = field.get("object")
            is_verify = field.get("is_verify")
            display_type = field.get("display_type")
            field_source_type = (
                field.get("field_source_schema_type")
                if field.get("field_source_schema_type")
                else field.get("field_source_type")
            )
            field["field_source_type"] = field_source_type

            obj_mapping = mapping_object_to_key.get(object_mapping, object_mapping)

            if obj_mapping not in config_mapping_field_by_object:
                config_mapping_field_by_object[obj_mapping] = {
                    "fields_verify": [],
                    "fields_replace": [],
                    "fields_append": [],
                    "fields_replace_ignore_empty": [],
                }

            if obj_mapping not in config_mapping_data_field_by_object:
                config_mapping_data_field_by_object[obj_mapping] = []
            config_mapping_data_field_by_object[obj_mapping].append(field)
            if field_target:
                field_target = field_target.replace("[*]", "")

            field_target_parent = field_target.split(".")[0] if "." in field_target else None
            if is_verify and field_target:
                config_mapping_field_by_object[obj_mapping]["fields_verify"].append(field_target)
            if action == ConstantActionMappingData.OVERWRITE and field_target:
                config_mapping_field_by_object[obj_mapping]["fields_replace"].append(field_target)
                if (
                    field_target_parent
                    and field_target_parent not in config_mapping_field_by_object[obj_mapping]["fields_replace"]
                ):
                    config_mapping_field_by_object[obj_mapping]["fields_replace"].append(field_target_parent)
                # fields_replace.append(field_target)
            if (
                action
                in [
                    ConstantActionMappingData.ADD,
                    ConstantActionMappingData.SEARCH_OR_INSERT,
                ]
                and field_target
            ):
                # fields_append.append(field_target)    
                item_key = "fields_replace" if display_type == "single_line" else "fields_append"
                    
                config_mapping_field_by_object[obj_mapping][item_key].append(field_target)
                if (
                    field_target_parent
                    and field_target_parent not in config_mapping_field_by_object[obj_mapping][item_key]
                ):
                    config_mapping_field_by_object[obj_mapping][item_key].append(field_target_parent)
            if action == ConstantActionMappingData.OVERWRITE_AND_IGNORE_VALUE_NULL and field_target:
                config_mapping_field_by_object[obj_mapping]["fields_replace_ignore_empty"].append(field_target)
                if (
                    field_target_parent
                    and field_target_parent
                    not in config_mapping_field_by_object[obj_mapping]["fields_replace_ignore_empty"]
                ):
                    config_mapping_field_by_object[obj_mapping]["fields_replace_ignore_empty"].append(
                        field_target_parent
                    )
        if mapping_object_setting_add_on_include_fields:
            config_mapping_field_by_object[obj_mapping].update(
                mapping_object_setting_add_on_include_fields.get(obj_mapping, {})
            )

        if (
            primary_object == "profile"
            and config_mapping_field_by_object[primary_object]["fields_verify"] == []
            and fields_verify
        ):
            config_mapping_field_by_object[primary_object]["fields_verify"] = fields_verify

        if "config_mapping_field_with_fe" in config_mapping_data:
            config_mapping_data.pop("config_mapping_field_with_fe")
        primary_object_unification_rules = {
            "operators": config_rule_unification.get("data_update_rules", {}).get("operators"),
            "consent": config_rule_unification.get("consent", {}),
        }
        primary_object_data_recording_rules = {
            "operators": config_rule_unification.get("data_recording_rules", {}).get("operators"),
            "consent": config_rule_unification.get("consent", {}),
        }

        lst_field_gen_partition = []
        operators_config_rule_data_recording_rules = config_rule_unification.get("data_recording_rules", {}).get(
            "operators", []
        )
        for operator in operators_config_rule_data_recording_rules:
            fields = operator.get("fields")
            lst_field_gen_partition = list(fields.keys())

        config_information_object = {}

        config_mapping_data_by_object_primary = copy.deepcopy(config_mapping_data)
        config_mapping_data_by_object_primary.update(
            {"fields": config_mapping_data_field_by_object.get(primary_object, [])}
        )

        result_primary_object = {
            "action": object_action,
            "name": connector_name,
            "is_trust_source": is_trust_source,
            "unification_rules": primary_object_unification_rules,
            "data_recording_rules": primary_object_data_recording_rules,
            "mapping_data": config_mapping_data_by_object_primary,
            "primary_object": primary_object,
            "list_field_gen_partition": lst_field_gen_partition,
            "config_information_object": config_information_object,
        }

        result_primary_object.update(config_mapping_field_by_object.get(primary_object, {}))

        if config_data_default_of_primary_object:
            for config_data in config_data_default_of_primary_object:
                result_primary_object["config_information_object"][config_data.get("field_key")] = config_data.get(
                    "value"
                )

        result = {"{}_connector_config".format(primary_object): result_primary_object}
        # if target_push_data_transform:
        #     result.update({"target_push_data_transform": target_push_data_transform})

        if config_rule_unification_secondary_object:

            for item in config_rule_unification_secondary_object:
                secondary_object = item.get("secondary_object")
                if not secondary_object:
                    continue
                obj = mapping_object_to_key.get(secondary_object, secondary_object)
                obj_connector_config = "{}_connector_config".format(obj)
                result_item = {
                    obj_connector_config: {
                        "name": connector_name,
                        "is_trust_source": is_trust_source,
                        "object": obj,
                    }
                }
                secondary_object_unification_rules = {}
                data_update_rules = item.get("data_update_rules", {})
                if data_update_rules:

                    secondary_object_unification_rules = {
                        "operators": item.get("data_update_rules", {}).get("operators"),
                        "consent": item.get("consent", {}),
                    }
                secondary_object_data_recording_rules = {}
                data_recording_rules = item.get("data_recording_rules", {})
                if data_recording_rules:
                    secondary_object_data_recording_rules = {
                        "operators": item.get("data_recording_rules", {}).get("operators"),
                        "consent": item.get("consent", {}),
                    }
                config_mapping_data_by_object_secondary = copy.deepcopy(config_mapping_data)
                config_mapping_data_by_object_secondary.update(
                    {"fields": config_mapping_data_field_by_object.get(obj, [])}
                )
                result_item[obj_connector_config].update(
                    {
                        "unification_rules": secondary_object_unification_rules,
                        "data_recording_rules": secondary_object_data_recording_rules,
                        "mapping_data": config_mapping_data_by_object_secondary,
                        "object": mapping_object_to_key.get(secondary_object, secondary_object),
                    }
                )
                result_item[obj_connector_config].update(config_mapping_field_by_object.get(obj, {}))
                result.update(result_item)
        return result, target_push_data_transform

    def build_name_orchestration(self, connector_id):
        return "connector_{}_{}".format(str(connector_id), int(datetime.datetime.now(datetime.UTC).timestamp()))

    def check_topic_name_exist(self, topic_name):

        topic = {
            EnsureKafkaTopic.TOPIC_NAME: topic_name,
            EnsureKafkaTopic.NUM_PARTITIONS: 8,
            EnsureKafkaTopic.CONFIG: {"compression.type": "snappy"},
            EnsureKafkaTopic.REPLICATION_ASSIGNMENT: os.getenv(KafkaReplication.DEFAULT_BROKER_ID_ASSIGN),
        }
        return KafkaHelper().create_kafka_topic(topic)

    def check_topic_name_exists(self, lst_topic_name):

        topics = []
        for topic_name in lst_topic_name:
            topic = {
                EnsureKafkaTopic.TOPIC_NAME: topic_name,
                EnsureKafkaTopic.NUM_PARTITIONS: 8,
                EnsureKafkaTopic.CONFIG: {"compression.type": "snappy"},
                EnsureKafkaTopic.REPLICATION_ASSIGNMENT: os.getenv(KafkaReplication.DEFAULT_BROKER_ID_ASSIGN),
            }
            topics.append(topic)
        if topics:
            return KafkaHelper().create_kafka_topics(lst_topic=topics)


if __name__ == "__main__":
    print(
        OrchestrationBuildHelper().build_columns_source_database_sync_orchestration(
            {
                "merchant_id" : "b18106d8-3397-11ef-9186-036799276e51",
                "created_by" : "2d6a856e-51d0-41cb-ac6c-e1a3c805471e",
                "status" : "active",
                "updated_by" : "2d6a856e-51d0-41cb-ac6c-e1a3c805471e",
                "config_connect" : {
                    "host" : "*************",
                    "port" : 50000,
                    "database_username" : "db2inst1",
                    "database_password" : "Mobio123A",
                    "database_name" : "testdb"
                },
                "source_key" : "db2",
                "source_type" : "databases",
                "name" : " IBM DB2 5 ",
                "description" : "234324",
                "keywords" : "ibm db2 5,234324",
                "name_ascii" : "ibm db2 5",
                "log_connection_information" : {
                    "status_connect" : "success",
                    "information" : [ 
                        {
                            "name" : "Kết nối đến database",
                            "status" : "success"
                        }, 
                        {
                            "name" : "Authorization",
                            "status" : "success"
                        }, 
                        {
                            "name" : "Database",
                            "status" : "success"
                        }, 
                        {
                            "name" : "Permission",
                            "status" : "success"
                        }
                    ]
                },
                "data_type" : "data_in",
                "connector_identification" : "",
                "version" : 2,
                "config_attribute" : "profile",
                "config_mapping_data" : {
                    "fields" : [ 
                        {
                            "object" : "profiles",
                            "action" : "overwrite_and_ignore_value_None",
                            "field_source" : "default_value",
                            "field_source_type" : "string",
                            "value_type" : "fixed",
                            "value_by_type_fixed" : " IBM DB2 5",
                            "field_target" : "cif",
                            "display_type" : "multi_line",
                            "field_property" : 2,
                            "format" : None,
                            "format_value" : None
                        }, 
                        {
                            "object" : "profiles",
                            "action" : "overwrite_and_ignore_value_None",
                            "field_source" : "ID",
                            "field_source_type" : "INTEGER",
                            "field_source_schema_type" : "INTEGER",
                            "value_type" : "record",
                            "field_target" : "name",
                            "display_type" : "single_line",
                            "field_property" : 2,
                            "format" : None,
                            "format_value" : None
                        }, 
                        {
                            "object" : "profiles",
                            "action" : "overwrite_and_ignore_value_None",
                            "field_source" : "INT",
                            "field_source_type" : "INTEGER",
                            "field_source_schema_type" : "INTEGER",
                            "value_type" : "record",
                            "field_target" : "_dyn_multiple_line_text_chu_1725952396245",
                            "display_type" : "multi_line",
                            "field_property" : 2
                        }
                    ],
                    "table" : "A_P_HAPTT_1",
                    "schema" : "DB2INST1"
                },
                "config_rule_unification" : {
                    "consent" : {
                        "tracking_consent" : "Có",
                        "analytics_consent" : "Có",
                        "mkt_consent" : "Có"
                    },
                    "data_recording_rules" : {
                        "operators" : [ 
                            {
                                "priority" : 1,
                                "fields" : {
                                    "cif" : {
                                        "match_type" : "exact",
                                        "normalized_type" : "string"
                                    }
                                }
                            }
                        ]
                    },
                    "data_update_rules" : {
                        "operators" : [ 
                            {
                                "priority" : 1,
                                "fields" : {
                                    "cif" : {
                                        "match_type" : "exact",
                                        "normalized_type" : "string"
                                    }
                                }
                            }
                        ]
                    }
                },
                "config_sync_calendar" : {
                    "mode" : "snapshot",
                    "schedule" : {
                        "type" : "manually"
                    },
                    "auto_retry_w_error" : {
                        "status" : 0
                    }
                },
                "is_trust_source" : False,
                "is_type_sync_manually" : False,
                "object" : "profiles",
                "object_attribute" : "profile",
                "query_jq" : "{\n    \"profile_data\": {\n        \"cif\": \" IBM DB2 5\",\n        \"name\": .ID,\n        \"_dyn_multiple_line_text_chu_1725952396245\": .INT,\n    }\n}",
                "status_connect" : "off",
                "status_sync" : "stop",
                "reason_connect" : ""
            },
            "A_P_HAPTT_1"
        )
    )
