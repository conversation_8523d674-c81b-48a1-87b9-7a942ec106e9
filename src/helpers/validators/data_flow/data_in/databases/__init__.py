#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 06/07/2024
"""

import copy

from mobio.libs.logging import MobioLogging
from mobio.libs.validator import Each, In, InstanceOf, Length, Range, Required
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import LANG
from src.common.data_flow_constant import (
    ConstantActionMappingData,
    ConstantDisplayTypeData,
    ConstantModeConfigSyncCalendar,
    ConstantObjectHandle,
    ConstantParamApi,
    ConstantScheduleConfigType,
    ConstantScheduleType,
    ConstantSyncStartTime,
    ConstantTableType,
    ConstantUnificationStructure,
    ConstantValueTypeMappingData,
    ConstantDisplayTypeData,
    OptionSnapshot,
    SnapshotType,
)
from src.helpers.validators.data_flow.base import TemplateSettingValidator


class BaseDatabases(TemplateSettingValidator):
    def validate_config_connect(self, data_validate):
        MobioLogging().info("BaseDatabases :: validate_config_connect :: data_validate :: {}".format(data_validate))
        rule_validate_connect_config = {
            ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_HOST: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_PORT: [InstanceOf(int), Required],
            ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_NAME: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_USERNAME: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
            ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT_DATABASE_PASSWORD: [
                InstanceOf(str),
                Required,
                Length(1),
            ],
        }
        self.abort_if_validate_error(
            rule_validate_connect_config,
            data_validate[ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT],
        )

    def validate_create_connectors(self, data_validate):
        rule_base = self._validate_rule_param_base_create_connector()
        self.abort_if_validate_error(rule_base, data_validate)
        self.validate_config_connect(data_validate)

    def validate_check_connection(self, data_validate):
        self.validate_config_connect(data_validate)

    def validate_update_connectors(self, data_validate, detail_connector):
        object_attribute = detail_connector.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
        if not object_attribute:
            object_attribute = data_validate.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
        smooth_data = copy.deepcopy(data_validate)
        object_handled = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        is_trust_source = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE)
        self._base_validate_and_smooth_update_connectors_data_in_database(smooth_data)
        config_rule_unification = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_RULE_UNIFICATION)
        config_mapping_data = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
        config_sync_calendar = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)

        if config_mapping_data:
            self.validate_and_smooth_update_connectors_config_rule_mapping_data(config_mapping_data)
        if config_sync_calendar:
            smooth_data[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )
        if config_rule_unification:
            self.validate_and_smooth_update_connectors_config_rule_unification(
                config_rule_unification, object_handled, object_attribute
            )

        if is_trust_source:
            config_mapping_data = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
            if is_trust_source:
                list_field_verify = data_validate.get(ConstantParamApi.BodyUpdateConnectors.LIST_FIELD_VERIFY)
                is_mapping_field_verify = False
                config_mapping_data_fields = config_mapping_data.get("fields")
                for field in config_mapping_data_fields:
                    field_target = field.get("field_target")
                    if field_target in list_field_verify:
                        is_mapping_field_verify = True
                if not is_mapping_field_verify:
                    raise CustomError(self.lang.get(LANG.FIELD_VERIFY_NOT_MAPPING_ONE).get("message"))
        return smooth_data

    def validate_modified_content_connectors(self, data_validate, detail_connector):
        object_attribute = detail_connector.get(ConstantParamApi.BodyUpdateConnectors.OBJECT_ATTRIBUTE)
        smooth_data = copy.deepcopy(data_validate)
        is_trust_source = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.IS_TRUST_SOURCE)
        object_handled = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.OBJECT)
        rule_validate = {
            ConstantParamApi.BodyCreateConnectors.NAME: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.DESCRIPTION: [InstanceOf(str)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_KEY: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.SOURCE_TYPE: [InstanceOf(str), Length(1)],
            ConstantParamApi.BodyCreateConnectors.CONFIG_CONNECT: [
                InstanceOf(dict),
                Length(1),
            ],
        }
        self.validate_optional_err(rule_validate, data_validate)
        self.validate_config_connect(data_validate[ConstantParamApi.BodyDatabaseCheckConnection.CONFIG_CONNECT])

        self._base_validate_and_smooth_update_connectors_data_in_database(data_validate)
        config_rule_unification = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_RULE_UNIFICATION)
        config_mapping_data = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
        config_sync_calendar = smooth_data.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR)

        if config_mapping_data:
            self.validate_and_smooth_update_connectors_config_rule_mapping_data(config_mapping_data)
        if config_sync_calendar:
            smooth_data[ConstantParamApi.BodyUpdateConnectors.CONFIG_SYNC_CALENDAR] = (
                self.validate_and_smooth_update_connectors_config_sync_calendar(config_sync_calendar)
            )
        if config_rule_unification:
            self.validate_and_smooth_update_connectors_config_rule_unification(
                config_rule_unification, object_handled, object_attribute
            )

        if is_trust_source:
            config_mapping_data = data_validate.get(ConstantParamApi.BodyUpdateConnectors.CONFIG_MAPPING_DATA)
            if is_trust_source:
                list_field_verify = data_validate.get(ConstantParamApi.BodyUpdateConnectors.LIST_FIELD_VERIFY)
                is_mapping_field_verify = False
                config_mapping_data_fields = config_mapping_data.get("fields")
                for field in config_mapping_data_fields:
                    field_target = field.get("field_target")
                    if field_target in list_field_verify:
                        is_mapping_field_verify = True
                if not is_mapping_field_verify:
                    raise CustomError(self.lang.get(LANG.FIELD_VERIFY_NOT_MAPPING_ONE).get("message"))
        return smooth_data

    def validate_and_smooth_update_connectors_config_rule_unification(
        self, data_validate, object_handled, object_attribute
    ):
        rule_validate = {
            "data_recording_rules": [
                Required,
                InstanceOf(dict),
                Length(1),
            ],
            "data_update_rules": [
                Required,
                InstanceOf(dict),
                Length(1),
            ],
        }

        if (
            object_handled == ConstantObjectHandle.Object.PROFILES
            and object_attribute == ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE
        ):
            rule_validate.update(
                {
                    "consent": [
                        Required,
                        InstanceOf(dict),
                        Length(1),
                    ],
                }
            )
        if object_handled in [ConstantObjectHandle.Object.SALE] or object_attribute in [
            ConstantObjectHandle.ObjectAttribute.PROFILE_DYNAMIC_EVENT,
            ConstantObjectHandle.ObjectAttribute.PROFILE_PRODUCT_HOLING,
        ]:
            rule_validate["data_update_rules"] = [
                InstanceOf(dict),
            ]
            rule_validate["data_recording_rules"] = [
                InstanceOf(dict),
            ]

        self.abort_if_validate_error(rule_validate, data_validate)

        data_recording_rules = data_validate.get("data_recording_rules")
        data_update_rules = data_validate.get("data_update_rules")
        if data_recording_rules and (
            object_handled == ConstantObjectHandle.Object.PROFILES
            and object_attribute == ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE
        ):
            rule_validate_data_recording = {
                "operators": [
                    Required,
                    InstanceOf(list),
                    Length(1),
                    Each(
                        {
                            ConstantUnificationStructure.PRIORITY: [
                                Required,
                                InstanceOf(int),
                            ],
                            ConstantUnificationStructure.FIELDS: [
                                Required,
                                InstanceOf(dict),
                                Length(1),
                            ],
                        }
                    ),
                ],
            }
            self.abort_if_validate_error(rule_validate_data_recording, data_recording_rules)

        if data_update_rules and (
            object_handled == ConstantObjectHandle.Object.PROFILES
            and object_attribute == ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE
        ):
            rule_validate_data_update = {
                "operators": [
                    Required,
                    InstanceOf(list),
                    Length(1),
                    Each(
                        {
                            ConstantUnificationStructure.PRIORITY: [
                                Required,
                                InstanceOf(int),
                            ],
                            ConstantUnificationStructure.FIELDS: [
                                Required,
                                InstanceOf(dict),
                                Length(1),
                            ],
                        }
                    ),
                ],
            }
            self.abort_if_validate_error(rule_validate_data_update, data_update_rules)

        rule_validate_consent = {
            "analytics_consent": [
                InstanceOf(str),
                Length(1),
                In(["Có", "có", "Không", "không"]),
            ],
            "tracking_consent": [
                InstanceOf(str),
                Length(1),
                In(["Có", "có", "Không", "không"]),
            ],
            "mkt_consent": [
                InstanceOf(str),
                Length(1),
                In(["Có", "có", "Không", "không"]),
            ],
        }
        if (
            object_handled == ConstantObjectHandle.Object.PROFILES
            and object_attribute == ConstantObjectHandle.ObjectAttribute.PROFILE_ATTRIBUTE
        ):
            self.abort_if_validate_error(rule_validate_consent, data_validate["consent"])
        if data_recording_rules:

            operators_data_recording_rules = data_recording_rules["operators"]
            func_name = (
                "_validate_operators_{}_recording_rules".format(object_handled)
                if not object_attribute
                else "_validate_operators_{}_{}_recording_rules".format(object_handled, object_attribute)
            )
            try:
                getattr(
                    self,
                    func_name,
                )(operators_data_recording_rules)
            except Exception as ex:
                raise CustomError(ex)
        if data_update_rules:

            operators_data_update_rules = data_update_rules["operators"]
            func_name = (
                "_validate_operators_{}_recording_rules".format(object_handled)
                if not object_attribute
                else "_validate_operators_{}_{}_recording_rules".format(object_handled, object_attribute)
            )
            try:
                getattr(self, func_name)(operators_data_update_rules)
            except Exception:
                raise CustomError("Not exist validate operators, object {}".format(object_handled))

    def validate_and_smooth_update_connectors_config_sync_calendar(self, data_validate):
        rule_validate_config_sync_calendar = {
            "mode": [Required, InstanceOf(str), Length(1), In(ConstantModeConfigSyncCalendar.get_all_attribute())],
            "auto_retry_w_error": [Required, InstanceOf(dict), Length(1)],
            "schedule": [InstanceOf(dict)],
        }

        mode_sync = data_validate.get("mode")
        if mode_sync == ConstantModeConfigSyncCalendar.SNAPSHOT:
            rule_validate_config_sync_calendar["schedule"] = [Required, InstanceOf(dict), Length(1)]

        self.validate_optional_err(rule_validate_config_sync_calendar, data_validate)

        auto_retry_w_error = data_validate.get("auto_retry_w_error")
        rule_validate_auto_sync_w_error = {"status": [Required, InstanceOf(int), In([0, 1])]}
        status_auto_retry_w_error = auto_retry_w_error.get("status")
        if status_auto_retry_w_error == 1:
            rule_validate_auto_sync_w_error.update(
                {
                    "number_max_retry": [Required, InstanceOf(int), Range(1, 10)],
                    "retry_interval_sec": [
                        Required,
                        InstanceOf(int),
                        Range(start=900, end=86400),
                    ],
                }
            )

        self.validate_optional_err(rule_validate_auto_sync_w_error, auto_retry_w_error)
        if status_auto_retry_w_error == 0:
            if "number_max_retry" in auto_retry_w_error:
                auto_retry_w_error.pop("number_max_retry")
            if "retry_interval_sec" in auto_retry_w_error:
                auto_retry_w_error.pop("retry_interval_sec")
        data_validate["auto_retry_w_error"] = auto_retry_w_error
        schedule = data_validate.get("schedule", {})
        snapshot_config = data_validate.get("snapshot_config", {})
        if mode_sync == ConstantModeConfigSyncCalendar.STREAMING:
            schedule = {}
        if schedule and mode_sync == ConstantModeConfigSyncCalendar.SNAPSHOT:
            rule_validate_schedule = {
                "type": [Required, InstanceOf(str), Length(1), In(ConstantScheduleType.get_all_attribute())]
            }
            schedule_type = schedule.get("type")
            if schedule_type and schedule_type == ConstantScheduleType.INTERVAL:
                rule_validate_schedule["config"] = [Required, InstanceOf(dict), Length(1)]
            
            self.abort_if_validate_error(rule_validate_schedule, schedule)
            
            if snapshot_config:
                rule_validate_snapshot_config = {
                    "snapshot_type": [Required, InstanceOf(str), Length(1), In(SnapshotType.get_all_attribute())],
                    "checkpoint": [Required, InstanceOf(dict), Length(1)],
                    "partition": [InstanceOf(int), Range(1000, 20000)],
                    "is_use_custom_checkpoint": [Required, InstanceOf(bool)],
                    "is_edit_checkpoint": [Required, InstanceOf(bool)],
                }
                self.abort_if_validate_error(rule_validate_snapshot_config, snapshot_config)
                checkpoint = snapshot_config.get("checkpoint")
                rule_validate_checkpoint = {
                    "field_name": [Required, InstanceOf(str), Length(1)],
                    "field_type": [Required, InstanceOf(str), Length(1)],
                    "option": [InstanceOf(dict)]
                }
                self.abort_if_validate_error(rule_validate_checkpoint, checkpoint)
                option = checkpoint.get("option", {})
                if option:      
                    rule_validate_option = {
                        "option_type": [Required, InstanceOf(str), Length(1), In(OptionSnapshot.get_all_attribute())],
                        "checkpoint_value": [],
                        "updated_time": [],
                    }
                    self.abort_if_validate_error(rule_validate_option, option)
            if schedule_type == ConstantScheduleType.INTERVAL:
                schedule_config = schedule.get("config")
                rule_validate_schedule_config = {
                    # "hour": [Required, InstanceOf(str), Length(1)],
                    "type": [Required, InstanceOf(str), Length(1), In(ConstantScheduleConfigType.get_all_attribute())],
                }
                schedule_config_type = schedule_config.get("type")
                if schedule_config_type:
                    if schedule_config_type == ConstantScheduleConfigType.DAY:
                        rule_validate_schedule_config["hour"] = [Required, InstanceOf(str), Length(1)]
                        schedule_config["values"] = []
                        if "type_select_day_in_month" in schedule_config:
                            schedule_config.pop("type_select_day_in_month")
                    if schedule_config_type == ConstantScheduleConfigType.WEEK:
                        rule_validate_schedule_config["hour"] = [Required, InstanceOf(str), Length(1)]
                        if "type_select_day_in_month" in schedule_config:
                            schedule_config.pop("type_select_day_in_month")
                        rule_validate_schedule_config["values"] = [
                            Required,
                            InstanceOf(list),
                            Length(1),
                            Each(
                                [
                                    InstanceOf(str),
                                    Length(1),
                                    In(ConstantScheduleConfigType.DayInWeek.get_all_attribute()),
                                ]
                            ),
                        ]
                    if schedule_config_type == ConstantScheduleConfigType.MONTH:
                        rule_validate_schedule_config["hour"] = [Required, InstanceOf(str), Length(1)]
                        rule_validate_schedule_config["type_select_day_in_month"] = (
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In(ConstantScheduleConfigType.TypeSelectDayInMonth.get_all_attribute()),
                        )
                        type_select_day_in_month = schedule_config.get("type_select_day_in_month")
                        if type_select_day_in_month:
                            if type_select_day_in_month == ConstantScheduleConfigType.TypeSelectDayInMonth.EXACT_DAY:
                                rule_validate_schedule_config["values"] = [
                                    InstanceOf(list),
                                    Length(1, 31),
                                    Each([InstanceOf(int), In([i for i in range(1, 32)])]),
                                ]
                            if type_select_day_in_month == ConstantScheduleConfigType.TypeSelectDayInMonth.FLEX_DAY:
                                rule_validate_schedule_config["values"] = [
                                    InstanceOf(list),
                                    Length(1),
                                    Each(
                                        [
                                            InstanceOf(str),
                                            Length(1),
                                            In(
                                                ConstantScheduleConfigType.TypeSelectDayInMonth.ConstantValueDaySelectInMonth.get_all_attribute()
                                            ),
                                        ]
                                    ),
                                ]
                    if schedule_config_type == ConstantScheduleConfigType.HOUR:
                        rule_validate_schedule_config["value"] = []
                    if schedule_config_type == ConstantScheduleConfigType.MINUTE:
                        rule_validate_schedule_config["value"] = []
                    rule_validate_schedule_config["sync_start_time"] = [InstanceOf(str), Length(1), In(ConstantSyncStartTime.get_all_attribute())]
                    rule_validate_schedule_config["appointment_time"] = []
                self.validate_optional_err(rule_validate_schedule_config, schedule_config)
                schedule["config"] = schedule_config
            
        data_validate["schedule"] = schedule
        return data_validate

    def validate_and_smooth_update_connectors_config_rule_mapping_data(self, data_validate):
        rule_validate_config_mapping_data = {
            "table": [Required, InstanceOf(str), Length(1)],
            "fields": [
                Required,
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        "field_source": [Required, InstanceOf(str), Length(1)],
                        "field_source_type": [InstanceOf(str)],
                        "field_source_schema_type": [InstanceOf(str)],
                        "field_target": [Required, InstanceOf(str), Length(1)],
                        "field_target_type": [InstanceOf(str)],
                        "object": [Required, InstanceOf(str), Length(1)],
                        "value_type": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In(ConstantValueTypeMappingData.get_all_attribute()),
                        ],
                        "value_by_type_fixed": [
                            InstanceOf(str),
                        ],
                        "required": [InstanceOf(bool)],
                        "format_value": [],
                        "display_type": [],
                        "field_property": [Required, InstanceOf(int)],
                        "format": [],
                        "action": [
                            Required,
                            InstanceOf(str),
                            Length(1),
                            In(ConstantActionMappingData.get_all_attribute()),
                        ],
                        "group": [],
                    }
                ),
            ],
            "schema": [],
            "table_type": [InstanceOf(str), Length(1), In(ConstantTableType.get_all_attribute())],
        }
        self.abort_if_validate_error(rule_validate_config_mapping_data, data_validate)

        field_targets = []
        
        # Tìm max_group_index từ các trường đã có giá trị group
        groups = [field.get("group") for field in data_validate["fields"] if field.get("group") is not None]
        max_group_index = max(groups) if groups else 0
        
        for field in data_validate["fields"]:
            field_target = field.get("field_target")
            field_object = field.get("object")
            item = field_target + field_object

            # Phần này bỏ check field duplication theo yêu cầu 1. [IN] Cho phép chọn loại giấy tờ định danh để mapping với nguồn loại database (Daiichi)
            # if item in field_targets:
            #     raise CustomError("Field {} not duplicate mapping".format(field_target))
            
            # Kiểm tra display_type và action
            display_type = field.get("display_type")
            action = field.get("action")
            if (
                display_type not in ConstantDisplayTypeData.get_display_type_array() and "[*]" not in field_target
                and action == ConstantActionMappingData.ADD
            ):
                raise CustomError(self.lang.get(LANG.NOT_ALLOWED_ADD_ACTION).get("message"))
                
            # Tự động gán group cho field chưa có
            if field.get("group") is None:
                field["group"] = max_group_index + 1
                max_group_index += 1
                
            field_targets.append(item)
