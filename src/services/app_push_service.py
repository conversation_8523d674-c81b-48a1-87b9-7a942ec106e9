#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 15/06/2025
"""


import datetime
from urllib.parse import urljoin

from bson import ObjectId
from mobio.libs.logging import MobioLogging
from mobio.sdks.admin import MobioAdminSDK
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from src.common.app_push_constant import AppPushConstant, TypeSetupPushNotification
from src.common.json_encoder import J<PERSON><PERSON><PERSON><PERSON>
from src.common.utils import utf8_to_ascii
from src.models.data_history_model import DataHistoryModel
from src.models.file_upload_model import FileUploadModel, TypeFileUpload
from src.models.setup_push_notification_model import (
    SetupInformationPushNotificationModel,
)


class AppPushService:
    def __init__(self):
        self.logging = MobioLogging()

    def _normalizing_data_detail(self, request_data):
        if not request_data:
            return {}

        return JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(request_data)

    def _normalizing_data_history(self, request_data):
        if not request_data:
            return {}

        return JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(request_data)

    def _normalizing_data_file_upload(self, request_data):
        if not request_data:
            return {}

        return JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(request_data)

    def _insert_log_integrate_push_notification(
        self, application_id, merchant_id, account_id, before_data, after_data, type_action
    ):
        data_insert = {
            "application_push_id": ObjectId(application_id),
            "merchant_id": merchant_id,
            "account_id": account_id,
            "action_time": datetime.datetime.now(datetime.timezone.utc),
            "action": type_action,
            "before_data": before_data,
            "after_data": after_data,
            "type": TypeSetupPushNotification.APP_PUSH,
        }

        data_result = DataHistoryModel().insert_document(data_insert)

        return data_insert

    def _get_url_call_api_app_push(self, merchant_id):
        public_host = MobioAdminSDK().request_get_merchant_config_host(merchant_id=merchant_id, key="public-host")
        return urljoin(public_host, "guest")

    def get_list_applications_integrate_push_notification(
        self, merchant_id, account_id, order_by, order_type, per_page, before_token, name_search
    ):
        """
        Get list applications integrate push notification
        Args:
            merchant_id (str): Merchant ID
            account_id (str): Account ID
            order_by (str): Order by
            order_type (str): Order type
            per_page (int): Per page
            before_token (str): Before token
            name_search (str): Name search
        Returns:
            data_result (list): List of applications integrate push notification
            paging (dict): Paging
        """
        mapping_order_type = {
            "desc": -1,
            "asc": 1,
        }

        filter_option = {
            "merchant_id": merchant_id,
            "type": TypeSetupPushNotification.APP_PUSH,
        }

        if name_search:
            filter_option["search_ascii"] = {"$regex": utf8_to_ascii(name_search.lower()), "$options": "i"}

        data_result, after_token = SetupInformationPushNotificationModel().find_paginate_load_more(
            filter_option,
            per_page=per_page,
            after_token=before_token,
            sort_option=[(order_by, mapping_order_type.get(order_type))],
            projection=None,
        )

        data_result = [self._normalizing_data_detail(item) for item in data_result]

        return data_result, {
            "cursors": {
                "before": before_token,
                "after": after_token,
                "per_page": per_page,
            },
        }

    def _build_lst_screen_from_template_import_screen_create_app_push_notification(
        self, merchant_id, account_id, info_upload_screen
    ):
        if not info_upload_screen:
            return []
        local_path = info_upload_screen.get("local_path")
        if not local_path:
            raise Exception("File template import screen not found")
        import pandas as pd

        data_result = []
        df = pd.read_excel(local_path)
        # Strip whitespace from column names
        df.columns = df.columns.str.strip()
        for _, row in df.iterrows():
            # Strip whitespace from both ends of the values
            code = (
                str(row[AppPushConstant.Screen.ColumnName.CODE]).strip()
                if pd.notna(row[AppPushConstant.Screen.ColumnName.CODE])
                else ""
            )
            name = (
                str(row[AppPushConstant.Screen.ColumnName.NAME]).strip()
                if pd.notna(row[AppPushConstant.Screen.ColumnName.NAME])
                else ""
            )
            if not code or not name:
                continue
            if code in [item["code"] for item in data_result]:
                continue
            data_result.append({"code": code, "name": name})
        return data_result

    def create_application_integrate_push_notification(self, merchant_id, account_id, request_data):
        self.logging.info(f"Create application integrate push notification: {request_data}")

        name = request_data.get(AppPushConstant.FieldKeyBodyRequestCreateApplication.NAME)
        search_ascii = utf8_to_ascii(name.lower())

        info_upload_screen = request_data.get(AppPushConstant.FieldKeyBodyRequestCreateApplication.INFO_UPLOAD_SCREEN)
        lst_screen = self._build_lst_screen_from_template_import_screen_create_app_push_notification(
            merchant_id, account_id, info_upload_screen
        )

        data_insert = {
            "screens": lst_screen,
            "search_ascii": search_ascii,
            "merchant_id": merchant_id,
            "created_by": account_id,
            "updated_by": account_id,
            "created_time": datetime.datetime.now(datetime.timezone.utc),
            "updated_time": datetime.datetime.now(datetime.timezone.utc),
            "type": TypeSetupPushNotification.APP_PUSH,
            "url_call_api": self._get_url_call_api_app_push(merchant_id),
        }

        data_insert.update(request_data)

        data_result = SetupInformationPushNotificationModel().insert_document(data_insert)

        self._insert_log_integrate_push_notification(
            data_insert.get("_id"), merchant_id, account_id, {}, data_insert, AppPushConstant.TypeAction.CREATE
        )

        data_result = self._normalizing_data_detail(data_insert)

        return data_result

    def get_detail_config_application_integrate_push_notification(self, merchant_id, application_id):
        data_result = SetupInformationPushNotificationModel().find_one(
            {"_id": ObjectId(application_id), "type": TypeSetupPushNotification.APP_PUSH, "merchant_id": merchant_id},
        )

        return self._normalizing_data_detail(data_result)

    def update_application_integrate_push_notification(
        self, merchant_id, account_id, application_id, request_data, detail_data
    ):
        """
        Update application integrate push notification
        Args:
            merchant_id (str): Merchant ID
            account_id (str): Account ID
            application_id (str): Application ID
            request_data (dict): Request data
        Returns:
            status_update: True if update success, False if update failed
            data_result: Data result
            reason: Reason if update failed
        """

        # Lấy danh sách field có thể update
        list_field_update = [
            AppPushConstant.FieldKeyBodyRequestCreateApplication.NAME,
            AppPushConstant.FieldKeyBodyRequestCreateApplication.THUMBNAIL,
            AppPushConstant.FieldKeyBodyRequestCreateApplication.THUMBNAIL_TYPE,
            AppPushConstant.FieldKeyBodyRequestCreateApplication.PROGRAMMING_LANGUAGE,
            AppPushConstant.FieldKeyBodyRequestCreateApplication.CONFIG,
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_PUSH_NOTIFICATION_OMNI_CHANNEL,
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_TRACKING_EVENT_IN_APP,
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_PUSH_NOTIFICATION_ON_PAGE,
            AppPushConstant.FieldKeyBodyRequestCreateApplication.INFO_UPLOAD_SCREEN,
        ]

        data_update = {
            "updated_by": account_id,
            "updated_time": datetime.datetime.now(datetime.timezone.utc),
        }

        for field in list_field_update:
            if field in request_data:
                data_update[field] = request_data[field]
                if field == AppPushConstant.FieldKeyBodyRequestCreateApplication.INFO_UPLOAD_SCREEN:
                    data_update["screens"] = (
                        self._build_lst_screen_from_template_import_screen_update_app_push_notification(
                            merchant_id,
                            account_id,
                            request_data[field],
                            detail_data.get("info_upload_screen"),
                            detail_data.get("screens"),
                        )
                    )
                if field == AppPushConstant.FieldKeyBodyRequestCreateApplication.NAME:
                    data_update["search_ascii"] = utf8_to_ascii(request_data[field].lower())

        self.logging.info(f"Data update: {data_update}")

        data_result = SetupInformationPushNotificationModel().update_one_query(
            {"_id": ObjectId(application_id)}, data_update
        )

        self._insert_log_integrate_push_notification(
            application_id, merchant_id, account_id, data_result, data_update, AppPushConstant.TypeAction.UPDATE
        )

        detail_data_update = SetupInformationPushNotificationModel().find_one(
            {"_id": ObjectId(application_id), "type": TypeSetupPushNotification.APP_PUSH, "merchant_id": merchant_id},
        )

        return data_result == 1, self._normalizing_data_detail(detail_data_update), ""

    def _build_lst_screen_from_template_import_screen_update_app_push_notification(
        self, merchant_id, account_id, info_upload_screen_new, info_upload_screen_old, screens_old
    ):
        if not info_upload_screen_new:
            return []
        local_path_new = info_upload_screen_new.get("local_path")
        local_path_old = info_upload_screen_old.get("local_path") if info_upload_screen_old else None
        if local_path_new != local_path_old:
            import pandas as pd

            data_result = []
            df_new = pd.read_excel(local_path_new)
            # Strip whitespace from column names
            df_new.columns = df_new.columns.str.strip()
            for index, row in df_new.iterrows():
                # Strip whitespace from both ends of the values
                code = (
                    str(row[AppPushConstant.Screen.ColumnName.CODE]).strip()
                    if pd.notna(row[AppPushConstant.Screen.ColumnName.CODE])
                    else ""
                )
                name = (
                    str(row[AppPushConstant.Screen.ColumnName.NAME]).strip()
                    if pd.notna(row[AppPushConstant.Screen.ColumnName.NAME])
                    else ""
                )
                if not code or not name:
                    continue
                if code in [item["code"] for item in data_result]:
                    continue
                data_result.append({"code": code, "name": name})
            return data_result
        return screens_old

    def get_list_applications_integrate_push_notification_by_ids(
        self, merchant_id, account_id, application_integration_ids
    ):
        data_result = SetupInformationPushNotificationModel().find(
            {
                "_id": {"$in": [ObjectId(application_id) for application_id in application_integration_ids]},
                "type": TypeSetupPushNotification.APP_PUSH,
                "merchant_id": merchant_id,
            },
        )

        return [self._normalizing_data_detail(item) for item in data_result]

    def change_status_application_integrate_push_notification(
        self, merchant_id, account_id, application_id, request_data
    ):
        data_result = SetupInformationPushNotificationModel().find_one(
            {"_id": ObjectId(application_id), "type": TypeSetupPushNotification.APP_PUSH, "merchant_id": merchant_id},
        )

        if not data_result:
            return False, {}, "Application not found"

        data_update = {
            "updated_by": account_id,
            "updated_time": datetime.datetime.now(datetime.timezone.utc),
        }

        for field in request_data:
            if field in data_result:
                data_update[field] = request_data[field]

        data_result = SetupInformationPushNotificationModel().update_one_query(
            {"_id": ObjectId(application_id)}, data_update
        )

        self._insert_log_integrate_push_notification(
            application_id, merchant_id, account_id, data_result, data_update, AppPushConstant.TypeAction.UPDATE
        )

        detail_data = SetupInformationPushNotificationModel().find_one(
            {"_id": ObjectId(application_id), "type": TypeSetupPushNotification.APP_PUSH, "merchant_id": merchant_id},
        )

        return data_result == 1, self._normalizing_data_detail(detail_data), ""

    def get_list_applications_integrate_push_notification_by_filter_tracking(
        self, merchant_id, account_id, type_push_tracking, per_page, after_token_this_request
    ):

        filter_option = {
            "merchant_id": merchant_id,
            "type": TypeSetupPushNotification.APP_PUSH,
            "$or": [{"status_{}".format(item): {"$in": [AppPushConstant.Status.ON]}} for item in type_push_tracking],
        }

        data_result, after_token = SetupInformationPushNotificationModel().find_paginate_load_more(
            filter_option,
            per_page=per_page,
            after_token=after_token_this_request,
            sort_option=[("updated_time", -1)],
            projection=None,
        )

        return [self._normalizing_data_detail(item) for item in data_result], {
            "cursors": {
                "before": after_token_this_request,
                "after": after_token,
                "per_page": per_page,
            },
        }

    def get_action_history_integrate_push_notification(
        self, merchant_id, account_id, application_id, per_page, after_token_this_request
    ):
        filter_option = {
            "merchant_id": merchant_id,
            "type": TypeSetupPushNotification.APP_PUSH,
            "application_push_id": ObjectId(application_id),
        }

        data_result, after_token = DataHistoryModel().find_paginate_load_more(
            filter_option,
            per_page=per_page,
            after_token=after_token_this_request,
            sort_option=[("action_time", -1)],
            projection={
                "action_time": 1,
                "action": 1,
                "account_id": 1,
                "merchant_id": 1,
                "application_push_id": 1,
                "_id": 1,
            },
        )

        return [self._normalizing_data_history(item) for item in data_result], {
            "cursors": {
                "before": after_token_this_request,
                "after": after_token,
                "per_page": per_page,
            },
        }

    def upload_file(self, merchant_id, account_id, file, type_file):
        filename_origin = file.filename
        info_upload = MobioMediaSDK().upload_with_kafka(merchant_id=merchant_id, file_data=file, do_not_delete=True)
        self.logging.debug("upload_file :: info_upload %s " % info_upload)
        info_upload["filename"] = filename_origin
        data_insert = {
            "type": type_file,
            "created_by": account_id,
            "created_time": datetime.datetime.now(datetime.timezone.utc),
            "updated_by": account_id,
            "updated_time": datetime.datetime.now(datetime.timezone.utc),
            "merchant_id": merchant_id,
            "source": TypeSetupPushNotification.APP_PUSH,
        }
        data_insert.update(info_upload)

        data_result = FileUploadModel().insert_document(data_insert).inserted_id
        self.logging.info(f"Data insert: {data_result}")

        return self._normalizing_data_file_upload(data_insert)

    def get_template_import_screen(self, merchant_id, account_id):
        filter_option = {
            "type": TypeFileUpload.TEMPLATE_IMPORT_SCREEN,
            "merchant_id": {"$in": [merchant_id, "mobio"]},
        }
        data_result = FileUploadModel().find_one(filter_option)
        return self._normalizing_data_file_upload(data_result)
