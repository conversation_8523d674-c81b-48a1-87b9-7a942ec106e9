from mobio.libs.logging import MobioLogging

from src.helpers.confluent_kafka_base import ConfluentKafkaConsumer


class TestConsumer(ConfluentKafkaConsumer):

    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):

        MobioLogging().info("TestConsumer :: payload :: %s" % payload)
        # print(payload["name"])
        # print(payload["topic"])


if __name__ == "__main__":
    TestConsumer("am-test", "am-test")
