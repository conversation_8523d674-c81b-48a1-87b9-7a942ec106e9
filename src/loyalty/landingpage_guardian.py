#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: ThongNV
    Company: M O B I O
    Date Created: 03/06/2021
"""

# Landing Page Guardian

# ======================= Lấy mã OTP ==========================
# Version 1.0.2
# Version 1.0.1
# ---

# ------ Version 1.0.2 ---

"""
@api {post} /loyalty/api/v2.1/landing-page/get/otp  Lấy mã OTP
@apiDescription Lấy mã OTP bằng số điện thoại
@apiGroup LandingPage
@apiVersion 1.0.2
@apiName GetOtpByPhone

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant
@apiParam	   (Query:)			{String}	app_code    Mã phần mềm: <code>GUARDIAN_WEB</code>. (Client fix)

@apiParam	   (Body:)			{String}	phone_number    Số điện thoại
@apiParam	   (Body:)			{String}	[channel]         Kênh nhận OTP
                                                            <ul>
                                                                <li><code>SMS</code> : SMS (Default)</li>
                                                                <li><code>ZNS</code> : ZNS</li>
                                                            </ul>
                                                            
@apiParamExample {json} Body example
    {
        "phone_number": "0372528961",
        "channel": "SMS"
    }

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response Card Policy example
{
    "code": 200,
    "message": "request thành công"
}
"""

# ------ Version 1.0.1 ---

"""
@api {post} /loyalty/api/v2.1/landing-page/get/otp  Lấy mã OTP
@apiDescription Lấy mã OTP bằng số điện thoại
@apiGroup LandingPage
@apiVersion 1.0.1
@apiName GetOtpByPhone

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant
@apiParam	   (Query:)			{String}	app_code    Mã phần mềm: <code>GUARDIAN_WEB</code>. (Client fix)

@apiParam	   (Body:)			{String}	phone_number    Số điện thoại


@apiParamExample {json} Body example
    {
        "phone_number": "0372528961"
    }

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response Card Policy example
{
    "code": 200,
    "message": "request thành công"
}
"""


# ======================= Xác thực mã OTP ==========================

"""
@api {post} /loyalty/api/v2.1/landing-page/verify/otp  Xác thực mã OTP
@apiDescription Xác thực mã OTP 
@apiGroup LandingPage
@apiVersion 1.0.1
@apiName VerifyOTP

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant
@apiParam	   (Query:)			{String}	app_code    Mã phần mềm: <code>GUARDIAN_WEB</code>. (Client fix)

@apiParam	   (Body:)			{String}	phone_number    Số điện thoại
@apiParam	   (Body:)			{String}	code    Mã OTP


@apiParamExample {json} Body example
    {
        "phone_number": "0372528961",
        "code": "ABCBAA"
    }

@apiSuccess {Objects}   data      Thông tin đăng nhập
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	    merchant_id		ID merchant
@apiSuccess (data) {String} 	    token			Access token

@apiSuccessExample {json} Response example
{
    "data": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "profile_id":  "c264ac2b-e4cd-467f-b44e-e0d101e7c5e8",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhZGRyZXNzIjpudWxsLCJwb2ludCI6bnVsbCwiYmlydGhkYXkiOm51bGwsImVtYWlsIjpudWxsLCJnZW5kZXIiOjIsIm5hbWUiOiJUclx1MWVhN24gSHV5IFRpXHUxZWM3cCIsInBob25lX251bWJlciI6eyJsYXN0X3ZlcmlmeSI6IjIwMTktMDUtMTBUMDM6MTc6NTguMjkxWiIsInBob25lX251bWJlciI6Iis4NDM2MzMzNTAyMCIsInN0YXR1cyI6MH0sInByb2ZpbGVfaWQiOiJjMjY0YWMyYi1lNGNkLTQ2N2YtYjQ0ZS1lMGQxMDFlN2M1ZTgiLCJjcmVhdGVkX3RpbWUiOiIyMDE5LTAzLTMwVDAzOjQ2OjIwLjg5OFoiLCJ1cGRhdGVkX3RpbWUiOiIyMDE5LTA1LTEwVDAzOjE3OjU4LjI5OFoiLCJjYXJkIjp7ImNvZGUiOm51bGwsImNhcmRfbmFtZSI6bnVsbH0sInZvdWNoZXIiOjQsImlhdCI6MTU1ODQzNDg4MS45MzMyODQ4LCJtZXJjaGFudF9pZCI6IjFiOTliZGNmLWQ1ODItNGY0OS05NzE1LTFiNjFkZmZmMzkyNCJ9.ijWSSwXq0jabbQR524KR2XihXR6Nw9mU-FR4bd6zZPU"
    },
    "code": 200,
    "message": "request thành công"
}
"""

# ======= Đổi voucher ========

"""
@api {post} /loyalty/api/v2.1/landing-page/get/voucher   Lấy mã voucher
@apiVersion 1.0.0
@apiDescription Đổi voucher 
@apiGroup LandingPage
@apiName landingPageGetVoucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant
@apiParam	   (Query:)			{String}	app_code    Mã phần mềm: <code>GUARDIAN_WEB</code>. (Client fix)

@apiParam   (Body:)   {string} voucher_id        ID của voucher trên hệ thống MOBIO                                                 

@apiParamExample    {json}      Body example:
{
    "voucher_id": "f086ecf9-1ca9-4564-b79f-ccaa64ad2a4b"
}

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:) {Object} voucher  Thông tin mã voucher được nhận
@apiSuccess (data:) {string} voucher...code Mã code của voucher 
@apiSuccess (data:) {string} voucher...expiry_time Thời gian hết hạn của code 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "voucher": {
        "code": "KAY001",
        "expire_time" :"2017-08-11 12:00:00"
        }
    }
}
"""

# ========= Danh sách voucher của tôi =====
"""   
@api {get} /loyalty/api/v2.1/landing-page/vouchers/code/self  Danh sách mã voucher của tôi                                                                                           
@apiVersion 1.0.0              
@apiDescription Lấy danh sách mã voucher của profile       
@apiGroup LandingPage
@apiName landingPageVoucherCodeSelf

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant
@apiParam	   (Query:)			{String}	app_code    Mã phần mềm: <code>GUARDIAN_WEB</code>. (Client fix)

@apiSuccess {Array}   data  Nội dung trả về  
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:) {Object} code_info Thông tin mã code
@apiSuccess (code_info:) {string} code Mã code của voucher 
@apiSuccess (code_info:) {string} expire_time Thời gian hết hạn của code (Format: %Y-%m-%d %H:%M:%S)
@apiSuccess (code_info:) {int}    status Trạng thái sử dụng của code 
                                            <code>1</code> Đã sử dụng </br>
                                            <code>0</code> Chưa sử dụng </br>
@apiSuccess (code_info:) {string} created_time Thời gian cấp mã (Format: %Y-%m-%d %H:%M:%S)

@apiSuccess (data:)      {Objects}  voucher_info Thông tin voucher
@apiSuccess (voucher_info:) {string}  id ID của voucher 
@apiSuccess (voucher_info:) {string}  name Tên của voucher 
@apiSuccess (voucher_info:) {string}  avatar    Link ảnh avatar của voucher nếu có
@apiSuccess   (voucher_info:)  {string}   discount_type      <ul> Kiểu giảm giá:</ul> 
<li><b>PERCENT</b>-Phần trăm</li> 
<li><b>MONEY</b>-Giảm tiền</li>  
@apiSuccess   (voucher_info:)  {string}   discount_unit       <ul>Đơn vị của giá trị giảm giá:</ul>
<li><b> % </b>-Phần trăm </li>
<li><b>VND </b> Việt Nam đồng </li>
<li><b>USD </b></li>
@apiSuccess (voucher_info:) {int}  discount   Mức giảm giá của voucher 
@apiSuccess (voucher_info:) {int}  price Số diểm cần để đổi voucher 
@apiSuccess (voucher_info:) {number=-1-DEACTIVE 0-DRAFT 1-ACTIVE} status Trạng thái của voucher. <code>DEACTIVE: Ẩn</code>,
    <code>ACTIVE: Hiển thị</code>, <code>DRAFT: nháp</code>
@apiSuccess (voucher_info:) {string} [start_time] Thời gian bắt đầu  (Format: %Y-%m-%d %H:%M:%S)
@apiSuccess (voucher_info:) {string} [end_time]   Thời gian kết thúc (Format: %Y-%m-%d %H:%M:%S)

@apiSuccessExample {json} Response: HTTP/1.1 200 OK                                                                                             

{
    "code": 200,
    "data": [
        {
            "code_info": {
                "code": "8TYLTN",
                "created_time": "2021-04-02 03:02:21",
                "expire_time": "2021-10-03 08:22:01",
                "status": 1
            },
            "voucher_info": {
                "avatar": "https://mobiodiag.blob.core.windows.net/images/ea1b1cfa-9f0d-41de-ac5d-2f2d7f8aa782",
                "discount": 20,
                "discount_type": "PERCENT",
                "discount_unit": "%",
                "end_time": null,
                "id": "ea1b1cfa-9f0d-41de-ac5d-2f2d7f8aa782",
                "name": "Pink Rose Voucher",
                "price": 0,
                "start_time":  "2021-04-01 01:02:32",
                "status": 1
            }
        }
    ],
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 10,
        "total_count": 1,
        "total_page": 1
    }
}
"""

# ===== Thông tin điểm, hạng thẻ của profile ========

"""
@api {get} /loyalty/api/v2.1/landing-page/profile/loyalty-info Thông tin loyalty của profile
@apiName  LandingPageProfileLoyaltyInfo
@apiGroup LandingPage
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant
@apiParam	   (Query:)			{String}	app_code    Mã phần mềm: <code>GUARDIAN_WEB</code>. (Client fix)

@apiSuccess {Number} code       Response status
@apiSuccess {String} message    Response message
@apiSuccess {object} data       Thông tin loyalty của profile
@apiSuccess (data) {object}     card       Thông tin thẻ của profile
@apiSuccess (data) {string}     card.name       Tên hạng thẻ
@apiSuccess (data) {object}     point      Thông tin điểm của profile
@apiSuccess (data) {Number}     point.total      Tổng số điểm của profile

@apiSuccessExample Response success:
{
    "code": 200,
    "data": {
        "card": {
            "name": "thẻ kim cương 6/6"
        },
        "point": {
            "total": 0
        }
    },
    "message": "request thành công."
}

"""

# ======== Lịch sử thay đổi điểm =========
"""
@api {get} /loyalty/api/v2.1/landing-page/point/history Lịch sử thay đổi điểm
@apiName  LandingPagePointHistory
@apiGroup LandingPage
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant
@apiParam	   (Query:)			{String}	app_code    Mã phần mềm: <code>GUARDIAN_WEB</code>. (Client fix)


@apiSuccess {Number} code       Response status
@apiSuccess {String} message    Response message
@apiSuccess {Array}  data       Danh sách lịch sử thay đổi điểm
@apiSuccess (data) {string}     type        Loại
                                            <ul>
                                                <li><code>gift</code>: Tặng điểm</li>
                                                <li><code>check_in</code>: Check in tại cửa hàng</li>
                                                <li><code>buying</code>: Mua hàng</li>
                                                <li><code>returns</code>: Trả hàng</li>
                                                <li><code>rating</code>: Đánh giá giao dịch</li>
                                                <li><code>get_voucher</code>: Đổi voucher</li>
                                                <li><code>expired_point</code>: Hết hạn điểm</li>
                                                <li><code>transfer</code>: Chuyển nhượng điểm</li>
                                                <li><code>mini_game</code>: Tham gia mini game</li>
                                                <li><code>rate_posts</code>: Đánh giá bài viết</li>
                                                <li><code>update_card</code>: Thay đổi hạng thẻ</li>
                                                <li><code>introduce_friend</code>: Giới thiệu bạn bè</li>
                                                <li><code>first_login_app</code>: Đăng nhập lần đầu</li>
                                                <li><code>customer_birthday</code>: Tặng điểm sinh nhật</li>
                                                <li><code>no_transaction_in_period</code>: Không phát sinh giao dịch sau 1 khoảng thời gian</li>
                                            </ul>
@apiSuccess (data) {string}     channel    Kênh
@apiSuccess (data) {Number}     point      Số điểm thay đổi
@apiSuccess (data) {String}     action_time      Thời gian (UTC). (Format: <code>%Y-%m-%dT%H:%M:%SZ</code>)

@apiSuccessExample Response success:
{
    "code": 200,
    "data": [
        {
            "type": "buying",
            "channel": "Offline",
            "point": 2,
            "action_time": "2021-06-04T00:02:04Z"        
        }
    ],
    "message": "request thành công."
}

"""

# ===== Đăng xuất ====
# Version 1.0

"""
@api {post} /loyalty/api/v2.1/landing-page/logout Đăng xuất
@apiName  LandingPageLogout
@apiGroup LandingPage
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	   (Query:)			{String}	app_code    Mã phần mềm: <code>GUARDIAN_WEB</code>. (Client fix)


@apiSuccess {Number} code       Response status
@apiSuccess {String} message    Response message

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request thành công."
}

"""