#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 10/03/2022
"""
"""
@api {get} {domain}/ticket/api/v1.0/rule-config         L<PERSON><PERSON> danh sách các luật cấu hình                     
@apiDescription     L<PERSON>y danh sách các luật cấu hình
@apiGroup RuleConfig
@apiVersion 1.0.0
@apiName  GetRuleConfig
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse Response

@apiSuccess     {Array}     data            Danh sách các luật cấu hình
@apiSuccess     (data)      {String}        merchant_id     <code>ID</code> định danh tennat
@apiSuccess     (data)      {String}        code            Mã của luật cấu hình
                                                            <ul>
                                                                <li><code>not_assign_period_time</code> Khung giờ tạm dừng phân công</li>
                                                                <li><code>not_assign_day_off</code> Không phân công trong ngày nghỉ </li>
                                                                <li><code>staff_assign_default</code> Phân công cho thành viên mặc định</li>
                                                                <li><code>config_alert_over_threshold_wait_assign</code> Cấu hình thông báo khi vượt ngưỡng chờ phân công</li>
                                                                <li><code>not_assign_staffs_internal_team</code>Cấu hình không phân công cho nhân viên trong team</li>
                                                                <li><code>config_ticket_max_internal_team</code>Cấu hình số lượng ticket nhiều nhất mà thành viên trong team phụ trách</li>
                                                                <li><code>only_assign_staff_online_internal_team</code>Cấu hình chỉ phân công cho nhân viên đang online của team</li>
                                                            </ul>
@apiSuccess     (data)      {String}        apply_type      Áp dung cho team hay luật chung
                                                            <ul>
                                                                <li><code>internal_team</code>: phân công trong nội bộ team</li>
                                                                <li><code>merchant</code>: phân công của merchant</li>
                                                            </ul>
@apiSuccess     (data)      {String}        config_type     Loại cấu hình.
                                                            <ul>
                                                                <li><code>config_internal_team</code>: Loại cấu hình chung của nội bộ team</li>
                                                                <li><code>config_not_assign_period_time</code>: Loại không phân công trong 1 khoảng thời gian</li>
                                                                <li><code>config_assign_other</code>: Loại cấu hình phân công khác</li>
                                                            </ul>
@apiSuccess     (data)      {String}        description     Mô tả của luật


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "code": "not_assign_period_time",
            "apply_type": "merchant",
            "config_type": "config_not_assign_period_time",
            "description": "Khung thời gian tạm dừng phân công" 
        },
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "code": "not_assign_day_off",
            "apply_type": "merchant",
            "config_type": "config_not_assign_period_time",
            "description": "Không phân công trong ngày nghỉ" 
        },
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "code": "staff_assign_default",
            "apply_type": "merchant",
            "config_type": "config_assign_other",
            "description": "Cấu hình mặc định phân công khi không xác định được nhân viên phù hợp" 
        },
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "code": "config_alert_over_threshold_wait_assign",
            "apply_type": "merchant",
            "config_type": "config_assign_other",
            "description": "Cấu hình thông báo khi nhiều ticket chưa được phân công" 
        },
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "code": "not_assign_staffs_internal_team",
            "apply_type": "internal_team",
            "config_type": "config_internal_team",
            "description": "Không phân công cho thành viên chỉ định" 
        },
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "code": "config_ticket_max_internal_team",
            "apply_type": "internal_team",
            "config_type": "config_internal_team",
            "description": "Số lượng ticket tối đa 01 thành viên xử lý" 
        },
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "code": "only_assign_staff_online_internal_team",
            "apply_type": "internal_team",
            "config_type": "config_internal_team",
            "description": "Chỉ phân công cho thành viên đang đăng nhập." 
        }
    ]
}

"""
"""
@api {get} {domain}/ticket/api/v1.0/rule-config/holidays         Lấy danh sách ngày nghỉ của luật cấu hình
@apiDescription      Lấy danh sách ngày nghỉ của luật cấu hình
@apiGroup RuleConfig
@apiVersion 1.0.0
@apiName  GetHolidaysRuleConfig
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse Response

@apiSuccess     {Array}     data                Danh sách các luật cấu hình
@apiSuccess     (data)  {String}    code        Mã của ngày nghỉ tương ứng
@apiSuccess     (data)  {Object}    name        Tên của ngày nghỉ
@apiSuccess     (data)  {String}    name.lang   Ngôn ngữ 

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "code": "day_new_year",
            "name": {
                "vi": "Tết dương lịch",
                "en": ""
            }
        },
        {
            "code": "29th_lunar_new_year",
            "name": {
                "vi": "Ngày 29 tết âm lịch",
                "en": ""
            }
        },
        {
            "code": "30th_lunar_new_year",
            "name": {
                "vi": "Ngày 30 tết âm lịch",
                "en": ""
            }
        },
        {
            "code": "1st_lunar_new_year",
            "name": {
                "vi": "Mùng 1 tết âm lịch",
                "en": ""
            }
        },
        {
            "code": "2nd_lunar_new_year",
            "name": {
                "vi": "Mùng 2 tết âm lịch",
                "en": ""
            }
        },
        {
            "code": "3rd_lunar_new_year",
            "name": {
                "vi": "Mùng 3 tết âm lịch",
                "en": ""
            }
        },
        {
            "code": "liberation_day",
            "name": {
                "vi": "Ngày Giải phóng miền Nam(30/4)",
                "en": ""
            }
        },
        {
            "code": "international_labor_day",
            "name": {
                "vi": "Ngày quốc tế lao động",
                "en": ""
            }
        },
        {
            "code": "independence_day",
            "name": {
                "vi": "Ngày Quốc Khánh",
                "en": ""
            }
        },
    ]
}

"""
