#!/usr/bin/python
# -*- coding: utf8 -*-

************************************************** API LẤY DANH SÁCH MODULE THEO MERCHANT **************************************************
* version: 2.1.0                                                                                                                           *
********************************************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/modules Lấy danh sách module theo merchant
@apiDescription Lấy danh sách module theo merchant
@apiVersion 2.1.0
@apiGroup Owner
@apiName Get

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort

@apiParam  (Query:) {String}  merchant_id 	Merchant id cuar merchant cần lấy module
@apiSuccess  {string} merchant_id ID nhãn hàng
@apiSuccess (data) {string} module_id ID module được gán cho nhãn hàng
@apiSuccess (data) {string} name Tên module được gán cho nhãn hàng
@apiSuccess (data) {string} description Mô tả module
@apiSuccess (data) {boolean} is_checked Trạng thái được module được gán cho merchant
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "a83269d2-9a9f-403c-8afe-a9d3cb4fb1f4",
  "data": [
    {
      "module_id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
      "name": "Quản lý nội dung",
      "description": "Quản lý nội dung chương trình",
      "is_checked": true
    },
    ...
  ],
  "lang": "vi",
  "sort": "name",
  "order": "asc",
  "paging": {
    ...
  }
}
"""

************************************************** API GÁN MODULE CHO MERCHANT **************************************************
* version: 2.1.0                                                                                                                *
*********************************************************************************************************************************
"""
@api {put} /api/v2.1/merchants/modules Gán module cho merchant
@apiDescription Gán module cho merchant
@apiVersion 2.1.0
@apiGroup Owner
@apiName Put

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam  (Query:) {String}  merchant_id 	Merchant id cuar merchant cần lấy module
@apiParam  {Array} module_ids Danh sách module id muốn gán.
@apiParamExample {json} Body
{
    "module_ids": [
        "26ebae9c-ec2e-47c4-ba09-4921f3b512e9",
        "e5de4b87-05e4-4111-8ccc-134f6e57c692",
        "8086bbd2-0ee8-4f90-b4b8-7f3f067d5527"
    ]
}

@apiSuccess  {Array} module_ids Danh sách module id được gán thành công.
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "module_ids": [
        "26ebae9c-ec2e-47c4-ba09-4921f3b512e9",
        "e5de4b87-05e4-4111-8ccc-134f6e57c692",
        "8086bbd2-0ee8-4f90-b4b8-7f3f067d5527"
    ]
}
"""


******************************* API XOÁ DANH SÁCH NHÂN VIÊN *******************************
* version: 2.1.0                                                                *
*********************************************************************************
"""
@api {delete} /api/v2.1/merchants/accounts/actions/delete Xoá danh sách nhân viên
@apiDescription Xoá danh sách nhân viên 
@apiVersion 2.1.0
@apiGroup Owner
@apiName Delete

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)   {String}  merchant_id   merchant_id của tenant
@apiParam   (Query:)  {String}  staff_ids   danh sách các nhân viên cần xóa <code>staff_ids:aijwdiawdj,kawikdowks2093</code>

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
   "message": "delete successful!!!"
}
"""

# ***********************************************************************************************************************
# ************************************************** UPDATE NHÂN VIÊN **************************************************
# ***********************************************************************************************************************
"""
@api {put} /api/v2.1/merchants/accounts Update nhân viên
@apiDescription Update nhân viên. Dịch vụ gửi lên request dạng <code>form-data</code>  
@apiVersion 2.1.0
@apiGroup Owner
@apiName put

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Query:)   {String} account_id                          	id nhân viên cần update 
@apiParam      (Query:)   {String} merchant_id                          id của merchant
@apiParam      (Body:)     {File}      avatar                          	Ảnh đại diện
@apiParam      (Body:)     {String}    info                            Thông tin  sản phẩm, String object json


@apiParam (info) {string} password         Mật khẩu
@apiParam (info) {string} fullname         Tên đầu đủ
@apiParam (info) {string} phone_number     Số điện thoại
@apiParam (info) {string} email            Email của tài khoản
@apiParam (info) {string} [module_ids]     Mảng các module_id:


@apiParamExample {json} Info example
{
  "fullname": "Nguyễn Hữu Lộc",
  "phone_number": "**********",
  "module_ids":["18a07df3-5fdc-41c4-9bc9-00cee694c68a"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be",
  "id": "cbce917d-f9f8-4088-bcad-da01975d3163",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "modules":[
          {
                "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                "name": "Chăm sóc khách hàng",
                "description": ""
          },
          ...
  ]
}

"""
# **********************************************************************************************************************************
# ************************************************** API LẤY DANH SÁCH NHÂN VIÊN  **************************************************
# **********************************************************************************************************************************
"""
@api {Get} /api/v2.1/merchants/accounts Lấy danh sách nhân viên 
@apiDescription Lấy danh sách nhân viên của 1 nhãn hàng
@apiVersion 2.1.0
@apiGroup Owner
@apiName Get

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search

@apiParam (Query:)     {String} merchant_id   merchant_id của tenant
@apiParam (Query:)     {int}    [is_online]    Trạng thái đăng nhập <code> 1- online, 0- offline</code>
@apiParam (Query:)     {int}    [role_group]   Nhóm quyền <code> 1- owner, 2- admin, 3- manager, 4- user</code>
@apiParam (Query:)     {string}    [day_offline]   Ngày không hoạt động <code> "equal", "higher_equal", "higher", "between", "lower", "lower_equal"</code>
@apiParam (Query:)     {string}    [day_value_start]   Dùng cho bettween thời gian bắt đầu etc: "2017-08-07T04:02:28.002Z"
@apiParam (Query:)     {string}    [day_value_end]   Dùng cho bettween thời gian kết thúc etc: "2017-08-07T04:02:28.002Z"
@apiParam (Query:)     {string}    [day_value]   dùng để lọc ngày offline etc: "2017-08-07T04:02:28.002Z"

@apiSuccess {string} merchant_id        UUID sub-brand
@apiSuccess {object} data               Danh sách accounts

@apiSuccess (data) {string} id          ID tài khoản nhân viên
@apiSuccess (data) {string} username    Tên truy cập nhân viên
@apiSuccess (data) {string} fullname    Tên dầy đủ nhân viên
@apiSuccess (data) {string} avatar      Ảnh đại điện nhân viên
@apiSuccess (data) {string} phone_number        Số điện thoại nhân viên
@apiSuccess (data) {string} email               Thư điện tử nhân viên
@apiSuccess (data) {string} created_account     Người tạo 
@apiSuccess (data) {int} status         Trạng thái 
@apiSuccess (data) {int} is_online       Trạng thái đăng nhập <code> 1- online, 0- offline</code>

@apiSuccess (data) {Object}         modules     Danh sách nhóm quyền

@apiSuccess (modules)    {string}       id               ID nhóm quyền
@apiSuccess (modules)    {string}       name             Tên nhóm quyền
@apiSuccess (modules)    {string}       description      Mô tả nhóm quyền

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "data": [
    {
      "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
      "username": "Locnh",
      "fullname": "Nguyễn Hữu Lộc",
      "avatar": "",
      "phone_number": "",
      "email": "<EMAIL>",
      "create_time": "2017-08-07T04:02:28.002Z",
      "update_time": "2017-08-07T04:02:28.002Z",
      "created_account": "admin@mobio",
      "is_offline": 1,
      "role_group": 1,
      "modules":[
          {
                "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                "name": "Chăm sóc khách hàng",
                "description": ""
          },
          ...
      ]
    },
    ...
  ],
  "lang": "vi",
  "paging": {
    ...
  }
}

"""

******************************* API LẤY CHI TIẾT NHÂN VIÊN *******************************
* version: 2.1.0
******************************************************************************************
"""
@api {get} /api/v2.1/merchants/accounts/detail Lấy chi tiết nhân viên
@apiDescription Lấy thông tin chi tiết nhân viên thuộc nhãn hàng
@apiVersion 2.1.0
@apiGroup Owner
@apiName GetDetailAccount

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Query:)   {String} account_id                          	id nhân viên cần update 
@apiParam      (Query:)   {String} merchant_id                          id của merchant

@apiSuccess  {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess  {String} id ID tài khoản nhân viên
@apiSuccess  {String} username Tên truy cập nhân viên
@apiSuccess  {String} fullname Tên dầy đủ nhân viên
@apiSuccess  {String} avatar Ảnh đại điện nhân viên
@apiSuccess  {String} phone_number Số điện thoại nhân viên
@apiSuccess  {String} email Thư điện tử nhân viên
@apiUse created_time
@apiUse updated_time
@apiSuccess  {String} created_account Tên truy cập tài khoản tạo account nhân viên
@apiSuccess  {String} full_name_created_account Tên dầy đủ nhân viên tạo account nhân viên
@apiSuccess  {String} full_name_updated_account Tên dầy đủ nhân viên sửa account nhân viên
@apiSuccess  {number=1:admin 2:normal} is_admin Phân biệt tài khoản quản trị của nhãn hàng với tài khoản thường
@apiSuccess  {number=1:Mobio 2:others} is_mobio Phân biệt tài khoản của Mobio với các nhãn hàng khác
@apiSuccess  {String} [push_id] Mã app mobile dùng để push notify đến app
@apiSuccess  {String} [push_operating_system] Hệ điều hành của app mobile
@apiSuccess  {Array} master_campaign Master campaign của nhân viên
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "is_admin": 1,
  "is_mobio": 1,
  "push_id": "KLAJSIOWN!O()@@LKLASJSOI!)@KSLA",
  "push_operating_system": "IOS",
  "master_campaign":[
  	{
		"id": "0015ba02-2cf9-4ee9-8ec0-6a1829bfeb0e",
		"name": "Nhà hàng Lã Vọng"
  	}
  ],
  "full_name_created_account": "Nam Anh (admin@mobioshop)",
  "full_name_updated_account": "Nam Anh (admin@mobioshop)"
}
"""


************************* LẤY DANH SÁCH MODULE *****************************
* version: 2.1.0                                                           *
****************************************************************************
"""
@api {get} /api/v2.1/merchants/modules Lấy danh sách module
@apiDescription Lấy danh sách tất cả module
@apiVersion 2.1.0
@apiGroup Owner
@apiName Get

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search
@apiParam (Query:)    {string}       [type]         Loại quyền <code>'created'- Tự tạo, 'default'- Mặc định </code>
@apiParam (Query:)    {string}       [account]      <code>1- trả về thông tin của người có quyền 2- mặc định không trả về </code>
@apiParam (Query:)    {string}       [sub_brand_id]      <code>merchant id của sub_brand</code>
@apiSuccess (data) {string} id Module id
@apiSuccess (data) {string} name Tên module
@apiSuccess (data) {string} description Mô tả module
@apiSuccess (data)    {Array}        accounts         Danh sách accounts

@apiSuccess (accounts)    {string}           id                 ID accounts
@apiSuccess (accounts)    {string}           username           username tên đăng nhập  của accounts
@apiSuccess (accounts)    {string}           avatar             Ảnh đại diện của accounts
@apiSuccess (accounts)    {string}           fullname           Tên đầy đủ của accounts
@apiSuccess (accounts)    {string}           phone_number       Số điện thoại của accounts
@apiSuccess (accounts)    {string}           email              Email của accounts

@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
      "name": "Quản lý nội dung",
      "description": "Quản lý nội dung chương trình",
      "create_time": "2017-08-07T04:02:28.002Z",
      "type": "created",
      "update_time": "2017-08-07T04:02:d8.002Z",
      "accounts": [
            {
                  "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                  "username": "Locnh",
                  "fullname": "Nguyễn Hữu Lộc",
                  "avatar": "",
                  "phone_number": "",
                  "email": "<EMAIL>"
            },
            ...
        ]
    },
    ...
  ],
  "lang": "vi",
  "sort": "name",
  "order": "asc",
  "paging": {
    ...
  }
}
"""

************************* API SỬA MODULE cho sub_brand************
* version: 2.1.0                                                 *
******************************************************************

# **********************************************************************************************************************************
"""
@api{put} /api/v2.1/merchants/modules/actions/update  Update nhóm quyền  cho subrand
@apiDescription Update nhóm quyền subrand
@apiVersion 2.1.0
@apiGroup Owner
@apiName PutModule

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam      (Query:)   {String} module_id                           id của module 
@apiParam      (Query:)   {String} sub_brand_id                        id của sub_brand
@apiParam      (Body:)     {String}    name                 Tên nhóm quyền 
@apiParam      (Body:)     {String}    description          Mô tả nhóm quyên
@apiParam      (Body:)     {Array}     functions            Danh sách chức năng 
@apiParam      (Body:)     {number}    [is_all_function]    Chọn tất cả các quyền. <code>0: Không</code>, <code>1: Chọn tất cả</code>  

@apiParam      (Functions:)     {String}    function_id     uuid chức năng 
@apiParam      (Functions:)     {Array}     action_ids      Danh sách uuid action

@apiParamExample {json} Body example
{   
  "name": "Chăm sóc khách hàng",
  "description": "",
  "is_all_function": 1,
  "functions": [
      {
          "function_id": "c7229324-ffe3-4090-8acb-27093003f703",
          "action_ids": ["c7229324-ffe3-4090-8acb-27093003f703", "c7229324-ffe3-4090-8acb-27093003f703"]
      }
  ]

}
"""

******************************* Lấy D/S chức năng *******************************
* version: 2.1.0                                                                *
*********************************************************************************
"""
@api {get} /api/v2.1/merchants/functions Lấy danh sách chức năng
@apiDescription Lấy danh sách chức năng của hệ thống.
<li>Có hỗ trợ sắp xếp theo: <code>tên, đường dẫn</code>;</li>
<li>Có hỗ trợ tìm kiếm theo tên chức năng;</li>
<li>Có hỗ trợ phân trang;</li>
@apiGroup Owner
@apiVersion 2.1.0
@apiName GetFuncs

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse lang_success
@apiUse paging
@apiUse search
@apiUse order_sort
@apiParam    (Query:)  {int:}    [group_by_parent]                   Sắp xếp nhóm chức năng theo parent <code>1: sắp xếp , 2: mặc định không sắp xếp</code>
@apiSuccess     {Function[]}    data                        Danh sách chức năng của hệ thống.

@apiSuccess    (Function)     {String}        id                     Id của chức năng.
@apiSuccess    (Function)     {String}        name                   Tên của chức năng.
@apiSuccess    (Function)     {String}        description            Mô tả của chức năng.
@apiSuccess    (Function)     {String}        path                   Đường dẫn đến chức năng. Dành riêng cho client là kiểu WebCRM.
@apiSuccess    (Function)     {String}        parent                 Xâu theo format để quy định vị trí của chức năng trong menu.
@apiSuccess    (Function)     {String}        [icon_name]            Tên icon đại diện cho chức năng. Tên icon sử dụng theo kiểu font. Ví dụ: <code>fas fa-edit</code> (Font Awesome Icons)
@apiSuccess    (Function)     {Number}        [type]                 Xác định function sẽ có hiệu lực trên ứng dụng nào.
                                                                    <li><code>1-MerchantCRM</code>: function được phép chạy trên ứng dụng Merchant CRM;</li>
                                                                    <li><code>2-AdminCRM</code>: function được phép chạy trên ứng dụng Admin CRM;</li>
                                                                    <li><code>3-PartnerMobileApp</code>: function được phép chạy trên ứng dụng mobile Partner;</li>
@apiUse actions_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "id": "7a810df9-7be5-486f-bd4e-1d0d825d593d",
            "name": "Quản lý cửa hàng",
            "description": "Chức năng quản lý danh sách cửa hàng.",
            "path":"customer-care/content/shop",
            "parent":"CSKH::QLND",
            "icon_name":"fas fa-home",
            "type":1,
            "create_time": "2017-08-07T04:02:28.002Z",
            "update_time": "2017-08-07T04:02:28.002Z",
            "actions": [
                {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
                {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Sửa item"
                },
                {
                    "id": 3,
                    "name_vi": "Xoá",
                    "name_en": "Delete",
                    "description": "Xoá item"
                }
            ]
        },
        {
            "id": "d699b141-375e-4235-b91f-17850b806d03",
            "name": "Quản lý sản phẩm",
            "description": "Chức năng quản lý danh sách sản phẩm.",
            "path":"customer-care/content/product",
            "parent":"CSKH::QLND",
            "icon_name":"fas fa-home",
            "type":1,
            "create_time": "2017-08-07T04:02:28.002Z",
            "update_time": "2017-08-07T04:02:28.002Z",
            "actions": [
                {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
                {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Sửa item"
                },
                {
                    "id": 3,
                    "name_vi": "Xoá",
                    "name_en": "Delete",
                    "description": "Xoá item"
                }
            ]
        }
    ],
    "sort":"name_vi",
    "order":"asc",
    "paging": {
    ...
    }
}
@apiSuccessExample     {json}    group_by_parent
{
    "data":
        [
            {
                "parent":"CSKH::QLND",
                "data_group":[
                    {
                        "id": "7a810df9-7be5-486f-bd4e-1d0d825d593d",
                        "name": "Quản lý cửa hàng",
                        "description": "Chức năng quản lý danh sách cửa hàng.",
                        "path":"customer-care/content/shop",
                        "parent":"CSKH::QLND",
                        "icon_name":"fas fa-home",
                        "type":1,
                        "create_time": "2017-08-07T04:02:28.002Z",
                        "update_time": "2017-08-07T04:02:28.002Z",
                        "actions": [
                            {
                                "id": 1,
                                "name_vi": "Thêm",
                                "name_en": "Add",
                                "description": "Thêm item"
                            },
                            {
                                "id": 2,
                                "name_vi": "Sửa",
                                "name_en": "Edit",
                                "description": "Sửa item"
                            },
                            {
                                "id": 3,
                                "name_vi": "Xoá",
                                "name_en": "Delete",
                                "description": "Xoá item"
                            }
                        ]
                    },
                    {
                        "id": "d699b141-375e-4235-b91f-17850b806d03",
                        "name": "Quản lý sản phẩm",
                        "description": "Chức năng quản lý danh sách sản phẩm.",
                        "path":"customer-care/content/product",
                        "parent":"CSKH::QLND",
                        "icon_name":"fas fa-home",
                        "type":1,
                        "create_time": "2017-08-07T04:02:28.002Z",
                        "update_time": "2017-08-07T04:02:28.002Z",
                        "actions": [
                            {
                                "id": 1,
                                "name_vi": "Thêm",
                                "name_en": "Add",
                                "description": "Thêm item"
                            },
                            {
                                "id": 2,
                                "name_vi": "Sửa",
                                "name_en": "Edit",
                                "description": "Sửa item"
                            },
                            {
                                "id": 3,
                                "name_vi": "Xoá",
                                "name_en": "Delete",
                                "description": "Xoá item"
                            }
                        ]
                    }
                ]
            }
        ......
        ]
}
"""

************************* API XOÁ MODULE *************************
* version: 2.1.0                                                 *
******************************************************************
"""
@api {delete} /api/v2.1/merchants/modules Xóa nhóm quyền cho subrand
@apiDescription Xóa nhóm quyền cho subrand
@apiVersion 2.1.0
@apiGroup Owner
@apiName DeleteModule
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Query:)     {String}    sub_brand_id      Merchant_id của sub_brand merchant
@apiParam      (Query:)     {String}    ids      Tập hợp các UUID của nhóm quyền.

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
  "ids_module": ["597281cb-03a5-437c-a4c1-95a6b5289af6",
          "2e673fa2-c292-4ea4-aca2-3a378234926e"]
}

"""

************************* API TẠO MỘT MODULE cho sub_brands **********
* version: 2.1.0                                                     *
**********************************************************************
"""
@api{post} /api/v2.1/merchants/modules Tạo nhóm quyền cho sub brands
@apiDescription  Tạo nhóm quyền cho sub brands
@apiVersion 2.1.0
@apiGroup Owner
@apiName PostModule

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}    name                 Tên nhóm quyền 
@apiParam      (Body:)     {String}    description          Mô tả nhóm quyên
@apiParam      (Body:)     {Array}     functions            Danh sách chức năng 
@apiParam      (Body:)     {number}    [is_all_function]    Chọn tất cả các quyền. <code>0: Không</code>, <code>1: Chọn tất cả</code>  
@apiSuccess    (Body:)     {String}       [type]           Loại quyền <code>'created'- Tự tạo, 'default'- Mặc định (không truyền lên lấy mặc định)</code>
@apiParam      (Functions:)     {String}    function_id     uuid chức năng 
@apiParam      (Functions:)     {Array}     action_ids      Danh sách uuid action

@apiParamExample {json} Body example
{   
  "merchant_id": "c7229324-ffe3-4090-8acb-27093003f703",
  "name": "Chăm sóc khách hàng",
  "description": "",
  "is_all_function": 1,
  "type": "created",
  "functions": [
      {
          "function_id": "c7229324-ffe3-4090-8acb-27093003f703",
          "action_ids": ["c7229324-ffe3-4090-8acb-27093003f703", "c7229324-ffe3-4090-8acb-27093003f703"]
      }
  ]
}
"""
************************* API Lấy pattern merchant  **********
* version: 2.1.0                                                     *
**********************************************************************

"""
@api {get} /api/v2.1/merchants/sub/actions/pattern/user Lấy pattern của merchant
@apiDescription Lấy pattern của merchant
@apiVersion 2.1.0
@apiGroup Owner
@apiName GetPatternOwner

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Query:)     {String}    merchant_id                     id merchant
@apiSuccess      (data)     {Object}    pattern_password                pattern của mật khẩu merchant (đã được encode base 64 'utf-8')
@apiSuccess      (data)     {Object}    pattern                         pattern của username merchant (đã được encode base 64 'utf-8')
@apiSuccess      (data)     {Object}    password                         policy của patter mật khẩu 
@apiSuccess      (data)     {Object}    username                         policy của patter username
  
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "password": {
      "contain": {
        "key": "least",
        "number": 2,
        "symbol": 3,
        "uppercase": 1,
        "value": "2"
      },
      "length_max": "15",
      "length_min": "4"
    },
    "pattern_password": "Xig/PSg/Oi4qW0EtWl0uKil7MSx9KSg/PSg/Oi4qWzAtOV0uKil7Mix9KSguezAsMTV9JCl8KD89KD86LipbQS1aXS4qKXsxLH0pKD89KD86LipbXmEtekEtWjAtOV0uKil7Myx9KSguezAsMTV9JCl8KD89KD86LipbMC05XS4qKXsyLH0pKD89KD86LipbXmEtekEtWjAtOV0uKil7Myx9KSguezAsMTV9JCkuKiQ=",
    "pattern_username": "Xig/PSg/Oi4qW0EtWl0uKil7MSx9KSg/PSg/Oi4qW2Etel0uKil7MSx9KSg/PSg/Oi4qWzAtOV0uKil7MSx9KSguezAsMjd9JCkuKiQ=",
    "username": {
      "contain": {
        "key": "least",
        "lowercase": 1,
        "number": 1,
        "uppercase": 1,
        "value": "3"
      },
      "length_max": "27",
      "length_min": "4"
    }
  },
  "lang": "vi",
  "message": "request thành công."
}


"""

************************* API LẤY DANH SÁCH MODULE DETAIL*************************
* version: 2.1.0                                                           *
****************************************************************************
"""
@api {get} /api/v2.1//merchants/modules/list Lấy danh sách module chi tiết
@apiDescription Lấy danh sách tất cả module chi tiết 
@apiVersion 2.1.0
@apiGroup Owner
@apiName ModuleLListDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search
@apiParam (Query:)    {string}       [type]         Loại quyền <code>'created'- Tự tạo, 'default'- Mặc định </code>
@apiParam (Query:)    {string}       [account]      <code>1- trả về thông tin của người có quyền 2- mặc định không trả về </code>
@apiParam (Query:)    {string}       sub_brand_id      <code>merchant id của sub_brand</code>
@apiSuccess (data) {string} id Module id
@apiSuccess (data) {string} name Tên module
@apiSuccess (data) {string} description Mô tả module
@apiSuccess (data)    {Array}        accounts         Danh sách accounts

@apiSuccess (accounts)    {string}           id                 ID accounts
@apiSuccess (accounts)    {string}           username           username tên đăng nhập  của accounts
@apiSuccess (accounts)    {string}           avatar             Ảnh đại diện của accounts
@apiSuccess (accounts)    {string}           fullname           Tên đầy đủ của accounts
@apiSuccess (accounts)    {string}           phone_number       Số điện thoại của accounts
@apiSuccess (accounts)    {string}           email              Email của accounts

@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
      "name": "Quản lý nội dung",
      "description": "Quản lý nội dung chương trình",
      "create_time": "2017-08-07T04:02:28.002Z",
      "type": "created",
      "update_time": "2017-08-07T04:02:d8.002Z",
      "accounts": [
            {
                  "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                  "username": "Locnh",
                  "fullname": "Nguyễn Hữu Lộc",
                  "avatar": "",
                  "phone_number": "",
                  "email": "<EMAIL>"
            },
            ...
        ]
    },
    ...
  ],
  "lang": "vi",
  "sort": "name",
  "order": "asc",
  "paging": {
    ...
  }
}
"""