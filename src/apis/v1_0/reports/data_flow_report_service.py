#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 30/05/2024
"""
from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.reports.data_flow_controller import DataFlowReportController

data_flow_report_mod = Blueprint("data_flow_report", __name__)


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SNAPSHOT.TOTAL_SESSIONS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def snapshot_total_sessions(connector_id):
    return build_response_message(DataFlowReportController().snapshot_total_sessions(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SNAPSHOT.LIST_SESSIONS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def snapshot_get_list_session(connector_id):
    return build_response_message(DataFlowReportController().snapshot_get_list_session(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SNAPSHOT.DETAIL_SESSIONS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def snapshot_detail_sessions(connector_id, session_id):
    return build_response_message(DataFlowReportController().snapshot_detail_sessions(connector_id, session_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.STREAMING.HISTORY_BY_OBJECT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def streaming_history_by_object(connector_id):
    return build_response_message(DataFlowReportController().streaming_history_by_object(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.STREAMING.TOTAL_HISTORY_BY_PIPELINE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def streaming_history_total_pipeline(connector_id):
    return build_response_message(DataFlowReportController().streaming_history_total_pipeline(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.STREAMING.LIST_PIPELINE_HISTORY, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def streaming_history_pipeline_list(connector_id):
    return build_response_message(DataFlowReportController().streaming_history_pipeline_list(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.STREAMING.RESULT_REALTIME, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def streaming_result_real_time(connector_id):
    return build_response_message(DataFlowReportController().streaming_result_real_time(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.STREAMING.RESULT_REALTIME_BY_OBJECT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def streaming_real_time_result_by_object(connector_id):
    return build_response_message(DataFlowReportController().streaming_real_time_result_by_object(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.STREAMING.RESULT_REALTIME_BY_STATUS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def streaming_real_time_result_by_status(connector_id):
    return build_response_message(DataFlowReportController().streaming_real_time_result_by_status(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SNAPSHOT.RESULT_RUNNING_SESSIONS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def snapshot_result_running_sessions(connector_id):
    return build_response_message(DataFlowReportController().snapshot_result_running_sessions(connector_id))


@data_flow_report_mod.route(
    URI.DATA_FLOW.REPORTS.SNAPSHOT.RESULT_TOTAL_SESSIONS_BY_STATUS_EVERYDAY, methods=[HTTP.METHOD.GET]
)
@auth.verify_token
@try_catch_error
def snapshot_total_session_by_status_everyday(connector_id):
    return build_response_message(DataFlowReportController().snapshot_total_session_by_status_everyday(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.STREAMING.RESULT_REALTIME_BY_OBJECT_EVENT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def streaming_result_real_time_by_object_event(connector_id):
    return build_response_message(DataFlowReportController().streaming_result_real_time_by_object_event(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.STREAMING.RESULT_HISTORY_BY_OBJECT_EVENT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def streaming_result_history_by_object_event(connector_id):
    return build_response_message(DataFlowReportController().streaming_result_history_by_object_event(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.STREAMING.RESULT_HISTORY_FROM_SOURCE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def streaming_history_result_from_source(connector_id):
    return build_response_message(DataFlowReportController().streaming_history_result_from_source(connector_id))


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.TRIGGER.TRIGGER_AGGREGATE_REPORT, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def trigger_aggregate_report():
    return build_response_message(DataFlowReportController().trigger_aggregate_report())


@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.TRIGGER.TRIGGER_SEND_NOTI, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def trigger_send_noti():
    return build_response_message(DataFlowReportController().trigger_send_noti())

@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SNAPSHOT.PIPELINE_SESSION_BY_CONNECTOR, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def pipeline_session_by_connector(connector_id):
    return build_response_message(DataFlowReportController().pipeline_session_by_connector(connector_id))

@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SYNC_LOG.SESSION_SYNC_LOG_SNAPSHOT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def sync_log_get_session_snapshot(connector_id):
    return build_response_message(DataFlowReportController().sync_log_get_session_snapshot(connector_id))

@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SYNC_LOG.SESSION_SYNC_LOG_STREAMING, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def sync_log_get_session_streaming(connector_id):
    return build_response_message(DataFlowReportController().sync_log_get_session_streaming(connector_id))

@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SYNC_LOG.RESULT_SNAPSHOT_BY_STATUS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def sync_log_result_snapshot_by_status(connector_id, session_id):
    return build_response_message(DataFlowReportController().sync_log_result_snapshot_by_status(connector_id, session_id))

@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SYNC_LOG.DETAIL_LOG_SESSION_SNAPSHOT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def sync_log_get_log_session_snapshot(connector_id, session_id):
    return build_response_message(DataFlowReportController().sync_log_get_log_session_snapshot(connector_id, session_id))

@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SYNC_LOG.RESULT_STREAMING_BY_DATE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def sync_log_result_streaming_by_date(connector_id):
    return build_response_message(DataFlowReportController().sync_log_result_streaming_by_date(connector_id))

@data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SYNC_LOG.DETAIL_LOG_SESSION_STREAMING, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def sync_log_get_log_date_streaming(connector_id):
    return build_response_message(DataFlowReportController().sync_log_get_log_date_streaming(connector_id))

# @data_flow_report_mod.route(URI.DATA_FLOW.REPORTS.SYNC_LOG.DETAIL_MESSAGE, methods=[HTTP.METHOD.GET])
# @auth.verify_token
# @try_catch_error
# def sync_log_get_detail_message(connector_id, message_id):
#     return build_response_message(DataFlowReportController().sync_log_get_detail_message(connector_id, message_id))



