#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 10/06/2025
"""

from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.app_push_controller import AppPushController

app_push_mod = Blueprint("app_push_service", __name__)


@app_push_mod.route(URI.APP_PUSH.LIST_APPLICATIONS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_applications_integrate_push_notification():
    return build_response_message(AppPushController().get_list_applications_integrate_push_notification())


@app_push_mod.route(URI.APP_PUSH.LIST_APPLICATIONS_BY_IDS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_list_applications_integrate_push_notification_by_ids():
    return build_response_message(AppPushController().get_list_applications_integrate_push_notification_by_ids())


@app_push_mod.route(URI.APP_PUSH.DETAIL_APPLICATION, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_applications_integrate_push_notification_by_id(application_id):
    return build_response_message(
        AppPushController().get_list_applications_integrate_push_notification_by_id(application_id)
    )


@app_push_mod.route(URI.APP_PUSH.CREATE_APPLICATION, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def create_application_integrate_push_notification():
    return build_response_message(AppPushController().create_application_integrate_push_notification())


@app_push_mod.route(URI.APP_PUSH.UPDATE_APPLICATION, methods=[HTTP.METHOD.PATCH])
@auth.verify_token
@try_catch_error
def update_application_integrate_push_notification(application_id):
    return build_response_message(AppPushController().update_application_integrate_push_notification(application_id))


@app_push_mod.route(URI.APP_PUSH.CHANGE_STATUS_APPLICATION, methods=[HTTP.METHOD.PUT])
@auth.verify_token
@try_catch_error
def change_status_application_integrate_push_notification(application_id):
    return build_response_message(
        AppPushController().change_status_application_integrate_push_notification(application_id)
    )


@app_push_mod.route(URI.APP_PUSH.PROGRAMMING_LANGUAGES, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_program_language_integrate_push_notification():
    return build_response_message(AppPushController().get_program_language_integrate_push_notification())


@app_push_mod.route(URI.APP_PUSH.ACTION_HISTORY, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_action_history_integrate_push_notification(application_id):
    return build_response_message(AppPushController().get_action_history_integrate_push_notification(application_id))


@app_push_mod.route(URI.APP_PUSH.LIST_APPLICATIONS_BY_FILTER_TRACKING, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_applications_integrate_push_notification_by_filter_tracking():
    return build_response_message(
        AppPushController().get_list_applications_integrate_push_notification_by_filter_tracking()
    )


@app_push_mod.route(URI.APP_PUSH.UPLOAD_FILE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def upload_file():
    return build_response_message(AppPushController().upload_file())


@app_push_mod.route(URI.APP_PUSH.TEMPLATE_IMPORT_SCREEN, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_template_import_screen():
    return build_response_message(AppPushController().get_template_import_screen())