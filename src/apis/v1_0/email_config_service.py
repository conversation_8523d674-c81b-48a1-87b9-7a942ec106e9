from flask import Blueprint

from src.apis import HTT<PERSON>, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.email_config_controller import EmailConfigController

email_config_service_mod = Blueprint("email_config_service", __name__)


# create email config
@email_config_service_mod.route(URI.EMAIL_CONFIG.EMAIL_CONFIG, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def create_email_config():
    return build_response_message(EmailConfigController().create_email_config())


# create email config with sync
@email_config_service_mod.route(URI.EMAIL_CONFIG.SYNC_DATA, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def create_email_config_with_sync():
    return build_response_message(EmailConfigController().create_email_config_with_sync())


# #update status email config
@email_config_service_mod.route(URI.EMAIL_CONFIG.CHANGE_STATUS, methods=[HTTP.METHOD.PATCH])
@auth.verify_token
@try_catch_error
def update_status_email_config(email_config_id):
    return build_response_message(EmailConfigController().update_status_email_config(email_config_id))


# #export lst record
@email_config_service_mod.route(URI.EMAIL_CONFIG.EXPORT_RECORDS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def export_records(email_config_id):
    return build_response_message(EmailConfigController().export_records(email_config_id))


# #decode value in field of email config
@email_config_service_mod.route(URI.EMAIL_CONFIG.DECODE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def decrypt_values(email_config_id):
    return build_response_message(EmailConfigController().decrypt_values(email_config_id))


# send test email
@email_config_service_mod.route(URI.EMAIL_CONFIG.SEND_EMAIL, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def send_test_email(email_config_id):
    return build_response_message(EmailConfigController().send_test_email(email_config_id))


# #get list email config
@email_config_service_mod.route(URI.EMAIL_CONFIG.EMAIL_CONFIG, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_email_configs():
    return build_response_message(EmailConfigController().get_email_configs())


# #add mail
@email_config_service_mod.route(URI.EMAIL_CONFIG.EMAIL, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def add_email(email_config_id):
    return build_response_message(EmailConfigController().add_email(email_config_id))


# #get mail
@email_config_service_mod.route(URI.EMAIL_CONFIG.EMAIL, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_emails(email_config_id):
    return build_response_message(EmailConfigController().get_emails(email_config_id))


# delete email
@email_config_service_mod.route(URI.EMAIL_CONFIG.EMAIL_DETAIL, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_email(email_config_id, email):
    return build_response_message(EmailConfigController().delete_email(email_config_id, email))


# #detail sms config
@email_config_service_mod.route(URI.EMAIL_CONFIG.DETAIL, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def detail_email_config(email_config_id):
    return build_response_message(EmailConfigController().detail_email_config(email_config_id))


# delete email config
@email_config_service_mod.route(URI.EMAIL_CONFIG.DETAIL, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_email_config(email_config_id):
    return build_response_message(EmailConfigController().delete_email_config(email_config_id))


# update email config
@email_config_service_mod.route(URI.EMAIL_CONFIG.DETAIL, methods=[HTTP.METHOD.PATCH])
@auth.verify_token
@try_catch_error
def update_email_config(email_config_id):
    # return build_response_message(EmailConfigController().update_email_config(email_config_id))
    return build_response_message(EmailConfigController().update_email_config_aws(email_config_id))


# Response send email
@email_config_service_mod.route(URI.EMAIL_CONFIG.RESPONSE_SEND_SMS, methods=[HTTP.METHOD.GET])
@try_catch_error
def response_send_email(email_config_id):
    return build_response_message(EmailConfigController().response_send_email(email_config_id))


@email_config_service_mod.route(URI.EMAIL_CONFIG.WF_BY_DOMAIN, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def wf_by_domain():
    return build_response_message(EmailConfigController().wf_by_domain())


@email_config_service_mod.route(URI.EMAIL_CONFIG.LIST_REGION_AWS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def list_region_aws():
    return build_response_message(EmailConfigController().get_list_region_aws())


@email_config_service_mod.route(URI.EMAIL_CONFIG.DOMAIN_CHANGE_STATUS, methods=[HTTP.METHOD.PUT])
@auth.verify_token
@try_catch_error
def change_status_domain(email_config_id):
    return build_response_message(EmailConfigController().domain_change_status(email_config_id))


@email_config_service_mod.route(URI.EMAIL_CONFIG.DOMAIN_CONFIG, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_domain_config(email_config_id):
    return build_response_message(EmailConfigController().delete_domain_config(email_config_id))


@email_config_service_mod.route(URI.EMAIL_CONFIG.CONFIG_SHOW_ACCOUNT_AWS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def config_show_account_aws():
    return build_response_message(EmailConfigController().upsert_config_show_account_aws())


@email_config_service_mod.route(URI.EMAIL_CONFIG.LIST_DOMAIN_BY_MERCHANT, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_domain_by_merchant():
    return build_response_message(EmailConfigController().get_list_domain_by_merchant())
