#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 25/12/2024
"""

from flask import Blueprint

from src.apis import HTTP
from src.apis.uri import URI_CUSTOMIZE
from src.controllers.customize_controller import CustomizeController

customize_mod = Blueprint("customize_service", __name__)


@customize_mod.route(URI_CUSTOMIZE.CONNECTOR_LIVE_DEBUG, methods=[HTTP.METHOD.GET, HTTP.METHOD.POST])
def connector_live_debug():
    return CustomizeController().connector_live_debug()


@customize_mod.route(URI_CUSTOMIZE.CONNECTOR_DETAIL_LIVE_DEBUG, methods=[HTTP.METHOD.GET])
def connector_live_debug_detail_session(session_id):
    return CustomizeController().connector_live_debug_detail_session(session_id)
