import json
import os.path
import shutil

from bson import ObjectId
from flask import request
from mobio.libs.validator import Required, In, Length, InstanceOf
from mobio.sdks.admin import MobioAdminSDK
from mobio.libs.logging import MobioLogging
from mobio.sdks.media.mobio_media_sdk import CONSTANT_MAP_FORMAT_FILE, MobioMediaSDK
from pymongo import UpdateOne

from src.common import COMMON, CONSTANT, PopupBuilder, LANG, CommonParams, PopupDraft, STATIC_DIR, PopupTemplate
from src.common.mobio_exception import CustomError
from src.common.utils import (
    utf8_to_ascii,
    generate_session,
    get_utcnow,
    generate_object_id,
    convert_data_for_search_regex,
)
from src.controllers import MobioTemplateBaseController
from src.controllers.v1_0.popup_draft_controller import PopupDraftController
from src.controllers.v1_0.popup_report_controller import PopupReportController
from src.internal_module.journey_builder.call_to_Journey_builder import CallToJourneyBuilder
from src.internal_module.license import License
from src.models.mongodb.popup_builder_model import PopupBuilderModel
from src.models.mongodb.popup_draft_model import PopupDraftModel
from src.models.mongodb.popup_template_model import PopupTemplateModel


class PopupBuilderController(MobioTemplateBaseController):
    def __init__(self):
        super().__init__()
        self.class_name = self.__class__.__name__

    def validate_field_of_add_popup_builder(self, data_input):
        rule_input = {
            PopupBuilder.TITLE: [Required, InstanceOf(str), Length(1, 36)],
            PopupBuilder.POPUP_TEMPLATE_ID: [InstanceOf(str)],
            CommonParams.APPLIER_TYPE: [InstanceOf(str), In([CommonParams.GENERAL, CommonParams.PARTICULAR])],
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_field_of_update_popup_builder(self, data_input):
        rule_input = {
            PopupBuilder.POPUP_TEMPLATE_ID: [InstanceOf(str)],
            PopupBuilder.TITLE: [InstanceOf(str), Length(1, 36)],
            PopupBuilder.BODY_TEMPLATE: [Required, InstanceOf(str)],
            PopupBuilder.BUILD_TEMPLATE: [Required, InstanceOf(str)],
            CommonParams.SESSION: [Required, InstanceOf(str)],
            PopupBuilder.SECOND_PAGE: [Required, InstanceOf(str), In(["0", "1"])],
            PopupBuilder.DYNAMIC: [InstanceOf(str)],
            PopupBuilder.CURRENT_FIELD: [InstanceOf(str)],
            PopupBuilder.CURRENT_BUTTON: [InstanceOf(str)],
            PopupBuilder.NUMBER_LINK_TRACKING: [],
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_field_of_update_title(self, data_input):
        rule_input = {PopupBuilder.TITLE: [Required, InstanceOf(str), Length(1, 36)]}
        self.abort_if_validate_error(rule_input, data_input)

    def validate_field_of_active_popup_builder(self, data_input):
        rule_input = {
            PopupBuilder.POPUP_DRAFT_ID: [Required, InstanceOf(str)],
            CommonParams.SESSION: [Required, InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_field_of_connect_with_popup_builder(self, data_input):
        rule_input = {
            PopupBuilder.JOURNEY_BUILDER_USING: [Required, InstanceOf(str)],
            PopupBuilder.MASTER_CAMPAIGN_USING: [Required, InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_field_of_connect_with_many_popup_builder(self, data_input):
        rule_input = {
            PopupBuilder.POPUP_BUILDER_USING: [Required, InstanceOf(list)],
            PopupBuilder.JOURNEY_BUILDER_USING: [Required, InstanceOf(str)],
            PopupBuilder.MASTER_CAMPAIGN_USING: [Required, InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_get_list_name_popup_builder(self, data_input):
        rule_input = {CommonParams.IDS: [InstanceOf(list)]}
        self.abort_if_validate_error(rule_input, data_input)

    def validate_get_list_popup_builder_by_ids(self, data_input):
        rule_input = {CommonParams.IDS: [InstanceOf(list)], CommonParams.FIELDS: [InstanceOf(list)]}
        self.abort_if_validate_error(rule_input, data_input)

    def validate_active_in_list_popup_builder_json(self, data_input):
        rule_input = {CommonParams.SESSION: [Required, InstanceOf(str), Length(1)]}
        self.abort_if_validate_error(rule_input, data_input)

    def validate_copy_popup_builder(self, data_input):
        rule_input = {
            PopupBuilder.TITLE: [Required, InstanceOf(str), Length(1, 36)],
            CommonParams.SESSION: [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_check_title_is_exists(self, data_input):
        rule_input = {
            PopupBuilder.TITLE: [Required, InstanceOf(str), Length(1, 36)],
        }
        self.abort_if_validate_error(rule_input, data_input)

    def generate_avatar_info(self, merchant_id, entity_id, files, detail_entity):
        func_name = "generate_avatar_info"
        avatar_info = detail_entity.get(PopupBuilder.AVATAR_INFO, {})
        url_override = avatar_info.get(PopupBuilder.URL, "")
        local_path = avatar_info.get(PopupBuilder.LOCAL_PATH, "")
        is_local_path_exists = os.path.exists(local_path)
        if url_override and is_local_path_exists:
            self.override_image(merchant_id, files, entity_id, url_override)
            MobioLogging().info("{} :: {} override_image: {}".format(self.class_name, func_name, url_override))
        else:
            avatar_info = self.upload_image_with_file_data(merchant_id, files, entity_id)
            if not avatar_info:
                raise CustomError(self.lang.get(LANG.CREATE_IMG_ERROR).get("message"))
        return avatar_info

    def generate_popup_builder_info(self, merchant_id, entity_id, path, detail_entity):
        func_name = "generate_popup_builder_info"
        popup_builder_info = detail_entity.get(PopupBuilder.POPUP_BUILDER_INFO, {})
        url_override = popup_builder_info.get(PopupBuilder.URL, "")
        local_path = popup_builder_info.get(PopupBuilder.LOCAL_PATH, "")
        is_local_path_exists = os.path.exists(local_path)
        if url_override and is_local_path_exists:
            PopupBuilderController.override_html(merchant_id, entity_id, url_override, path)
            MobioLogging().info("{} :: {} override_popup: {}".format(self.class_name, func_name, url_override))
        else:
            popup_builder_info = self.upload_file_html_with_file_path(merchant_id, entity_id, path)
            if not popup_builder_info:
                raise CustomError(self.lang.get(LANG.PUBLISH_POPUP_BUILDER_ERROR).get("message"))
        return popup_builder_info

    @staticmethod
    def get_current_field_and_current_button(data):
        current_field = data.get(PopupBuilder.CURRENT_FIELD, {})
        if current_field:
            current_field = json.loads(current_field)
        current_button = data.get(PopupBuilder.CURRENT_BUTTON, {})
        if current_button:
            current_button = json.loads(current_button)
        return current_field, current_button

    @staticmethod
    def update_current_field_and_current_button(current_field, current_button, detail_popup_builder):
        # update config_field
        config_field = detail_popup_builder.get(PopupBuilder.CONFIG_FIELD, {})
        config_field.update(current_field)

        # update config_button
        config_button = detail_popup_builder.get(PopupBuilder.CONFIG_BUTTON, {})
        config_button.update(current_button)
        return config_field, config_button

    @staticmethod
    def override_html(merchant_id, entity_id, url_override, file_path):
        filename = str(entity_id) + ".html"
        result = MobioMediaSDK().override_file(
            filename=filename, merchant_id=merchant_id, file_path=file_path, url=url_override, desired_format=None
        )
        return result

    @staticmethod
    def generate_new_file_image_from_file_path(avatar_info, entity_id):
        local_path = avatar_info.get(PopupBuilder.LOCAL_PATH, "")
        is_local_path_exists = os.path.exists(local_path)
        if is_local_path_exists:
            new_file_path = STATIC_DIR + "/" + str(entity_id) + ".jpg"
            shutil.copy(local_path, new_file_path)
        else:
            new_file_path = ""
        return new_file_path

    @staticmethod
    def generate_title_unique(title):
        return title.strip().lower()

    @staticmethod
    def build_data_response(data, projection):
        for key, value in projection.items():
            if "." in key:
                split_key = key.split(".")
                if split_key[1] in data.get(split_key[0], {}):
                    del data[split_key[0]][split_key[1]]
            else:
                if key in data:
                    data.pop(key)
        return data

    @staticmethod
    def build_data_response_for_add_popup_builder(data):
        projection = {
            CommonParams.AVATAR_LOCAL_PATH: 0,
            CommonParams.POPUP_LOCAL_PATH: 0,
            PopupBuilder.JOURNEY_BUILDER_USING: 0,
            PopupBuilder.MASTER_CAMPAIGN_USING: 0,
            PopupBuilder.TITLE_CODE: 0,
            PopupBuilder.CONFIG_FIELD: 0,
            PopupBuilder.CURRENT_FIELD: 0,
            PopupBuilder.CONFIG_BUTTON: 0,
            PopupBuilder.CURRENT_BUTTON: 0,
            CommonParams.CREATED_TIME: 0,
            CommonParams.CREATED_BY: 0,
            CommonParams.UPDATED_TIME: 0,
            CommonParams.UPDATED_BY: 0,
        }
        return PopupBuilderController.build_data_response(data, projection)

    @staticmethod
    def response_update_popup_builder(data):
        projection = {
            CommonParams.AVATAR_LOCAL_PATH: 0,
            CommonParams.POPUP_LOCAL_PATH: 0,
            PopupBuilder.BODY_TEMPLATE: 0,
            PopupBuilder.BUILD_TEMPLATE: 0,
            PopupBuilder.DYNAMIC: 0,
            PopupBuilder.POPUP_TEMPLATE_ID: 0,
            PopupBuilder.SECOND_PAGE: 0,
            PopupBuilder.MASTER_CAMPAIGN_USING: 0,
            PopupBuilder.TITLE_CODE: 0,
            PopupBuilder.CONFIG_FIELD: 0,
            PopupBuilder.CURRENT_FIELD: 0,
            PopupBuilder.CONFIG_BUTTON: 0,
            PopupBuilder.CURRENT_BUTTON: 0,
            CommonParams.CREATED_TIME: 0,
            CommonParams.CREATED_BY: 0,
            CommonParams.UPDATED_TIME: 0,
            CommonParams.UPDATED_BY: 0,
        }
        return PopupBuilderController.build_data_response(data, projection)

    @staticmethod
    def response_edit_main_popup_builder(data):
        projection = {
            CommonParams.AVATAR_LOCAL_PATH: 0,
            CommonParams.POPUP_LOCAL_PATH: 0,
            PopupBuilder.JOURNEY_BUILDER_USING: 0,
            PopupBuilder.MASTER_CAMPAIGN_USING: 0,
            PopupBuilder.TITLE_CODE: 0,
            PopupBuilder.CONFIG_FIELD: 0,
            PopupBuilder.CURRENT_FIELD: 0,
            PopupBuilder.CONFIG_BUTTON: 0,
            PopupBuilder.CURRENT_BUTTON: 0,
            CommonParams.CREATED_TIME: 0,
            CommonParams.CREATED_BY: 0,
            CommonParams.UPDATED_TIME: 0,
            CommonParams.UPDATED_BY: 0,
        }
        return PopupBuilderController.build_data_response(data, projection)

    @staticmethod
    def response_delete_popup_draft(data):
        projection = {
            CommonParams.AVATAR_LOCAL_PATH: 0,
            CommonParams.POPUP_LOCAL_PATH: 0,
            PopupBuilder.JOURNEY_BUILDER_USING: 0,
            PopupBuilder.MASTER_CAMPAIGN_USING: 0,
            PopupBuilder.TITLE_CODE: 0,
            PopupBuilder.CONFIG_FIELD: 0,
            PopupBuilder.CURRENT_FIELD: 0,
            PopupBuilder.CONFIG_BUTTON: 0,
            PopupBuilder.CURRENT_BUTTON: 0,
            CommonParams.CREATED_TIME: 0,
            CommonParams.CREATED_BY: 0,
            CommonParams.UPDATED_TIME: 0,
            CommonParams.UPDATED_BY: 0,
        }
        return PopupBuilderController.build_data_response(data, projection)

    def check_title_is_exists(self):
        merchant_id = PopupBuilderController.get_merchant_header()
        body = request.get_json()
        self.validate_check_title_is_exists(body)
        result = False
        title = body.get(PopupBuilder.TITLE, "")
        title_unique = PopupBuilderController.generate_title_unique(title)
        if title:
            is_exists = PopupBuilderModel().get_detail_popup_builder_by_title_unique(merchant_id, title_unique)
            if is_exists:
                result = True
        data = {PopupBuilder.IS_EXISTS: result}
        return {"data": data}

    def add_popup_builder(self):
        """
        - Tạo popup builder
        - Nếu gửi lên id của mẫu popup thì tạo popup mới từ mẫu popup
        - Nếu không gửi lên id mẫu popup thì tạo popup trống
        @return:
        """

        func_name = "add_popup_builder"
        merchant_id = PopupBuilderController.get_merchant_header()
        body = request.get_json()
        self.validate_field_of_add_popup_builder(body)

        # Check title is exists
        title = body.get(PopupBuilder.TITLE, "")
        title_unique = PopupBuilderController.generate_title_unique(title)
        if title:
            is_exists = PopupBuilderModel().get_detail_popup_builder_by_title_unique(merchant_id, title_unique)
            if is_exists:
                raise CustomError(self.lang.get(LANG.NAME_POPUP_BUILDER_ALREADY_EXISTS_IN_SYSTEM).get("message"))

        popup_builder_id = generate_object_id()
        popup_template_id = body.get(PopupBuilder.POPUP_TEMPLATE_ID)
        if popup_template_id:
            self.validate_format_object_id(popup_template_id)
            type_popup_template = str(body.get(CommonParams.APPLIER_TYPE))
            if type_popup_template == CommonParams.PARTICULAR:
                search_options = {
                    CommonParams.MERCHANT_ID: merchant_id,
                    CommonParams.ID: ObjectId(popup_template_id),
                }
            else:
                search_options = {
                    CommonParams.ID: ObjectId(popup_template_id),
                }
            detail_popup_template = PopupTemplateModel().get_detail_popup_template_full_info(search_options)
            if not detail_popup_template:
                raise CustomError(self.lang.get(LANG.POPUP_TEMPLATE_NOT_EXIST).get("message"))

            avatar_info_of_template = detail_popup_template.get(PopupTemplate.AVATAR_INFO)
            new_file_path = PopupBuilderController.generate_new_file_image_from_file_path(
                avatar_info_of_template, popup_builder_id
            )
            if new_file_path:
                avatar_info = self.upload_image_with_file_path(merchant_id, popup_builder_id, new_file_path)
                if avatar_info:
                    body.update({PopupBuilder.AVATAR_INFO: avatar_info})
            body.update(
                {
                    PopupBuilder.BODY_TEMPLATE: detail_popup_template.get(PopupTemplate.BODY_TEMPLATE),
                    PopupBuilder.BUILD_TEMPLATE: detail_popup_template.get(PopupTemplate.BUILD_TEMPLATE),
                    PopupBuilder.SECOND_PAGE: detail_popup_template.get(PopupTemplate.SECOND_PAGE),
                }
            )
        account_id = MobioAdminSDK().get_value_from_token("id")
        title_code = utf8_to_ascii(title.lower())
        time_now = get_utcnow()
        body.update(
            {
                CommonParams.ID: ObjectId(popup_builder_id),
                PopupBuilder.TITLE_CODE: title_code,
                PopupBuilder.TITLE_UNIQUE: title_unique,
                CommonParams.MERCHANT_ID: merchant_id,
                PopupBuilder.ACTIVE: PopupBuilder.NOT_ACTIVATED,
                CommonParams.SESSION: generate_session(),
                CommonParams.CREATED_TIME: time_now,
                CommonParams.CREATED_BY: account_id,
                CommonParams.UPDATED_TIME: time_now,
                CommonParams.UPDATED_BY: account_id,
            }
        )

        popup_builder_id = str(PopupBuilderModel().insert_document(body).inserted_id)
        if not popup_builder_id:
            raise CustomError(self.lang.get(LANG.ADD_POPUP_BUILDER_ERROR).get("message"))
        MobioLogging().info("{} :: {} insert success: {}".format(self.class_name, func_name, popup_builder_id))

        body = self.build_data_response_for_add_popup_builder(body)
        return {"data": self.json_encoder(body)}

    def update_popup_builder(self, popup_builder_id):
        """
        - Cập nhật popup builder
        - Nếu chưa active thì cập nhật vào bản chính
        - Nếu đã active và chưa có bản nháp thì tạo bản nháp
        - Nếu đã active và có bản nháp thì ghi đè bản nháp
        @param popup_builder_id:
        @return:
        """
        self.validate_format_object_id(popup_builder_id)
        merchant_id = PopupBuilderController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token("id")
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))

        form_data = request.form
        body = self.convert_form_data_to_raw_data(form_data)
        files_data = request.files
        files = files_data.get(CommonParams.AVATAR)
        self.validate_field_of_update_popup_builder(body)
        # Kiem tra session
        if detail_popup_builder.get(CommonParams.SESSION) != body.get(CommonParams.SESSION, ""):
            raise CustomError(self.lang.get(LANG.SESSION_DISSIMILARITY).get("message"))

        session = generate_session()
        time_now = get_utcnow()
        is_active = detail_popup_builder.get(PopupBuilder.ACTIVE)
        if is_active == PopupBuilder.NOT_ACTIVATED:
            current_field, current_button = PopupBuilderController.get_current_field_and_current_button(body)

            body.update(
                {
                    PopupBuilder.POPUP_DRAFT_ID: None,
                    PopupBuilder.CURRENT_FIELD: current_field,
                    PopupBuilder.CURRENT_BUTTON: current_button,
                    CommonParams.UPDATED_TIME: time_now,
                    CommonParams.UPDATED_BY: account_id,
                    CommonParams.SESSION: session,
                }
            )
            if files:
                avatar_info = self.generate_avatar_info(merchant_id, popup_builder_id, files, detail_popup_builder)
                body.update({CommonParams.AVATAR_INFO: avatar_info})

            status_update = PopupBuilderModel().update_one_query({CommonParams.ID: ObjectId(popup_builder_id)}, body)
            if status_update:
                detail_popup_builder.update(body)
                data_response = PopupBuilderController.response_update_popup_builder(detail_popup_builder)
                return {"data": self.json_encoder(data_response)}

        elif is_active == PopupBuilder.ACTIVATED:
            popup_draft_id = detail_popup_builder.get(PopupBuilder.POPUP_DRAFT_ID)
            # Nếu có bản nháp thì cập nhật vào bản nháp, nếu chưa có thì tạo bản nháp mới
            if popup_draft_id:
                detail_popup_draft = PopupDraftModel().get_detail_popup_draft(popup_draft_id, merchant_id)

                # # Lấy field và button trong form hiên tại
                current_field, current_button = PopupBuilderController.get_current_field_and_current_button(body)

                body.update(
                    {
                        PopupBuilder.CURRENT_FIELD: current_field,
                        PopupBuilder.CURRENT_BUTTON: current_button,
                        CommonParams.UPDATED_TIME: time_now,
                        CommonParams.UPDATED_BY: account_id,
                        CommonParams.SESSION: session,
                    }
                )
                if files:
                    avatar_info = self.generate_avatar_info(merchant_id, popup_draft_id, files, detail_popup_draft)
                    body.update({CommonParams.AVATAR_INFO: avatar_info})

                # update session and updated_time of popup builder
                data_update_popup_builder = {
                    CommonParams.UPDATED_TIME: time_now,
                    CommonParams.UPDATED_BY: account_id,
                    CommonParams.SESSION: session,
                }
                PopupBuilderModel().update_one_query(
                    {CommonParams.ID: ObjectId(popup_builder_id), CommonParams.MERCHANT_ID: merchant_id},
                    data_update_popup_builder,
                )

                # update popup draft
                status_update = PopupDraftModel().update_one_query({CommonParams.ID: ObjectId(popup_draft_id)}, body)
                if status_update:
                    detail_popup_builder.update(data_update_popup_builder)
                    data_response = PopupBuilderController.response_update_popup_builder(detail_popup_builder)
                    return {"data": self.json_encoder(data_response)}
            else:
                popup_draft_id = generate_object_id()
                if files:
                    avatar_info = self.upload_image_with_file_data(merchant_id, files, popup_draft_id)
                    if not avatar_info:
                        raise CustomError(self.lang.get(LANG.CREATE_IMG_ERROR).get("message"))
                    body.update({CommonParams.AVATAR_INFO: avatar_info})
                current_field, current_button = PopupBuilderController.get_current_field_and_current_button(body)

                body.update(
                    {
                        CommonParams.ID: ObjectId(popup_draft_id),
                        PopupDraft.TITLE: detail_popup_builder.get(PopupBuilder.TITLE, ""),
                        PopupDraft.TITLE_CODE: detail_popup_builder.get(PopupBuilder.TITLE_CODE, ""),
                        CommonParams.MERCHANT_ID: merchant_id,
                        PopupBuilder.CURRENT_FIELD: current_field,
                        PopupBuilder.CURRENT_BUTTON: current_button,
                        CommonParams.CREATED_TIME: time_now,
                        CommonParams.CREATED_BY: account_id,
                        CommonParams.UPDATED_TIME: time_now,
                        CommonParams.UPDATED_BY: account_id,
                        CommonParams.SESSION: session,
                    }
                )

                popup_draft_id = str(PopupDraftModel().insert_document(body).inserted_id)
                if not popup_draft_id:
                    raise CustomError(self.lang.get(LANG.ADD_POPUP_DRAFT_ERROR).get("message"))

                # update popup_draft_id to popup builder
                search_option_popup_builder = {
                    CommonParams.ID: ObjectId(popup_builder_id),
                    CommonParams.MERCHANT_ID: merchant_id,
                }
                data_update_popup_builder = {
                    PopupBuilder.POPUP_DRAFT_ID: popup_draft_id,
                    CommonParams.UPDATED_TIME: time_now,
                    CommonParams.UPDATED_BY: account_id,
                    CommonParams.SESSION: session,
                }
                result_update = PopupBuilderModel().update_one_query(
                    search_option_popup_builder, data_update_popup_builder
                )
                if not result_update:
                    raise CustomError(self.lang.get(LANG.UPDATE_POPUP_DRAFT_ID_TO_POPUP_BUILDER_ERROR).get("message"))
                if result_update:
                    detail_popup_builder.update(data_update_popup_builder)
                    data_response = PopupBuilderController.response_update_popup_builder(detail_popup_builder)
                    return {"data": self.json_encoder(data_response)}
        raise CustomError

    def update_title_popup_builder(self, popup_builder_id):
        """
        - Cập nhật title của popup_builder
        @param popup_builder_id:
        @return:
        """

        func_name = "update_title_popup_builder"
        self.validate_format_object_id(popup_builder_id)
        merchant_id = PopupBuilderController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token("id")
        form_data = request.form
        body = self.convert_form_data_to_raw_data(form_data)
        self.validate_field_of_update_title(body)

        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))

        # Check title is exists
        title = body.get(PopupBuilder.TITLE, "")
        title_unique = PopupBuilderController.generate_title_unique(title)
        if title:
            is_exists = PopupBuilderModel().get_detail_popup_builder_by_title_unique(merchant_id, title_unique)
            if is_exists:
                raise CustomError(self.lang.get(LANG.NAME_POPUP_BUILDER_ALREADY_EXISTS_IN_SYSTEM).get("message"))

        title_code = utf8_to_ascii(title.lower())
        body.update(
            {
                PopupBuilder.TITLE_CODE: title_code,
                PopupBuilder.TITLE_UNIQUE: title_unique,
                CommonParams.UPDATED_TIME: get_utcnow(),
                CommonParams.UPDATED_BY: account_id,
            }
        )
        popup_draft_id = detail_popup_builder.get(PopupBuilder.POPUP_DRAFT_ID)
        if popup_draft_id:
            PopupDraftModel().update_one_query({CommonParams.ID: ObjectId(popup_draft_id)}, body)

        status_update = PopupBuilderModel().update_one_query({CommonParams.ID: ObjectId(popup_builder_id)}, body)
        MobioLogging().info("{} :: {} status_update: {}".format(self.class_name, func_name, status_update))
        if status_update:
            return
        raise CustomError

    def delete_one_popup_builder(self, popup_builder_id):
        """
        - Xóa một popup_builder theo id
        - Nếu popup chưa active xóa PU và các link publish của PU
        - Nếu popup đã active và có bản nháp xóa PU + bản nháp + các link publish của PU và bản nháp
        @param popup_builder_id:
        @return:
        """

        self.validate_format_object_id(popup_builder_id)
        merchant_id = PopupBuilderController.get_merchant_header()
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))

        is_active = detail_popup_builder.get(PopupBuilder.ACTIVE)
        if is_active == PopupBuilder.ACTIVATED:
            journey_builder_using = detail_popup_builder.get(PopupBuilder.JOURNEY_BUILDER_USING, [])
            if len(journey_builder_using) == 0:
                popup_draft_id = detail_popup_builder.get(PopupBuilder.POPUP_DRAFT_ID, "")
                # Nếu có bản nháp thì xóa bản nháp
                if popup_draft_id:
                    PopupDraftController().delete_one_popup_draft(popup_draft_id)
            else:
                raise CustomError(self.lang.get(LANG.POPUP_IS_BEING_USED).get("message"))

        delete_options = {CommonParams.ID: ObjectId(popup_builder_id), CommonParams.MERCHANT_ID: merchant_id}
        status_delete = PopupBuilderModel().delete_one(delete_options=delete_options)
        if status_delete:
            url = self.get_url_need_delete(detail_popup_builder)
            self.delete_info_publish_with_mobio_media_sdk(merchant_id, url)
            return
        raise CustomError(self.lang.get(LANG.DELETE_POPUP_BUILDER_ERROR).get("message"))

    def get_list_popup_builder(self):
        merchant_id = PopupBuilderController.get_merchant_header()
        args = request.args
        paging = PopupBuilderController.get_paging_from_args(args)
        search_options = {CommonParams.MERCHANT_ID: merchant_id}

        is_active = args.get(PopupBuilder.ACTIVE)
        if is_active:
            search_options.update({PopupBuilder.ACTIVE: is_active})

        is_search = args.get(PopupBuilder.TITLE)
        if is_search:
            title_code = utf8_to_ascii(is_search.lower())
            search_options.update({PopupBuilder.TITLE_CODE: {"$regex": title_code}})

        results = PopupBuilderModel().get_list_popup_builder(search_options, paging)
        paging = PopupBuilderController.get_info_paging_for_response(results, paging)

        data = list()
        lst_popup_builder = results.get(CommonParams.LIST_DATA, [])
        for popup_builder in lst_popup_builder:
            popup_builder = self.json_encoder(popup_builder)
            if popup_builder.get(PopupBuilder.ACTIVE, PopupBuilder.NOT_ACTIVATED) == PopupBuilder.ACTIVATED:
                popup_builder_id = popup_builder.get("id", "")
                result = PopupReportController.get_information_for_list_popup(merchant_id, popup_builder_id)
                popup_builder.update(result)
            data.append(popup_builder)
        return {"data": data, "paging": paging}

    def get_detail_popup_builder(self, popup_builder_id):
        self.validate_format_object_id(popup_builder_id)
        merchant_id = PopupBuilderController.get_merchant_header()
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))
        return {"data": self.json_encoder(detail_popup_builder)}

    def update_and_active_popup_builder(self, popup_builder_id):
        """
        - Cập nhật và kích hoạt popup_builder theo id
        @param popup_builder_id:
        @return:
        """

        self.validate_format_object_id(popup_builder_id)
        func_name = "update_and_active_popup_builder"
        merchant_id = PopupBuilderController.get_merchant_header()
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))
        form_data = request.form
        body = self.convert_form_data_to_raw_data(form_data)
        self.validate_field_of_update_popup_builder(body)

        if detail_popup_builder.get(CommonParams.SESSION, "") != body.get(CommonParams.SESSION, ""):
            raise CustomError(self.lang.get(LANG.SESSION_DISSIMILARITY).get("message"))

        files_data = request.files
        files = files_data.get(CommonParams.AVATAR)
        if files:
            avatar_info = self.generate_avatar_info(merchant_id, popup_builder_id, files, detail_popup_builder)
            body.update({CommonParams.AVATAR_INFO: avatar_info})

        build_template = body.get(PopupBuilder.BUILD_TEMPLATE, "")
        if build_template:
            path = self.write_file_html_to_folder_static(popup_builder_id, build_template)
            popup_builder_info = self.generate_popup_builder_info(
                merchant_id, popup_builder_id, path, detail_popup_builder
            )
            body.update({PopupBuilder.POPUP_BUILDER_INFO: popup_builder_info})

        current_field, current_button = PopupBuilderController.get_current_field_and_current_button(body)
        config_field, config_button = PopupBuilderController.update_current_field_and_current_button(
            current_field, current_button, detail_popup_builder
        )
        body.update(
            {
                PopupBuilder.CURRENT_FIELD: current_field,
                PopupBuilder.CURRENT_BUTTON: current_button,
                PopupBuilder.CONFIG_FIELD: config_field,
                PopupBuilder.CONFIG_BUTTON: config_button,
            }
        )

        session = generate_session()
        time_now = get_utcnow()
        account_id = MobioAdminSDK().get_value_from_token("id")

        #  Cần sửa lại hàm xóa bản nháp, thêm 1 func xóa nháp bên popup_draft_controller
        popup_draft_id = detail_popup_builder.get(PopupBuilder.POPUP_DRAFT_ID)
        if popup_draft_id:
            PopupDraftController().delete_one_popup_draft(popup_draft_id)

        body.update(
            {
                PopupBuilder.POPUP_DRAFT_ID: None,
                CommonParams.UPDATED_TIME: time_now,
                CommonParams.UPDATED_BY: account_id,
                CommonParams.SESSION: session,
                PopupBuilder.ACTIVE: PopupBuilder.ACTIVATED,
            }
        )

        status_update = PopupBuilderModel().update_one_query({CommonParams.ID: ObjectId(popup_builder_id)}, body)
        MobioLogging().info("{} :: {} status_update: {}".format(self.class_name, func_name, status_update))
        if status_update:
            data_response = PopupBuilderModel().get_detail_popup_builder(popup_builder_id, merchant_id)
            return {"data": self.json_encoder(data_response)}
        raise CustomError

    def active_with_popup_builder_not_draft(self, detail_popup_builder, merchant_id, popup_builder_id):
        """
        - Được thay thế bằng func active_in_list_popup_builder_json, tương lại sẽ bỏ func này
        @param detail_popup_builder:
        @param merchant_id:
        @param popup_builder_id:
        @return:
        """
        build_template = detail_popup_builder.get(PopupBuilder.BUILD_TEMPLATE, "")
        path = self.write_file_html_to_folder_static(popup_builder_id, build_template)
        popup_builder_info = self.upload_file_html_with_file_path(merchant_id, popup_builder_id, path)
        if not popup_builder_info:
            raise CustomError(self.lang.get(LANG.PUBLISH_POPUP_BUILDER_ERROR).get("message"))
        session = generate_session()
        journey_builder_using = detail_popup_builder.get(PopupBuilder.JOURNEY_BUILDER_USING, [])
        master_campaign_using = detail_popup_builder.get(PopupBuilder.MASTER_CAMPAIGN_USING, [])
        config_field = detail_popup_builder.get(PopupBuilder.CURRENT_FIELD, {})
        config_button = detail_popup_builder.get(PopupBuilder.CURRENT_BUTTON, {})
        account_id = MobioAdminSDK().get_value_from_token("id")
        data_update = {
            PopupBuilder.ACTIVE: PopupBuilder.ACTIVATED,
            PopupBuilder.POPUP_BUILDER_INFO: popup_builder_info,
            PopupBuilder.JOURNEY_BUILDER_USING: journey_builder_using,
            PopupBuilder.MASTER_CAMPAIGN_USING: master_campaign_using,
            PopupBuilder.CONFIG_FIELD: config_field,
            PopupBuilder.CONFIG_BUTTON: config_button,
            CommonParams.UPDATED_TIME: get_utcnow(),
            CommonParams.UPDATED_BY: account_id,
            CommonParams.SESSION: session,
        }
        status_update = PopupBuilderModel().update_one_query({CommonParams.ID: ObjectId(popup_builder_id)}, data_update)
        if status_update:
            return True
        raise CustomError

    def active_in_list_popup_builder(self, popup_builder_id):
        """
        - Được thay thế bằng func active_in_list_popup_builder_json, tương lại sẽ bỏ func này
        @param popup_builder_id:
        @return:
        """
        self.validate_format_object_id(popup_builder_id)
        func_name = "active_in_list_popup_builder"
        merchant_id = PopupBuilderController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token("id")
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))

        form_data = request.form
        body = self.convert_form_data_to_raw_data(form_data)
        if detail_popup_builder.get(CommonParams.SESSION, "") != body.get(CommonParams.SESSION, ""):
            raise CustomError(self.lang.get(LANG.SESSION_DISSIMILARITY).get("message"))

        session = generate_session()
        is_active = detail_popup_builder.get(PopupBuilder.ACTIVE, None)
        if is_active == PopupBuilder.NOT_ACTIVATED:
            result = self.active_with_popup_builder_not_draft(detail_popup_builder, merchant_id, popup_builder_id)
            if result:
                detail_popup_builder = PopupBuilderModel().get_detail_popup_builder(popup_builder_id, merchant_id)
                return {"data": self.json_encoder(detail_popup_builder)}
            raise CustomError
        elif is_active == PopupBuilder.ACTIVATED:
            is_draft = detail_popup_builder.get(PopupBuilder.POPUP_DRAFT_ID, "")
            if not is_draft or is_draft == "":
                raise CustomError(self.lang.get(LANG.POPUP_BUILDER_IS_LATEST).get("message"))
            else:
                popup_draft_id = detail_popup_builder.get(PopupBuilder.POPUP_DRAFT_ID, "")
                detail_popup_draft = PopupDraftModel().get_detail_popup_draft(popup_draft_id, merchant_id)
                if not detail_popup_draft:
                    raise CustomError(self.lang.get(LANG.POPUP_DRAFT_NOT_EXIST).get("message"))

                # delete link images trên bản chính
                urls = list()
                if detail_popup_builder.get(PopupBuilder.AVATAR_INFO, {}).get(PopupBuilder.URL, ""):
                    urls.append(detail_popup_builder["avatar_info"]["url"])
                self.delete_info_publish_with_mobio_media_sdk(merchant_id, urls)

                # get current_field and current_button from PopupDraft
                current_field = detail_popup_draft.get(PopupDraft.CURRENT_FIELD, {})
                current_button = detail_popup_draft.get(PopupDraft.CURRENT_BUTTON, {})

                # update config_field
                config_field = detail_popup_builder.get(PopupBuilder.CONFIG_FIELD, {})
                config_field.update(current_field)

                # update config_button
                config_button = detail_popup_builder.get(PopupBuilder.CONFIG_BUTTON, {})
                config_button.update(current_button)

                data_update = {
                    PopupBuilder.BODY_TEMPLATE: detail_popup_draft.get(PopupDraft.BODY_TEMPLATE, ""),
                    PopupBuilder.BUILD_TEMPLATE: detail_popup_draft.get(PopupDraft.BUILD_TEMPLATE, ""),
                    PopupBuilder.DYNAMIC: detail_popup_draft.get(PopupDraft.DYNAMIC, ""),
                    PopupBuilder.SECOND_PAGE: detail_popup_draft.get(PopupDraft.SECOND_PAGE, "0"),
                    CommonParams.AVATAR_INFO: detail_popup_draft.get(CommonParams.AVATAR_INFO, {}),
                    PopupBuilder.POPUP_DRAFT_ID: None,
                    PopupBuilder.CONFIG_FIELD: config_field,
                    PopupBuilder.CURRENT_FIELD: current_field,
                    PopupBuilder.CONFIG_BUTTON: config_button,
                    PopupBuilder.CURRENT_BUTTON: current_button,
                }
                data_update.update(
                    {
                        CommonParams.UPDATED_TIME: get_utcnow(),
                        CommonParams.UPDATED_BY: account_id,
                        CommonParams.SESSION: session,
                    }
                )

                # tạo file html, upload lại file html vào bản chính
                build_template = detail_popup_draft.get(PopupDraft.BUILD_TEMPLATE, "")
                if build_template:
                    path = self.write_file_html_to_folder_static(popup_builder_id, build_template)
                    url_override = detail_popup_builder.get(PopupBuilder.POPUP_BUILDER_INFO, {}).get(
                        PopupBuilder.URL, ""
                    )
                    local_path = detail_popup_builder.get(PopupBuilder.POPUP_BUILDER_INFO, {}).get(
                        PopupBuilder.LOCAL_PATH, ""
                    )
                    is_local_path_exists = os.path.exists(local_path)
                    if url_override and is_local_path_exists:
                        PopupBuilderController.override_html(merchant_id, popup_builder_id, url_override, path)
                        MobioLogging().info(
                            "{} :: {} override_popup: {}".format(self.class_name, func_name, url_override)
                        )
                    else:
                        popup_builder_info = self.upload_file_html_with_file_path(merchant_id, popup_builder_id, path)
                        if not popup_builder_info:
                            raise CustomError(self.lang.get(LANG.PUBLISH_POPUP_BUILDER_ERROR).get("message"))
                        body.update({PopupBuilder.POPUP_BUILDER_INFO: popup_builder_info})

                status_update = PopupBuilderModel().update_one_query(
                    {CommonParams.ID: ObjectId(popup_builder_id)}, data_update
                )
                if status_update:
                    delete_options = {CommonParams.ID: ObjectId(popup_draft_id)}
                    # xóa bản nháp
                    status_delete = PopupDraftModel().delete_one(delete_options)
                    MobioLogging().info(
                        "{} :: {} status delete draft: {}".format(self.class_name, func_name, status_delete)
                    )
                    detail_popup_builder = PopupBuilderModel().get_detail_popup_builder(popup_builder_id, merchant_id)
                    return {"data": self.json_encoder(detail_popup_builder)}
        raise CustomError

    def active_in_list_popup_builder_json(self, popup_builder_id):
        """
        - Kích hoạt PU bên ngoài màn hình lấy danh sách PU
        @param popup_builder_id:
        @return:
        """

        self.validate_format_object_id(popup_builder_id)
        func_name = "active_in_list_popup_builder_json"
        merchant_id = PopupBuilderController.get_merchant_header()
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))

        body = request.get_json()
        self.validate_active_in_list_popup_builder_json(body)

        if detail_popup_builder.get(CommonParams.SESSION, "") != body.get(CommonParams.SESSION, ""):
            raise CustomError(self.lang.get(LANG.SESSION_DISSIMILARITY).get("message"))

        is_active = detail_popup_builder.get(PopupBuilder.ACTIVE)
        if is_active == PopupBuilder.NOT_ACTIVATED:
            build_template = detail_popup_builder.get(PopupBuilder.BUILD_TEMPLATE, "")
            path = self.write_file_html_to_folder_static(popup_builder_id, build_template)
            popup_builder_info = self.upload_file_html_with_file_path(merchant_id, popup_builder_id, path)
            if not popup_builder_info:
                raise CustomError(self.lang.get(LANG.PUBLISH_POPUP_BUILDER_ERROR).get("message"))

            account_id = MobioAdminSDK().get_value_from_token("id")

            # insert config_field = current_field, config_button = current_button
            config_field = detail_popup_builder.get(PopupBuilder.CURRENT_FIELD, {})
            config_button = detail_popup_builder.get(PopupBuilder.CURRENT_BUTTON, {})

            data_update = {
                PopupBuilder.ACTIVE: PopupBuilder.ACTIVATED,
                PopupBuilder.POPUP_BUILDER_INFO: popup_builder_info,
                PopupBuilder.CONFIG_FIELD: config_field,
                PopupBuilder.CONFIG_BUTTON: config_button,
                CommonParams.UPDATED_TIME: get_utcnow(),
                CommonParams.UPDATED_BY: account_id,
                CommonParams.SESSION: generate_session(),
            }
            status_update = PopupBuilderModel().update_one_query(
                {CommonParams.ID: ObjectId(popup_builder_id)}, data_update
            )
            MobioLogging().info("{} :: {} status_update: {}".format(self.class_name, func_name, status_update))

            if status_update:
                detail_popup_builder = PopupBuilderModel().get_detail_popup_builder(popup_builder_id, merchant_id)
                return {"data": self.json_encoder(detail_popup_builder)}

        raise CustomError

    def edit_main_popup_builder(self, popup_builder_id):
        """
        - Option sửa bản chính của popup
        - Khi chọn sửa bản chính thì bản gốc ngay lập tức bị xóa
        @param popup_builder_id:
        @return:
        """

        self.validate_format_object_id(popup_builder_id)
        func_name = "edit_main_popup_builder"
        merchant_id = PopupBuilderController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token("id")
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))

        popup_draft_id = detail_popup_builder.get(PopupBuilder.POPUP_DRAFT_ID, "")
        if popup_draft_id:
            PopupDraftController().delete_one_popup_draft(popup_draft_id)

        # câp nhật lại bản chính với popup_draft_id = None
        data_update = {
            CommonParams.UPDATED_TIME: get_utcnow(),
            CommonParams.UPDATED_BY: account_id,
            PopupBuilder.POPUP_DRAFT_ID: None,
        }
        status_update = PopupBuilderModel().update_one_query({CommonParams.ID: ObjectId(popup_builder_id)}, data_update)
        MobioLogging().info("{} :: {} status update: {}".format(self.class_name, func_name, status_update))

        # get data bản chinh va tra về
        if status_update:
            detail_popup_builder.update(data_update)
            detail_popup_builder = PopupBuilderController.response_edit_main_popup_builder(detail_popup_builder)
            return {"data": self.json_encoder(detail_popup_builder)}

    def delete_popup_draft(self, popup_builder_id):
        """
        - Option xóa bản nháp của PU
        @param popup_builder_id:
        @return:
        """
        self.validate_format_object_id(popup_builder_id)
        func_name = "delete_popup_draft"
        merchant_id = PopupBuilderController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token("id")
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        popup_draft_id = detail_popup_builder.get(PopupDraft.POPUP_DRAFT_ID, "")
        if popup_draft_id:
            # get ban nhap, nếu còn bản nháp, xóa data avatar upload, xóa bản nháp
            PopupDraftController().delete_one_popup_draft(popup_draft_id)

        # câp nhật lại bản chính với popup_draft_id = None
        session = generate_session()
        data_update = {
            CommonParams.UPDATED_TIME: get_utcnow(),
            CommonParams.UPDATED_BY: account_id,
            PopupBuilder.POPUP_DRAFT_ID: None,
            CommonParams.SESSION: session,
        }
        status_update = PopupBuilderModel().update_one_query({CommonParams.ID: ObjectId(popup_builder_id)}, data_update)
        MobioLogging().info("{} :: {} status update {}".format(self.class_name, func_name, status_update))

        if status_update:
            detail_popup_builder.update(data_update)
            detail_popup_builder = PopupBuilderController.response_delete_popup_draft(detail_popup_builder)
            return {"data": self.json_encoder(detail_popup_builder)}
        raise CustomError

    def connect_with_popup_builder(self, popup_builder_id):
        """
        - Khi PU được sử dụng gửi thông tin để lưu lại thực thể đã sử dụng
        - Sử dụng khi check xóa popup (không cho xóa nếu PU đang được sử dụng)
        @param popup_builder_id:
        @return:
        """
        self.validate_format_object_id(popup_builder_id)
        func_name = "connect_with_popup_builder"
        merchant_id = PopupBuilderController.get_merchant_header()
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))

        is_active = detail_popup_builder.get(PopupBuilder.ACTIVE, PopupBuilder.NOT_ACTIVATED)
        if is_active == PopupBuilder.NOT_ACTIVATED:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_ACTIVE).get("message"))
        body = request.get_json()
        self.validate_field_of_connect_with_popup_builder(body)
        journey_builder_id = body.get(PopupBuilder.JOURNEY_BUILDER_USING, "")
        master_campaign_id = body.get(PopupBuilder.MASTER_CAMPAIGN_USING, "")

        data_update = {
            "$addToSet": {
                PopupBuilder.JOURNEY_BUILDER_USING: journey_builder_id,
                PopupBuilder.MASTER_CAMPAIGN_USING: master_campaign_id,
            }
        }

        status_update = PopupBuilderModel().update_dictionary(popup_builder_id, data_update)
        MobioLogging().info("{} :: {} connect-with status: {}".format(self.class_name, func_name, status_update))

        if status_update:
            return
        raise CustomError

    def connect_with_many_popup_builder(self):
        """
        - Khi PU được sử dụng gửi thông tin để lưu lại thực thể đã sử dụng
        - Sử dụng khi check xóa popup (không cho xóa nếu PU đang được sử dụng)
        @return:
        """
        func_name = "connect_with_many_popup_builder"
        merchant_id = PopupBuilderController.get_merchant_header()
        body = request.get_json()
        self.validate_field_of_connect_with_many_popup_builder(body)
        lst_popup_builder_id = body.get(PopupBuilder.POPUP_BUILDER_USING, "")
        journey_builder_id = body.get(PopupBuilder.JOURNEY_BUILDER_USING, "")
        master_campaign_id = body.get(PopupBuilder.MASTER_CAMPAIGN_USING, "")

        list_bulk = list()
        for popup_builder_id in lst_popup_builder_id:
            self.validate_format_object_id(popup_builder_id)
            detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
            if not detail_popup_builder:
                MobioLogging().info("{} :: {} popup is not exists".format(self.class_name, func_name))
                continue

            is_active = detail_popup_builder.get(PopupBuilder.ACTIVE, PopupBuilder.NOT_ACTIVATED)
            if is_active == PopupBuilder.NOT_ACTIVATED:
                MobioLogging().info("{} :: {} popup is not active".format(self.class_name, func_name))
            data_update = {
                "$addToSet": {
                    PopupBuilder.JOURNEY_BUILDER_USING: journey_builder_id,
                    PopupBuilder.MASTER_CAMPAIGN_USING: master_campaign_id,
                }
            }

            search_options = {CommonParams.ID: ObjectId(popup_builder_id)}

            list_bulk.append(UpdateOne(search_options, data_update, upsert=True))
        if len(list_bulk) > 0:
            status_update = PopupBuilderModel().connect_with_many_popup_builder(list_bulk=list_bulk)
            MobioLogging().info("{} :: {} connect-with status: {}".format(self.class_name, func_name, status_update))

            if status_update:
                return
        raise CustomError

    def disconnect_with_popup_builder(self, popup_builder_id):
        """
        - Khi thực thể không sử dụng PU, gọi để bỏ thông tin của thực thể được lưu trong PU
        @param popup_builder_id:
        @return:
        """
        self.validate_format_object_id(popup_builder_id)
        func_name = "disconnect_with_popup_builder"
        merchant_id = PopupBuilderController.get_merchant_header()
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))

        is_active = detail_popup_builder.get(PopupBuilder.ACTIVE, PopupBuilder.NOT_ACTIVATED)
        if is_active == PopupBuilder.NOT_ACTIVATED:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_ACTIVE).get("message"))
        body = request.get_json()
        self.validate_field_of_connect_with_popup_builder(body)
        journey_builder_id = body.get(PopupBuilder.JOURNEY_BUILDER_USING, "")
        master_campaign_id = body.get(PopupBuilder.MASTER_CAMPAIGN_USING, "")
        journey_builder_using = detail_popup_builder.get(PopupBuilder.JOURNEY_BUILDER_USING, [])

        if journey_builder_id in journey_builder_using:
            journey_builder_using.remove(journey_builder_id)

        result = CallToJourneyBuilder().check_master_campaign_is_in_use(
            merchant_id, master_campaign_id, journey_builder_using
        )

        # check nếu journey_builder_using trống thì cho cả master_campaign_id và journey_builder_id
        if result and journey_builder_using:
            data_update = {
                "$pull": {
                    PopupBuilder.JOURNEY_BUILDER_USING: journey_builder_id,
                }
            }
        else:
            data_update = {
                "$pull": {
                    PopupBuilder.JOURNEY_BUILDER_USING: journey_builder_id,
                    PopupBuilder.MASTER_CAMPAIGN_USING: master_campaign_id,
                }
            }
        status_update = PopupBuilderModel().update_dictionary(popup_builder_id, data_update)
        MobioLogging().info("{} :: {} disconnect-with status: {}".format(self.class_name, func_name, status_update))

        if status_update:
            return
        raise CustomError

    def check_popup_is_using(self, popup_builder_id):
        """
        - Kiểm tra xem PU có đang được sử dụng không
        @param popup_builder_id:
        @return:
        """
        self.validate_format_object_id(popup_builder_id)
        merchant_id = PopupBuilderController.get_merchant_header()
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))
        result = False
        is_active = detail_popup_builder.get(PopupBuilder.ACTIVE, None)
        if is_active == PopupBuilder.ACTIVATED:
            master_campaign_using = detail_popup_builder.get(PopupBuilder.JOURNEY_BUILDER_USING, [])
            if len(master_campaign_using) > 0:
                result = True
        data = {PopupBuilder.IS_EXISTS: result}
        return {"data": data}

    def copy_popup_builder(self, popup_builder_id):
        """
        - Tạo ra một popup mới từ popup đã tồn tại
        @param popup_builder_id:
        @return:
        """
        self.validate_format_object_id(popup_builder_id)
        func_name = "copy_popup_builder"
        merchant_id = PopupBuilderController.get_merchant_header()
        body = request.get_json()
        self.validate_copy_popup_builder(body)

        new_popup_builder_id = generate_object_id()
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        if not detail_popup_builder:
            raise CustomError(self.lang.get(LANG.POPUP_BUILDER_NOT_EXIST).get("message"))

        if detail_popup_builder.get(CommonParams.SESSION) != body.get(CommonParams.SESSION, ""):
            raise CustomError(self.lang.get(LANG.SESSION_DISSIMILARITY).get("message"))

        title = body.get(PopupBuilder.TITLE, "")
        title_unique = PopupBuilderController.generate_title_unique(title)
        if title:
            is_exists = PopupBuilderModel().get_detail_popup_builder_by_title_unique(merchant_id, title_unique)
            if is_exists:
                raise CustomError(self.lang.get(LANG.NAME_POPUP_BUILDER_ALREADY_EXISTS_IN_SYSTEM).get("message"))
        title_code = utf8_to_ascii(title.lower())

        avatar_info = detail_popup_builder.get(PopupTemplate.AVATAR_INFO)
        new_file_path = PopupBuilderController.generate_new_file_image_from_file_path(avatar_info, new_popup_builder_id)
        if new_file_path:
            avatar_info = self.upload_image_with_file_path(merchant_id, popup_builder_id, new_file_path)
            if avatar_info:
                body.update({PopupBuilder.AVATAR_INFO: avatar_info})

        time_now = get_utcnow()
        account_id = MobioAdminSDK().get_value_from_token("id")

        body.update(
            {
                CommonParams.ID: ObjectId(new_popup_builder_id),
                PopupBuilder.POPUP_DRAFT_ID: None,
                PopupBuilder.TITLE_CODE: title_code,
                PopupBuilder.TITLE_UNIQUE: title_unique,
                PopupBuilder.ACTIVE: PopupBuilder.NOT_ACTIVATED,
                PopupBuilder.BODY_TEMPLATE: detail_popup_builder.get(PopupBuilder.BODY_TEMPLATE),
                PopupBuilder.BUILD_TEMPLATE: detail_popup_builder.get(PopupBuilder.BUILD_TEMPLATE),
                PopupBuilder.CURRENT_BUTTON: detail_popup_builder.get(PopupBuilder.CURRENT_BUTTON),
                PopupBuilder.CURRENT_FIELD: detail_popup_builder.get(PopupBuilder.CURRENT_FIELD),
                PopupBuilder.DYNAMIC: detail_popup_builder.get(PopupBuilder.DYNAMIC),
                PopupBuilder.SECOND_PAGE: detail_popup_builder.get(PopupBuilder.SECOND_PAGE),
                CommonParams.SESSION: generate_session(),
                CommonParams.MERCHANT_ID: merchant_id,
                CommonParams.CREATED_TIME: time_now,
                CommonParams.CREATED_BY: account_id,
                CommonParams.UPDATED_TIME: time_now,
                CommonParams.UPDATED_BY: account_id,
            }
        )

        new_popup_builder_id = str(PopupBuilderModel().insert_document(body).inserted_id)
        if not new_popup_builder_id:
            raise CustomError(self.lang.get(LANG.COPY_POPUP_BUILDER_ERROR).get("message"))
        MobioLogging().info("{} :: {} insert success: {}".format(self.class_name, func_name, new_popup_builder_id))
        data_response = PopupBuilderModel().get_detail_popup_builder(new_popup_builder_id, merchant_id)
        return {"data": self.json_encoder(data_response)}

    # FOR JOURNEY BUILDER
    def get_list_id_popup_builder(self):
        """
        - Trả về danh sách id của PU có field trong form
        @return:
        """
        merchant_id = PopupBuilderController.get_merchant_header()
        args = request.args
        search_options = {CommonParams.MERCHANT_ID: merchant_id}
        paging = PopupBuilderController.get_paging_from_args(args)
        is_search = args.get(CommonParams.SEARCH)
        if is_search:
            title_code = utf8_to_ascii(is_search)
            title_code = convert_data_for_search_regex(title_code.lower())
            search_options.update({PopupBuilder.TITLE_CODE: {"$regex": title_code}})
        active = args.get(PopupBuilder.ACTIVE)
        if active:
            search_options.update({PopupBuilder.ACTIVE: active})

        search_options.update(
            {"$and": [{PopupBuilder.CURRENT_FIELD: {"$exists": True}}, {PopupBuilder.CURRENT_FIELD: {"$ne": {}}}]}
        )

        results = PopupBuilderModel().get_list_id_popup_builder(search_options, paging)
        paging = PopupBuilderController.get_info_paging_for_response(results, paging)

        result = list()
        lst_id_popup_builder = results.get(CommonParams.LIST_DATA, [])
        for id_popup_builder in lst_id_popup_builder:
            convert = self.json_encoder(id_popup_builder)
            if convert.get("id", None):
                result.append(convert.get("id"))
        data = {CommonParams.IDS: result}
        return {"data": data, "paging": paging}

    def get_list_name_popup_builder(self):
        """
        - Trả về danh sách tên của PU có field form
        @return:
        """
        merchant_id = PopupBuilderController.get_merchant_header()
        args = request.args
        paging = PopupBuilderController.get_paging_from_args(args)
        search_options = {CommonParams.MERCHANT_ID: merchant_id}

        is_search = args.get(CommonParams.SEARCH)
        is_active = args.get(PopupBuilder.ACTIVE)
        body = request.get_json()
        self.validate_get_list_name_popup_builder(body)

        ids = body.get(CommonParams.IDS, [])
        if ids:
            lst_popup_builder_id = [ObjectId(popup_builder_id) for popup_builder_id in ids]
            search_options.update(
                {
                    CommonParams.ID: {"$in": lst_popup_builder_id},
                }
            )

        if is_search:
            title_code = utf8_to_ascii(is_search)
            title_code = convert_data_for_search_regex(title_code.lower())
            search_options.update({PopupBuilder.TITLE_CODE: {"$regex": title_code}})
        if is_active:
            search_options.update({PopupBuilder.ACTIVE: is_active})

        search_options.update(
            {"$and": [{PopupBuilder.CURRENT_FIELD: {"$exists": True}}, {PopupBuilder.CURRENT_FIELD: {"$ne": {}}}]}
        )

        results = PopupBuilderModel().get_list_name_popup_builder(search_options, paging)
        paging = PopupBuilderController.get_info_paging_for_response(results, paging)

        lst_name_popup_builder = results.get(CommonParams.LIST_DATA, [])
        lst_name_popup_builder = [self.json_encoder(item) for item in lst_name_popup_builder]
        return {"data": lst_name_popup_builder, "paging": paging}

    def check_license_export(self):
        merchant_id = MobioAdminSDK().get_value_from_token("merchant_id")

        # Check license
        check_license = License.check_allowed_feature_attribute(
            merchant_id=merchant_id,
            attribute=License.MODULE.POPUP_BUILDER_EXPORT_MOPAGE,
        )
        if not check_license.get("is_allowed"):
            error_msg = self.lang.get(LANG.LICENSE_NOT_ALLOWED)
            raise CustomError(error_msg["message"] % COMMON.CS_CONTACT, error_msg["code"])

        # Success

    def get_by_ids(self):
        merchant_id = PopupBuilderController.get_merchant_header()
        body = request.get_json()
        self.validate_get_list_popup_builder_by_ids(body)
        default_fields = [PopupBuilder.ID, PopupBuilder.TITLE]

        ids = body.get(CommonParams.IDS, [])

        if len(ids) >= PopupBuilder.MAX_IDS_REQUEST:
            raise CustomError(f"Max {PopupBuilder.MAX_IDS_REQUEST} ids per request")

        fields = body.get(CommonParams.FIELDS, []) + default_fields
        fields = list(set(fields))
        if any(field not in PopupBuilder.get_list_allow_get_by_ids() for field in fields):
            raise CustomError("Fields not allowed to access")

        list_popup_builder = []
        if ids:
            obj_fields_select = {field: 1 for field in fields}
            list_popup_builder_id = [ObjectId(popup_builder_id) for popup_builder_id in ids]

            list_popup_builder_raw = list(
                PopupBuilderModel().find(
                    {
                        CommonParams.MERCHANT_ID: merchant_id,
                        CommonParams.ID: {"$in": list_popup_builder_id},
                        PopupBuilder.ACTIVE: PopupBuilder.ACTIVATED,
                    },
                    obj_field_select=obj_fields_select,
                )
            )
            
            for popup_builder in list_popup_builder_raw:
                popup_builder[PopupBuilder.ID] = popup_builder[CommonParams.ID]
                del popup_builder[CommonParams.ID]
                list_popup_builder.append(self.json_encoder(popup_builder))

        return {"data": list_popup_builder}
