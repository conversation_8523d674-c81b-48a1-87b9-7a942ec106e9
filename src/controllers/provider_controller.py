import json
import re

from flask import request
from mobio.libs.logging import <PERSON><PERSON>Logging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.admin import MobioAdminSDK
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from src.common import CommonKeys, LogActionKeys, ProviderKeys, ProviderConfigKeys
from src.common.handle_headers import get_param_value_temp, validate_merchant_header
from src.common.json_encoder import JSONEncoder
from src.controllers import MarketPlaceBaseController
from src.internal_module.admin import GetInternalHost
from src.models.log_action import LogActionModel
from src.models.provider_config_model import ProviderConfigModel
from src.models.provider_model import ProviderModel


class ProviderController(MarketPlaceBaseController):

    def validate_provider(self, payload):
        rule_input = {
            ProviderKeys.PROVIDER: [Required, InstanceOf(str)],
            ProviderKeys.PROVIDER_LINK_IMAGE: [Required, InstanceOf(str)],
            ProviderKeys.PROVIDER_TYPE: [Required, InstanceOf(int)],
            ProviderKeys.CONNECT_CONFIG_INFO: [Required, InstanceOf(list), Length(2)],
            ProviderKeys.TYPE: [Required, InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_input, payload)

    def add_information_user_in_logs(self, logs, merchant_id):
        # account_ids = [log.get(CommonKeys.CREATED_BY) for log in logs]
        users_information = {}
        for log in logs:
            if log.get(ProviderConfigKeys.USERNAME):
                continue
            users_information[log.get(CommonKeys.ACCOUNT_ID)] = GetInternalHost.get_detail_user_information_by_account_id(
                merchant_id, log.get(CommonKeys.CREATED_BY)
            )
            log.update(users_information)

        return logs

    def upsert_provider(self):
        account_id = get_param_value_temp("id")
        merchant_id = "c9e1fafe-ac06-4fea-ad6e-4bf845acf151"
        image = request.files["file"]
        provider = request.form.get("provider")
        provider_type = int(request.form.get("provider_type"))
        config_type = request.form.get("type").split(",")
        connect_config_info = json.loads(request.form.get("connect_config_info"))
        service = request.form.get("service", "").strip().lower()

        image_info = MobioMediaSDK().upload_without_kafka(merchant_id=merchant_id, file_data=image, filename=provider)
        MobioLogging().debug("ProviderController :: upsert_provider :: image_info :: %s" % image_info)
        payload = {
            ProviderKeys.PROVIDER: provider,
            ProviderKeys.PROVIDER_LINK_IMAGE: image_info.get("url"),
            ProviderKeys.PROVIDER_TYPE: provider_type,
            ProviderKeys.CONNECT_CONFIG_INFO: connect_config_info,
            ProviderKeys.TYPE: config_type,
            ProviderKeys.SERVICE: service,
        }
        self.validate_provider(payload)

        provider_model: ProviderModel = ProviderModel()
        inserted_id = provider_model.upsert_provider(provider, account_id)
        if not inserted_id:
            raise CustomError("Provider upsert failed", 500)
        MobioLogging().info("ProviderController :: upsert_provider :: inserted_id :: %s" % inserted_id)
        return

    def get_providers(self):
        merchant_id = validate_merchant_header()
        lang = request.args.get("lang", "vi")
        service = request.args.get("service", "").strip().lower()

        provider_model: ProviderModel = ProviderModel()
        providers = provider_model.get_providers(service, lang)
        MobioLogging().debug("ProviderController :: get_providers :: providers :: %s" % providers)
        [self.json_encoder(provider) for provider in providers]
        return {"data": self.json_encoder(providers)}

    def get_log_provider_config(self, provider_config_id):
        merchant_id = validate_merchant_header()
        account_id = get_param_value_temp("id")

        config_type = request.args.get("type", "").strip().lower()
        order = int(request.args.get("order", -1))
        sort = request.args.get("sort", CommonKeys.CREATED_TIME)
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 10))

        log_action_model: LogActionModel = LogActionModel()

        log_query = {
            CommonKeys.MERCHANT_ID: merchant_id,
            LogActionKeys.TYPE: config_type,
            LogActionKeys.PROVIDER_CONFIG_ID: provider_config_id,
        }

        logs, paging = log_action_model.get_logs(log_query, order, sort, page, per_page)
        results = [self.json_encoder(log) for log in logs]
        return {"data": results, "paging": paging}
