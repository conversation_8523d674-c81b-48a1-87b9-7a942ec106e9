#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 25/12/2024
"""


import datetime

from flask import jsonify, render_template, request
from mobio.sdks.base.controllers import BaseController

from src.models.orchestration.pipeline_session_model import PipelineSessionModel
from src.models.reports.olap.dataflow.data_flow_model import DataFlowDialect


class CustomizeController(BaseController):

    @classmethod
    def _convert_datetime_to_response(cls, value):
        if value and isinstance(value, datetime.datetime):
            value = (value + datetime.timedelta(hours=7)).strftime("%Y-%m-%d %H:%M")
        return value

    def connector_live_debug(self):
        connector_id = request.form.get("connector_id", "")
        session_id = request.form.get("session_id", "")
        if not connector_id and not session_id:
            return render_template("index.html", requests=[], connector_id=connector_id, session_id=session_id)

        filter_data = {}
        if connector_id:
            filter_data["connector_id"] = int(connector_id)
        if session_id:
            filter_data["session_id"] = int(session_id)
        list_pipeline_by_connector = (
            PipelineSessionModel()
            .find(
                filter_data,
                {
                    "connector_id": 1,
                    "session_id": 1,
                    "start_time": 1,
                    "end_time": 1,
                    "consume_status": 1,
                    "process_status": 1,
                    "created_time": 1,
                    "updated_time": 1,
                },
            )
            .sort({"_id": -1})
        )
        results = []

        for pipeline_item in list_pipeline_by_connector:

            results.append(
                {
                    "connector_id": pipeline_item.get("connector_id"),
                    "session_id": pipeline_item.get("session_id"),
                    "start_time": self._convert_datetime_to_response(pipeline_item.get("start_time")),
                    "end_time": self._convert_datetime_to_response(pipeline_item.get("end_time")),
                    "created_time": self._convert_datetime_to_response(pipeline_item.get("created_time")),
                    "updated_time": self._convert_datetime_to_response(pipeline_item.get("updated_time")),
                    "consume_status": pipeline_item.get("consume_status"),
                    "process_status": pipeline_item.get("process_status"),
                }
            )
        return render_template("index.html", requests=results, connector_id=connector_id, session_id=session_id)

    def connector_live_debug_detail_session(self, session_id):
        session_id = int(session_id)

        session_logs = DataFlowDialect().get_data_log_by_session_id(session_id)
        results = []
        for item in session_logs:
            results.append(
                {
                    "session_id": str(item.session_id),
                    "message_id": str(item.message_id),
                    "created_time": self._convert_datetime_to_response(item.created_time),
                    "event_value": item.event_value,
                    "state": item.state,
                    "reason": item.reason,
                    "action": item.action,
                    "result": item.result,
                }
            )
        return jsonify(results)


if __name__ == "__main__":

    pass
