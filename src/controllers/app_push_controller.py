#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 11/06/2025
"""


from flask import request
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common.app_push_constant import AppPushConstant
from src.common.handle_headers import get_param_value_temp
from src.services.app_push_service import AppPushService


class AppPushController(BaseController):

    def get_list_applications_integrate_push_notification(self):
        """
        Get list applications integrate push notification
        Args:
            None
        Returns:
            list: List of applications integrate push notification
        """

        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")

        order_by = request.args.get("order_by", "updated_time")
        order_type = request.args.get("order_type", "desc")
        per_page = request.args.get("per_page", 10, type=int)
        after_token_this_request = request.args.get("after_token", None)

        name_search = request.args.get("name_search", None)

        data_result, paging = AppPushService().get_list_applications_integrate_push_notification(
            merchant_id, account_id, order_by, order_type, per_page, after_token_this_request, name_search
        )

        return {
            "data": data_result,
            "paging": paging,
        }

    def get_list_applications_integrate_push_notification_by_id(self, application_id):
        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")
        return {
            "data": AppPushService().get_detail_config_application_integrate_push_notification(
                merchant_id, application_id
            )
        }

    def _validate_create_application_integrate_push_notification(self, request_data):
        rule_validate = {
            AppPushConstant.FieldKeyBodyRequestCreateApplication.NAME: [Required, InstanceOf(str), Length(1)],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.OS: [
                Required,
                InstanceOf(str),
                Length(1),
                In(AppPushConstant.OS.get_all_attribute()),
            ],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.THUMBNAIL: [],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.THUMBNAIL_TYPE: [],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.INFO_UPLOAD_SCREEN: [],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.PROGRAMMING_LANGUAGE: [
                Required,
                InstanceOf(str),
                Length(1),
                In(AppPushConstant.ProgramLanguage.get_all_attribute()),
            ],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.CONFIG: [Required, InstanceOf(dict)],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_PUSH_NOTIFICATION_OMNI_CHANNEL: [
                Required,
                InstanceOf(str),
                Length(1),
                In(AppPushConstant.Status.get_all_attribute()),
            ],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_TRACKING_EVENT_IN_APP: [
                Required,
                InstanceOf(str),
                Length(1),
                In(AppPushConstant.Status.get_all_attribute()),
            ],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_PUSH_NOTIFICATION_ON_PAGE: [
                Required,
                InstanceOf(str),
                Length(1),
                In(AppPushConstant.Status.get_all_attribute()),
            ],
        }

        self.abort_if_validate_error(rule_validate, request_data)

        status_push_notification_omni_channel = request_data.get(
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_PUSH_NOTIFICATION_OMNI_CHANNEL
        )
        os = request_data.get(AppPushConstant.FieldKeyBodyRequestCreateApplication.OS)

        if status_push_notification_omni_channel == AppPushConstant.Status.ON:
            config = request_data.get(AppPushConstant.FieldKeyBodyRequestCreateApplication.CONFIG)
            if os == AppPushConstant.OS.ANDROID:
                rule_validate_config = {
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.SERVICE_ACCOUNT_JSON: [
                        Required,
                        InstanceOf(dict),
                        Length(1),
                    ],
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.PACKAGE_NAME: [
                        Required,
                        InstanceOf(str),
                        Length(1),
                    ],
                }
            elif os == AppPushConstant.OS.IOS:
                rule_validate_config = {
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.AUTHENTICATION_KEY_P8: [
                        Required,
                        InstanceOf(dict),
                        Length(1),
                    ],
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.TEAM_ID: [
                        Required,
                        InstanceOf(str),
                        Length(1),
                    ],
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.KEY_ID: [
                        Required,
                        InstanceOf(str),
                        Length(1),
                    ],
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.APP_BUNDLE_ID: [
                        Required,
                        InstanceOf(str),
                        Length(1),
                    ],
                }
            self.abort_if_validate_error(rule_validate_config, config)

    def create_application_integrate_push_notification(self):
        request_data = request.get_json()

        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")
        self._validate_create_application_integrate_push_notification(request_data)

        data_result = AppPushService().create_application_integrate_push_notification(
            merchant_id, account_id, request_data
        )

        return {
            "data": data_result,
        }

    def _validate_update_application_integrate_push_notification(self, request_data):
        pass

    def update_application_integrate_push_notification(self, application_id):
        request_data = request.get_json()

        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")
        self._validate_update_application_integrate_push_notification(request_data)

        detail_data = AppPushService().get_detail_config_application_integrate_push_notification(
            merchant_id, application_id
        )

        if not detail_data:
            return False, {}, "Application not found"

        # Không được change OS
        if request_data.get(AppPushConstant.FieldKeyBodyRequestCreateApplication.OS) and request_data.get(
            AppPushConstant.FieldKeyBodyRequestCreateApplication.OS
        ) != detail_data.get(AppPushConstant.FieldKeyBodyRequestCreateApplication.OS):
            return False, {}, "OS is not allowed to change"

        os = detail_data.get(AppPushConstant.FieldKeyBodyRequestCreateApplication.OS)
        request_data_config = request_data.get(AppPushConstant.FieldKeyBodyRequestCreateApplication.CONFIG)
        if request_data_config:
            if os == AppPushConstant.OS.ANDROID:
                rule_validate_config = {
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.SERVICE_ACCOUNT_JSON: [
                        Required,
                        InstanceOf(dict),
                        Length(1),
                    ],
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.PACKAGE_NAME: [
                        Required,
                        InstanceOf(str),
                        Length(1),
                    ],
                }
                self.abort_if_validate_error(rule_validate_config, request_data_config)
            elif os == AppPushConstant.OS.IOS:
                rule_validate_config = {
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.AUTHENTICATION_KEY_P8: [
                        Required,
                        InstanceOf(dict),
                        Length(1),
                    ],
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.TEAM_ID: [
                        Required,
                        InstanceOf(str),
                        Length(1),
                    ],
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.KEY_ID: [
                        Required,
                        InstanceOf(str),
                        Length(1),
                    ],
                    AppPushConstant.FieldKeyBodyRequestCreateApplication.Config.APP_BUNDLE_ID: [
                        Required,
                        InstanceOf(str),
                        Length(1),
                    ],
                }
                self.abort_if_validate_error(rule_validate_config, request_data_config)

        status_update, data_result, reason = AppPushService().update_application_integrate_push_notification(
            merchant_id, account_id, application_id, request_data, detail_data
        )

        if not status_update:
            raise CustomError(reason)

        return {"data": data_result}

    def _validate_change_status_application_integrate_push_notification(self, request_data):
        rule_validate = {
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_PUSH_NOTIFICATION_OMNI_CHANNEL: [
                InstanceOf(str),
                In(AppPushConstant.Status.get_all_attribute()),
            ],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_TRACKING_EVENT_IN_APP: [
                InstanceOf(str),
                In(AppPushConstant.Status.get_all_attribute()),
            ],
            AppPushConstant.FieldKeyBodyRequestCreateApplication.STATUS_PUSH_NOTIFICATION_ON_PAGE: [
                InstanceOf(str),
                In(AppPushConstant.Status.get_all_attribute()),
            ],
        }

        self.validate_optional_err(rule_validate, request_data)

    def change_status_application_integrate_push_notification(self, application_id):
        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")

        request_data = request.get_json()
        self._validate_change_status_application_integrate_push_notification(request_data)

        status_update, data_result, reason = AppPushService().change_status_application_integrate_push_notification(
            merchant_id, account_id, application_id, request_data
        )

        if not status_update:
            raise CustomError(reason)

        return {"data": data_result}

    def get_program_language_integrate_push_notification(self):
        request_args = request.args

        os = request_args.get("os")

        if os not in [AppPushConstant.OS.IOS, AppPushConstant.OS.ANDROID]:
            raise CustomError("OS is invalid")
        if os == AppPushConstant.OS.IOS:
            return {
                "data": [
                    {
                        "name": AppPushConstant.ProgramLanguage.SWIFT.capitalize(),
                        "code": AppPushConstant.ProgramLanguage.SWIFT,
                    },
                    {
                        "name": AppPushConstant.ProgramLanguage.OBJECTIVE_C.capitalize(),
                        "code": AppPushConstant.ProgramLanguage.OBJECTIVE_C,
                    },
                ]
            }
        if os == AppPushConstant.OS.ANDROID:
            return {
                "data": [
                    {
                        "name": AppPushConstant.ProgramLanguage.JAVA.capitalize(),
                        "code": AppPushConstant.ProgramLanguage.JAVA,
                    },
                    {
                        "name": AppPushConstant.ProgramLanguage.KOTLIN.capitalize(),
                        "code": AppPushConstant.ProgramLanguage.KOTLIN,
                    },
                ]
            }

    def get_list_applications_integrate_push_notification_by_ids(self):

        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")

        application_integration_ids = request.args.get("application_integration_ids", None)
        if not application_integration_ids:
            raise CustomError("Application integration ids is required")

        application_integration_ids = application_integration_ids.split(",")

        data_result = AppPushService().get_list_applications_integrate_push_notification_by_ids(
            merchant_id, account_id, application_integration_ids
        )

        return {
            "data": data_result,
        }

    def get_list_applications_integrate_push_notification_by_filter_tracking(self):
        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")

        type_push_tracking = request.args.get("type_push_tracking", None)

        type_push_tracking = type_push_tracking.split(",")
        if not all(item in AppPushConstant.TypePushTracking.get_all_attribute() for item in type_push_tracking):
            raise CustomError("Type push tracking is invalid")

        per_page = request.args.get("per_page", 10, type=int)
        after_token_this_request = request.args.get("after_token", None)

        data_result, paging = AppPushService().get_list_applications_integrate_push_notification_by_filter_tracking(
            merchant_id, account_id, type_push_tracking, per_page, after_token_this_request
        )

        return {
            "data": data_result,
            "paging": paging,
        }

    def get_action_history_integrate_push_notification(self, application_id):
        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")

        per_page = request.args.get("per_page", 10)
        after_token_this_request = request.args.get("after_token", None)

        data_result, paging = AppPushService().get_action_history_integrate_push_notification(
            merchant_id, account_id, application_id, per_page, after_token_this_request
        )

        return {
            "data": data_result,
            "paging": paging,
        }

    def upload_file(self):
        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")

        file = request.files.get("file")
        if not file:
            raise CustomError("File is required")

        type_file = request.form.get("type", None)
        if type_file not in [
            AppPushConstant.TypeFileConfig.THUMBNAIL,
            AppPushConstant.TypeFileConfig.AUTHENTICATION_APP,
            AppPushConstant.TypeFileConfig.SCREEN,
        ]:
            raise CustomError("Type file is invalid")

        data_result = AppPushService().upload_file(merchant_id, account_id, file, type_file)

        return {
            "data": data_result,
        }

    def get_template_import_screen(self):
        merchant_id = get_param_value_temp("merchant_id")
        account_id = get_param_value_temp("id")

        data_result = AppPushService().get_template_import_screen(merchant_id, account_id)

        return {
            "data": data_result,
        }
