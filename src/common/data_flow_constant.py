#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 27/03/2024
"""


from src.common.i_base_class import IBaseClass


class ConstantParamApi:
    class BodyDatabaseCheckConnection:
        SOURCE_KEY = "source_key"
        SOURCE_TYPE = "source_type"
        CONFIG_CONNECT = "config_connect"

        CONFIG_CONNECT_HOST = "host"
        CONFIG_CONNECT_PORT = "port"
        CONFIG_CONNECT_DATABASE_NAME = "database_name"
        CONFIG_CONNECT_DATABASE_USERNAME = "database_username"
        CONFIG_CONNECT_DATABASE_PASSWORD = "database_password"

        CONNECTOR_ID = "connector_id"

    class BodyWebhookCheckConnection:
        SOURCE_KEY = "source_key"
        SOURCE_TYPE = "source_type"
        CONFIG_CONNECT = "config_connect"

        CONFIG_APP_ID = "app_id"
        CONFIG_URL = "url"
        CONFIG_CONNECT_METHODS = "methods"
        CONFIG_CONNECT_PARAM_HEADERS = "param_headers"
        CONFIG_CONNECT_CONTENT_TYPE = "content_type"

    class BodyGoogleSheetConfigConnector:
        INTEGRATION_ACCOUNT_ID = "integration_account_id"
        TYPE_USE_SPREADSHEET = "type_use_spreadsheet"
        FOLDER_ID = "folder_id"
        SPREADSHEET_ID = "spreadsheet_id"
        SPREADSHEET_NAME = "spreadsheet_name"
        SHEET_ID = "sheet_id"
        SHEET_NAME = "sheet_name"

    class BodyApiCheckConnection:
        SOURCE_KEY = "source_key"
        SOURCE_TYPE = "source_type"
        CONFIG_CONNECT = "config_connect"

        CONFIG_APP_ID = "app_id"
        CONFIG_URL = "url"
        CONFIG_CONNECT_METHODS = "methods"
        CONFIG_CONNECT_PARAM_HEADERS = "param_headers"
        CONFIG_CONNECT_CONTENT_TYPE = "content_type"
        CONFIG_CONNECT_PARAM_QUERY = "param_query"

    class BodyCreateConnectors:
        NAME = "name"
        DESCRIPTION = "description"
        SOURCE_KEY = "source_key"
        SOURCE_TYPE = "source_type"
        CONFIG_CONNECT = "config_connect"

        DAT_TYPE = "data_type"

        DATABASE_CONFIG_CONNECT_HOST = "host"
        DATABASE_CONFIG_CONNECT_PORT = "port"
        DATABASE_CONFIG_CONNECT_DATABASE_NAME = "database_name"
        DATABASE_CONFIG_CONNECT_DATABASE_USERNAME = "database_username"
        DATABASE_CONFIG_CONNECT_DATABASE_PASSWORD = "database_password"

    class BodyGetDataSampleTables:
        TABLE_NAME = "table_name"
        SCHEMA = "schema"

    class BodyUpdateConnectors:
        OBJECT = "object"
        OBJECT_ATTRIBUTE = "object_attribute"

        IS_TRUST_SOURCE = "is_trust_source"
        CONFIG_MAPPING_DATA = "config_mapping_data"
        CONFIG_RULE_UNIFICATION = "config_rule_unification"
        CONFIG_SYNC_CALENDAR = "config_sync_calendar"
        LIST_FIELD_VERIFY = "list_field_verify"

        CONFIG_INFORMATION_OUT = "config_information_out"

        CONTACT_INFO = "contact_info"
        SCHEMA_JSON_UPLOAD = "schema_json_upload"

        AUTO_CONNECTION_CHECK_INTERVAL = "auto_connection_check_interval"
        MAX_NOTIFICATION_COUNT = "max_notification_count"
        SYNC_LIMIT = "sync_limit"
        SYNC_LIMIT_HTTP_CONNECTION_TIMEOUT = "http_connection_timeout"

        CONFIG_DATA_DEFAULT_OF_PRIMARY_OBJECT = "config_data_default_of_primary_object"


class ConstantSourceType:
    DATABASES = "databases"


class ConstantSourceKey:
    class DataTypeIn:
        POSTGRES = "postgres"
        MYSQL = "mysql"
        ORACLE = "oracle"
        DB2 = "db2"
        SQLSERVER = "sqlserver"

    class DataTypeOut:
        WEBHOOK = "webhook"


class ConstantStatusConnectionDatabases(IBaseClass):
    SUCCESS = "success"
    FAIL = "fail"


class ConstantStatusCheckConnection(IBaseClass):
    SUCCESS = "success"
    FAIL = "fail"


class ConstantTypeConfigurationGeneral:
    IP_WHITELIST = "ip_whitelist"
    OBJECT_HANDLED = "object_handled"
    HEADER_DEFAULT = "header_default"
    DATA_SAMPLE_REQUEST_API = "data_sample_request_api"


class ConstantParameterConnector:
    CONFIG_RULE_UNIFICATION = "config_rule_unification"
    IS_TRUST_SOURCE = "is_trust_source"
    LOG_CONNECTION_INFORMATION = "log_connection_information"
    CONFIG_SYNC_CALENDAR = "config_sync_calendar"
    IS_TYPE_SYNC_MANUALLY = "is_type_sync_manually"
    IS_FIELD_MAPPING_CONFIGURED = "is_field_mapping_configured"


class ConstantValueTypeMappingData(IBaseClass):
    FIXED = "fixed"
    RECORD = "record"


class ConstantActionMappingData(IBaseClass):
    ADD = "add"
    OVERWRITE = "overwrite"
    OVERWRITE_AND_IGNORE_VALUE_NULL = "overwrite_and_ignore_value_null"
    SEARCH_OR_INSERT = "search_or_insert"
    
    def get_action_mapping_data_single_line():
        return [
            ConstantActionMappingData.OVERWRITE,
            ConstantActionMappingData.OVERWRITE_AND_IGNORE_VALUE_NULL
        ]
        
class ConstantModeConfigSyncCalendar(IBaseClass):
    STREAMING = "streaming"
    SNAPSHOT = "snapshot"


class ConstantScheduleType(IBaseClass):
    MANUALLY = "manually"
    INTERVAL = "interval"


class ConstantScheduleConfigType(IBaseClass):

    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    HOUR = "hour"
    MINUTE = "minute"
    # YEAR = "year"

    class TypeSelectDayInMonth(IBaseClass):
        EXACT_DAY = "exact_day"
        FLEX_DAY = "flex_day"

        class ConstantValueDaySelectInMonth(IBaseClass):
            FIRST_OF_MONTH = "first_of_month"
            LAST_OF_MONTH = "last_of_month"

    class DayInWeek(IBaseClass):
        MON = "mon"
        TUE = "tue"
        WED = "wed"
        THU = "thu"
        FRI = "fri"
        SAT = "sat"
        SUN = "sun"


class ConstantActionSyncData(IBaseClass):
    ON = "on"
    OFF = "off"


class ConstantStatusConnect(IBaseClass):
    ON = "on"
    OFF = "off"
    FAILED = "failed"


class ConstantUnificationStructure(IBaseClass):
    # ROOT Structure
    RULE_ID = "id"
    SOURCE = "source"
    EDITABLE = "editable"
    DELETABLE = "deletable"
    DISABLE = "disable"
    STATUS = "status"
    SHOW = "show"

    # FILTER field
    UNIFICATION_VALUE = "unification_value"

    # Nested Structure
    LOGICAL_OPERATORS = "logical_operators"
    OPERATORS = "operators"
    PRIORITY = "priority"
    FIELDS = "fields"
    UNIQUE = "unique"
    IS_DEFAULT = "is_default"

    DEFAULT_CONSENT = "consent"


class ConstantUnificationSupportRule(IBaseClass):
    # Phần này khi profile thay đổi danh sách field đinh danh thì cũng cần cập nhập
    PROFILE_ID = "profile_id"
    SOCIAL_USER = "social_user"
    # SOCIAL_TYPE = "social_type"
    NAME = "name"
    PRIMARY_PHONE = "primary_phone"
    PRIMARY_EMAIL = "primary_email"
    PHONE_NUMBER = "phone_number"
    EMAIL = "email"
    CIF = "cif"
    DEVICE_ID = "device_id"
    CUSTOMER_ID = "customer_id"
    PROFILE_IDENTIFY = "profile_identify"
    INTERNAL_ID = "internal_id"
    SOURCE = "source"


class ConstantUnificationMatchRule(IBaseClass):
    MATCH_TYPE = "match_type"
    NORMALIZED_TYPE = "normalized_type"
    MATCH_VALUE = "match_value"


class ConstantUnificationMatchType(IBaseClass):
    EXACT = "exact"
    FUZZY = "fuzzy"
    EXACT_NORMALIZED = "exact_normalized"


class ConstantUnificationNormalizedType(IBaseClass):
    PHONE_NUMBER = "phone"
    EMAIL = "email"
    UUID = "uuid"
    INT = "int"
    FLOAT = "float"
    STRING = "string"


class ConstantConnectorStateCode(IBaseClass):
    INIT_CONNECTOR = "init_connector"
    UPDATE_CONNECTOR = "update_connector"
    CREATE_SYNC_ORCHESTRATION = "create_sync_orchestration"
    UPDATE_SYNC_ORCHESTRATION = "update_sync_orchestration"


class ConstantStatusSyncData(IBaseClass):
    RUNNING = "running"
    DONE = "done"
    STOPPED = "stopped"
    NOT_DATA_SYNC = "not_data_sync"
    SYNC_ERROR = "sync_error"
    FINISHED = "finished"
    STOP = "stop"
    ERROR = "error"
    INIT = "init"
    PREPARE = "prepare"
    PENDING = "pending"
    FAILED = "failed"
    
    PREPARE = "prepare"
    PENDING = "pending"


class ConstantTypeReport(IBaseClass):
    SNAPSHOT = "snapshot"
    STREAMING = "streaming"


class ConstantParamTimeReport(IBaseClass):
    class TimeUnit(IBaseClass):
        HOUR = "hour"
        MINUTE = "minute"
        DAY = "day"


class ConstantParamReportDataFlow(IBaseClass):
    class ParamQuery(IBaseClass):
        START_TIME = "start_time"
        END_TIME = "end_time"

        ORDER_BY = "order_by"
        SORT_BY = "sort_by"

    class ParamOrderBy(IBaseClass):
        ASC = 1
        DESC = -1


class ConstantDisplayTypeData(IBaseClass):
    SINGLE_LINE = "single_line"
    MULTI_LINE = "multi_line"
    DROPDOWN_SINGLE_LINE = "dropdown_single_line"
    DROPDOWN_MULTI_LINE = "dropdown_multi_line"
    RADIO = "radio"
    CHECKBOX = "checkbox"
    DATE_PICKER = "date_picker"
    TAGS = "tags"
    IMAGE = "image"
    FILES = "files"
    UPLOAD_FILE = "upload_file"
    SINGLE_DATE_UNIT = "single_date_unit"
    NESTED = "nested"
    DROPDOWN = "dropdown"
    MULTI_LINE_CUSTOM = "multi_line_custom"
    CALCULATION = "calculation"
    
    def get_display_type_array():
        return [
            ConstantDisplayTypeData.MULTI_LINE,
            ConstantDisplayTypeData.DROPDOWN_MULTI_LINE,
            ConstantDisplayTypeData.CHECKBOX,
            ConstantDisplayTypeData.TAGS
        ]
    
class ConstantTimeRunSchedule:
    TIME_REPORT_STATUS_SYNC_MINUTE = 15


class ConstantDataType(IBaseClass):
    DATA_IN = "data_in"
    DATA_OUT = "data_out"


class ConstantTypeContactInfo(IBaseClass):
    EMAIL = "email"
    ACCOUNT_ID = "account_id"


class ConstantContentType(IBaseClass):
    JSON = "application/json"


class ConstantMethod(IBaseClass):
    GET = "GET"
    POST = "POST"


class ConstantParamHeaderDefault(IBaseClass):
    X_MERCHANT_ID = "X-Merchant-ID"
    MOBIO_CONNECTOR_IDENTIFIER = "Mobio-Connector-Identifier"
    MOBIO_ACCESS_TOKEN = "Mobio-Access-Token"
    MOBIO_CONNECTOR_APPKEY = "Mobio-Connector-AppKey"


class ConstantTypeConfigApp(IBaseClass):
    DEFAULT = "default"
    DEFAULT_FORM = "default_form"
    USER_CREATE = "user_create"


class ConstantTypeUseSpreadsheet(IBaseClass):
    NEW_SPREADSHEET = "new_spreadsheet"
    EXISTING_SPREADSHEET = "existing_spreadsheet"


class ConstantColumnsShowMappingGoogleSheet:
    MAX_COLUMNS_SHOW_MAPPING_GOOGLE_SHEET = 27


class ConstantStatusConfigConnector(IBaseClass):
    ACTIVE = "active"
    DISABLED = "disabled"


class ConstantObjectHandle(IBaseClass):

    class Object(IBaseClass):
        PROFILES = "profiles"
        COMPANY = "company"
        SALE = "sale"
        TICKET = "ticket"

    class ObjectAttribute(IBaseClass):
        PROFILE_ATTRIBUTE = "profile"
        PROFILE_DYNAMIC_EVENT = "dynamic_event"
        PROFILE_PRODUCT_HOLING = "product_holding"


class ConstantVersion(IBaseClass):
    OLD = 1
    NEW = 2
    
class ConstantTableType(IBaseClass):
    TABLE = "table"
    VIEW = "view"
    
class SnapshotType(IBaseClass):
    FULL = "full"
    INCREMENTAL = "incremental"
    
class OptionSnapshot(IBaseClass):
    BEGINNING = "beginning"
    SELECTED = "selected"

class ConstantSyncStartTime(IBaseClass):
    START_NOW = "start_now"
    START_APPOINTMENT = "start_appointment"


if __name__ == "__main__":
    print(ConstantActionMappingData.get_action_mapping_data_single_line())
