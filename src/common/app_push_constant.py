#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 11/06/2025
"""


from src.common.i_base_class import IBaseClass


class TypeSetupPushNotification:
    WEB_PUSH = "webpush"
    APP_PUSH = "apppush"


class AppPushConstant:
    class OS(IBaseClass):
        IOS = "IOS"
        ANDROID = "ANDROID"

    class TypeFileConfig(IBaseClass):
        THUMBNAIL = "thumbnail"
        AUTHENTICATION_APP = "authentication_app"
        SCREEN = "screen"

    class ProgramLanguage(IBaseClass):
        JAVA = "JAVA"
        KOTLIN = "KOTLIN"
        SWIFT = "SWIFT"
        OBJECTIVE_C = "OBJECTIVE_C"

    class FieldKeyBodyRequestCreateApplication:
        NAME = "name"
        DESCRIPTION = "description"
        OS = "os"
        THUMBNAIL = "thumbnail"
        THUMBNAIL_TYPE = "thumbnail_type"
        PROGRAMMING_LANGUAGE = "programming_language"
        CONFIG = "config"
        STATUS_PUSH_NOTIFICATION_OMNI_CHANNEL = "status_push_notification_omni_channel"
        STATUS_TRACKING_EVENT_IN_APP = "status_tracking_event_in_app"
        STATUS_PUSH_NOTIFICATION_ON_PAGE = "status_push_notification_on_page"

        INFO_UPLOAD_SCREEN = "info_upload_screen"

        class Config:
            SERVICE_ACCOUNT_JSON = "service_account_json"
            PACKAGE_NAME = "package_name"

            AUTHENTICATION_KEY_P8 = "authentication_key_p8"
            TEAM_ID = "team_id"
            APP_ID = "app_id"
            KEY_ID = "key_id"
            APP_BUNDLE_ID = "app_bundle_id"

    class Status(IBaseClass):
        ON = "ON"
        OFF = "OFF"

    class Screen(IBaseClass):
        CODE = "code"
        NAME = "name"
        class ColumnName(IBaseClass):
            CODE = "Mã màn hình"
            NAME = "Mô tả màn hình"

    class TypeAction(IBaseClass):
        CREATE = "CREATE"
        UPDATE = "UPDATE"
        DELETE = "DELETE"

    class TypePushTracking(IBaseClass):
        PUSH_NOTIFICATION_OMNI_CHANNEL = "push_notification_omni_channel"
        TRACKING_EVENT_IN_APP = "tracking_event_in_app"
        PUSH_NOTIFICATION_ON_PAGE = "push_notification_on_page"
