#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os

from mobio.libs.caching import Lru<PERSON>ache, StoreType
from mobio.libs.monitor import Monitor
from mobio.sdks.notify import MobioNotifySDK

from configs import MarketPlaceApplicationConfig, RedisConfig

WORKING_DIR = MarketPlaceApplicationConfig.WORKING_DIR
APP_CONFIG_FILE_PATH = os.path.join(MarketPlaceApplicationConfig.CONFIG_DIR, "market_place.conf")
FILE_UPLOAD = WORKING_DIR + "/file_upload/"
os.makedirs(FILE_UPLOAD, exist_ok=True)
lru_redis_cache = LruCache(
    store_type=StoreType.REDIS,
    config_file_name=APP_CONFIG_FILE_PATH,
    redis_uri=RedisConfig.REDIS_URI,
    redis_cluster_uri=RedisConfig.REDIS_CLUSTER_URI,
    cache_prefix=RedisConfig.CACHE_PREFIX,
    redis_type=RedisConfig.REDIS_TYPE,
)
lru_redis_cache.accept_none(True)
monitor = Monitor()

mobio_notify_sdk = MobioNotifySDK().config(
    source="market-place"  # source module (nguồn gửi từ module nào) (Ex: 'sale', 'admin')
)


class Common:
    DATE_TIME_FMT_WITHOUT_SECOND_PARSE = "%Y-%m-%dT%H:%MZ"


class LANG:
    KEYS = "keys"
    DEFAULT = "default"
    LANG_ERROR = "lang_error"
    MERCHANT_NOT_EXIST = "merchant_not_exist"
    CUSTOM_ERROR = "custom_error"
    BAD_REQUEST = "bad_request"
    UNAUTHORIZED = "unauthorized"
    NOT_ALLOWED = "not_allowed"
    NOT_FOUND = "not_found"
    INTERNAL_SERVER_ERROR = "internal_server_error"
    VALIDATE_ERROR = "validate_error"
    WEBSITE_URL_NOT_FOUND = "website_url_not_found"
    REGISTER_TRACKING_LANDING_PAGE_ERROR = "register_tracking_landing_page_error"
    SCRIPT_NOT_FOUND = "script_not_found"
    INVALID_TYPE = "invalid_type"
    CONFIG_NOT_FOUND = "config_not_found"
    CONFIG_IDS_NOT_FOUND = "config_ids_not_found"
    WEBSITE_URL_EXIST = "website_url_exist"
    UPDATE_TRACKING_STATUS_ERROR = "update_tracking_status_error"
    NOT_PERMISSION_UPDATE_WEBSITE_URL_ERROR = "not_permission_update_website_url_error"
    FIELD_VERIFY_NOT_MAPPING_ONE = "field_verify_not_mapping_one"
    NOT_ON_SYNC_DATA = "not_on_sync_data"
    UPDATE_STATUS_CONNECT_FAILED = "update_status_connect_failed"
    DELETE_CONNECTOR_FAIL_BY_CONNECTOR_IN_RUNNING = "delete_connector_fail_by_connector_in_running"
    INFORMATION_CONNECT_CONFIG_FAIL = "information_connect_config_fail"
    CONFIGURATION_LIMIT = "configuration_limit"
    CONFIGURATION_EMAIL_LIMIT = "configuration_email_limit"
    SMS_CONFIG_EXISTS = "sms_config_exists"
    EMAIL_CONFIG_EXISTS = "email_config_exists"
    PROVIDER_NOT_FOUND = "provider_not_found"
    MULTIPLE_DOMAINS_REQUIRED_TO_REMOVE = "multiple_domains_required_to_remove"

    NEED_STOP_CONNECT_CHANGE_MODE = "need_stop_connect_change_mode"
    APP_USE_IN_CONNECTOR = "app_use_in_connector"
    APP_NAME_EXISTS = "app_name_exists"
    DOMAINS_NOT_FOUND = "domain_not_found"
    DOMAINS_DUPLICATE = "domain_duplicate"
    AWS_CONFIG_EXISTS = "aws_config_exists"
    UPSERT_ERROR = "upsert_error"
    MAX_DOMAIN_NOTIFICATION = "max_domain_notification"

    APP_NOT_EXISTS = "app_not_exists"
    APP_NOT_EXISTS_SET_APP_NEW = "app_not_exists_set_app_new"
    CONNECTOR_NAME_EXIST = "connector_name_exist_{}"
    DELETE_INTEGRATION_ACCOUNT_FAIL = "delete_integration_account_fail"

    SPREADSHEET_NOT_EXIST = "spreadsheet_not_exist"

    TABLE_NOT_FOUND_PRIMARY_KEY = "table_not_found_primary_key"
    
    NOT_ALLOWED_ADD_ACTION = "not_allowed_add_action"
    
    STREAMING_MUST_HAVE_PRIMARY_KEY = "streaming_must_have_primary_key"
    
    CANNOT_RETRIEVE_DATA_SAMPLE = "cannot_retrieve_data_sample"


class KeyHostService:
    DIFIENTY_SERVICE_HOST = "wp-web-push-api-services-host"
    ORCHESTRATION_SERVICE_HOST = "dataorchestration-api-service-host"
    ADMIN_SERVICE_HOST = "admin-app-api-service-host"
    JOURNEY_BUILDER_SERVICE_HOST = "journey-builder-app-api-service-host"
    MARKET_PLACE_SERVICE_HOST = "market-place-app-api-service-host"
    NM_SERVICE_HOST = "nm-app-api-service-host"
    DATA_OUT_SERVICE_HOST = "data-out-app-api-service-host"
    PUBLIC_HOST = "public_host"
    WF_SERVICE_HOST = "workflow-app-internal-service-host"


class SECTION:
    LOGGING = "logging_mode"
    LANG = "lang"


class DatetimeTypeKeys:
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    FORMAT = "%Y-%m-%dT%H:%M:%S.000Z"
    VALUES = [CREATED_TIME, UPDATED_TIME]


class ConstantTimeCaching:
    CACHING_CONFIG_TRACKING_CODE = 86400  # caching 1 ngày


class ConstantTypeThirdParty:
    WEBPUSH = "webpush"
    ACCEPT_VALUE = [WEBPUSH]


class ConstantThirdPartyConfigStatus:
    ENABLE = "ENABLE"
    DISABLE = "DISABLE"
    DELETED = "DELETED"
    NOT_EXIST = "NOT_EXIST"
    ACCEPT_VALUE = [ENABLE, DISABLE]


class ConstantStatusHandleSyncData:
    DONE = "done"


WORKING_DIR = str(os.environ.get("MARKET_PLACE_HOME"))


class CommonKeys:
    ID = "_id"
    MERCHANT_ID = "merchant_id"
    CREATED_BY = "created_by"
    UPDATED_BY = "updated_by"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"


class ServiceKeys:
    EMAIL = "email"
    SMS = "sms"


class ConfigTypeChoices:
    MESSAGE = "message"
    NOTIFICATION = "notification"


class ProviderKeys:
    ID = "_id"
    PROVIDER = "provider"
    PROVIDER_TYPE = "provider_type"
    PROVIDER_LINK_IMAGE = "provider_link_image"
    CONNECT_CONFIG_INFO = "connect_config_info"
    TYPE = "type"
    KEY_NAME = "key_name"
    DESCRIPTION = "description"
    SERVICE = "service"
    DECRYPT = "decrypt"
    NOTE = "note"
    HOST = "host"
    TOKEN = "token"
    KEYS_CONNECT_FOR_TYPE = "keys_connect_for_type"

    # key of provider ses
    PROVIDER_SES_TRANSACTION_TYPE = "provider_ses_transaction_type"
    PROVIDER_NAME = "provider_name"


class ProviderConfigKeys:
    ID = "_id"
    PROVIDER_ID = "provider_id"
    NAME = "name"
    TYPE = "type"
    STATUS = "status"
    LOWER_CASE_NAME = "lower_case_name"
    DESCRIPTION = "description"
    CONNECT_CONFIG_INFO = "connect_config_info"
    DOMAINS = "domains"

    # key of provider email service
    DOMAIN = "domain"
    DNS_RECORDS = "dns_records"
    RECORD_NAME = "name"
    RECORD_KEY = "key"
    RECORD_VALUE = "value"
    EMAILS = "emails"
    RETRY = "retry"

    # key of provider ses
    PROVIDER_SES_TRANSACTION_TYPE = "provider_ses_transaction_type"

    # key for schedule verify
    VERIFY_COUNT = "verify_count"
    VERIFY_TIME = "verify_time"
    VERIFY_BY = "verify_by"
    VERIFY_TIME_LOOP = "verify_time_loop"

    # key of connect_config_info
    AUTH_NAME = "auth_name"
    AUTH_PASS = "auth_pass"
    USERNAME = "username"
    PASSWORD = "password"
    API = "api"

    # field in sql
    AUTH_NAME = "auth_name"
    AUTH_PASS = "auth_pass"
    PROVIDER_TYPE = "provider_type"
    PROVIDER_API = "provider_api"
    AUTH_ATTACHMENT = "auth_attachment"
    OTHERS = "others"

    class ConfigInfoKeys:
        KEY = "key"
        KEY_NAME_VI = "key_name_vi"
        KEY_NAME_EN = "key_name_en"
        VALUE = "value"
        FIELD = "field"


class StatusChoice:
    ACTIVE = 1
    INACTIVE = 0
    WAIT_CONFIRM = 2


class PROVIDER:
    LINK_IMAGE = "LINK_IMAGE"
    TYPE = "type"
    NAME = "name"
    LOWER_CASE_NAME = "lower_case_name"


class TimeConfig:
    UTC = "UTC"


class LogActionKeys:
    ACTION_TYPE = "action_type"
    STATUS = "status"
    STATUS_DETAIL = "status_detail"
    ACTION = "action"
    TYPE = "type"
    PROVIDER_CONFIG_ID = "provider_config_id"


class ConstantStatusHandleSyncData:
    DONE = "done"


class DnsRecordKeys:
    RECORD_NAME = "name"
    RECORD_KEY = "key"
    RECORD_TYPE = "type"
    RECORD_VALUE = "value"
    VALUE_ENDPOINT = "value_endpoint"


class ConstantNM:
    class StatusConfig:
        ENABLE = 1
        DISABLED = 2


class ConstProcessRequestDataOut:

    class StatusProcess:
        WAIT_PROCESS = "wait_process"
        PROCESSING = "processing"
        DONE = "done"
        STOP_PROCESS = "stop_process"

        @classmethod
        def get_list_allow(cls):
            return [
                ConstProcessRequestDataOut.StatusProcess.WAIT_PROCESS,
                ConstProcessRequestDataOut.StatusProcess.PROCESSING,
                ConstProcessRequestDataOut.StatusProcess.DONE,
                ConstProcessRequestDataOut.StatusProcess.STOP_PROCESS,
            ]

        @classmethod
        def get_status_process_name(cls, value):
            name_mapping = {
                ConstProcessRequestDataOut.StatusProcess.WAIT_PROCESS: "Chờ xử lý",
                ConstProcessRequestDataOut.StatusProcess.PROCESSING: "Đang xử lý",
                ConstProcessRequestDataOut.StatusProcess.DONE: "Hoàn thành",
                ConstProcessRequestDataOut.StatusProcess.STOP_PROCESS: "Dừng xử lý",
            }
            return name_mapping.get(value, "--")

    class AreaCodes:
        
        MAPPING = { # đừng chửi em :P
            # ================= PROFILING  ================= #
            "PROFILING.PROFILE_LIST": {
                "module_name": "PROFILING",
                "area_code": "PROFILE_LIST",
                "area_name": "Danh sách Profile",
            },
            # ================= TICKET  ================= #
            "TICKET.TICKET_LIST": {
                "module_name": "TICKET",
                "area_code": "TICKET_LIST",
                "area_name": "Danh sách Ticket",
            },
            # ================= TASK  ================= #
            "TASK.TASK_LIST": {
                "module_name": "TASK",
                "area_code": "TASK_LIST",
                "area_name": "Danh sách Công việc",
            },
            # ================= SURVEY  ================= #
            "SURVEY.SURVEY_REPORT_DETAIL": {
                "module_name": "SURVEY",
                "area_code": "SURVEY_REPORT_DETAIL",
                "area_name": "Báo cáo chi tiết của Survey",
            },
            # ================= SALE  ================= #
            "SALE.DEAL_LIST": {
                "module_name": "SALE",
                "area_code": "DEAL_LIST",
                "area_name": "Danh sách Cơ hội bán",
            },
            "SALE.DEAL_BANK": {
                "module_name": "SALE",
                "area_code": "DEAL_BANK",
                "area_name": "Báo cáo bán hàng",
            },
            "SALE_MEMO.SALE_MEMO_REPORT": {
                "module_name": "SALE_MEMO",
                "area_code": "SALE_MEMO_REPORT",
                "area_name": "Báo cáo Sale Memo",
            },
            "KPI_MANAGEMENT.KPI_MANAGEMENT_REPORT": {
                "module_name": "KPI_MANAGEMENT",
                "area_code": "KPI_MANAGEMENT_REPORT",
                "area_name": "Báo cáo KPI",
            },
        }


class ConstSourceKey:
    MS_EXCEL = "microsoft_excel"


class ProviderType:
    SES = 203
    MOBIO_MAILER = 212
